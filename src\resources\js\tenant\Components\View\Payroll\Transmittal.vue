<template>
    <div class="content-wrapper">
        <app-page-top-section :title="$t('transmittal')">
            <app-default-button
                v-if="$can('export_transmittal')"
                btn-class="btn btn-success mr-1"
                :title="$t('export_transmittal')"
                @click="exportTransmittal()"
            />
            <app-default-button
                v-if="$can('generate_transmittal')"
                btn-class="btn btn-primary"
                :title="$t('generate_transmittal')"
                @click="generateTransmittal()"
            />
        </app-page-top-section>

        <div class="card card-with-shadow border-0" style="min-height: 400px;">
            <div class="card-header d-flex align-items-center justify-content-between primary-card-color p-primary mb-primary">
                <app-month-calendar/>
                <app-period-calendar :filters="periodFilters" :initial-filter-value="'lastMonth'"/>
            </div>

            <app-table
                id="transmittal-table"
                :options="tableOptions"
                @action="triggerActions"
            />
        </div>

        <app-confirmation-modal
            v-if="confirmationModalActive"
            :loading="loading"
            :message="promptMessage"
            :modal-class="promptClass"
            :icon="promptIcon"
            modal-id="app-confirmation-modal"
            @confirmed="confirmed"
            @cancelled="confirmationModalActive = false"
            :self-close="false"
        />
    </div>
</template>

<script>
import {axiosGet, axiosPost, urlGenerator} from "../../../../common/Helper/AxiosHelper";
import {
    TRANSMITTALS,
    TRANSMITTALS_GENERATE,
    TRANSMITTALS_SUMMARY,
    TRANSMITTALS_EXPORT_PDF,
    TRANSMITTALS_EXPORT_EXCEL
} from "../../../Config/ApiUrl";

export default {
    name: "Transmittal",
    data() {
        return {
            urlGenerator,
            confirmationModalActive: false,
            promptClass: '',
            promptIcon: '',
            promptMessage: '',
            loading: false,
            actionType: '',
            summaryData: {},
            periodFilters: [
                {
                    name: this.$t('last_month'),
                    value: 'lastMonth'
                },
                {
                    name: this.$t('this_month'),
                    value: 'thisMonth'
                },
                {
                    name: this.$t('last_week'),
                    value: 'lastWeek'
                },
                {
                    name: this.$t('this_week'),
                    value: 'thisWeek'
                }
            ],
            tableOptions: {
                name: this.$t('transmittal'),
                url: TRANSMITTALS,
                showHeader: true,
                enableRowSelect: false,
                showCount: true,
                showClearFilter: true,
                showFilter: true,
                showSearch: true,
                tablePaddingClass: "px-0",
                tableShadow: false,
                columns: [
                    {
                        title: this.$t('employee'),
                        type: 'custom-html',
                        key: 'employee',
                        isVisible: true,
                        modifier: (val, row) => {
                            return `<div class="media align-items-center">
                                        <div class="media-body">
                                            <p class="text-main-color mb-0">${val.full_name}</p>
                                            <p class="text-muted font-size-90 mb-0">${val.email}</p>
                                        </div>
                                    </div>`;
                        }
                    },
                    {
                        title: this.$t('period'),
                        type: 'text',
                        key: 'period',
                        isVisible: true
                    },
                    {
                        title: this.$t('amount'),
                        type: 'custom-html',
                        key: 'amount',
                        isVisible: true,
                        modifier: (val) => {
                            return `<span class="text-success font-weight-bold">${val}</span>`;
                        }
                    },
                    {
                        title: this.$t('status'),
                        type: 'custom-html',
                        key: 'status',
                        isVisible: true,
                        modifier: (val) => {
                            return `<span class="badge badge-pill badge-${val.class}">${val.translated_name}</span>`;
                        }
                    }
                ],
                filters: [],
                paginationType: "pagination",
                responsive: true,
                rowLimit: 10,
                showAction: true,
                orderBy: 'desc',
                actionType: "dropdown",
                actions: [
                    {
                        title: this.$t('view'),
                        actionName: 'view',
                        modifier: () => this.$can('view_transmittal')
                    },
                    {
                        title: this.$t('download'),
                        actionName: 'download',
                        modifier: () => this.$can('download_transmittal')
                    }
                ]
            }
        }
    },
    created() {
        this.loadSummaryData();
    },
    methods: {
        triggerActions(row, action) {
            if (action.actionName === 'view') {
                this.viewTransmittal(row);
            } else if (action.actionName === 'download') {
                this.downloadTransmittal(row);
            }
        },
        viewTransmittal(row) {
            axiosGet(`${TRANSMITTALS}/${row.id}`).then(response => {
                if (response.data.status) {
                    // Show transmittal details modal or navigate to details page
                    this.$toastr.s('Transmittal details loaded');
                }
            }).catch(error => {
                this.$toastr.e('Failed to load transmittal details');
            });
        },
        downloadTransmittal(row) {
            this.loading = true;
            axiosPost(TRANSMITTALS_EXPORT_PDF, {
                transmittal_ids: [row.id]
            }, {
                responseType: 'blob'
            }).then(response => {
                const url = window.URL.createObjectURL(new Blob([response.data]));
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', `transmittal-${row.reference_number}.pdf`);
                document.body.appendChild(link);
                link.click();
                link.remove();
                this.$toastr.s('Transmittal downloaded successfully');
            }).catch(error => {
                this.$toastr.e('Failed to download transmittal');
            }).finally(() => {
                this.loading = false;
            });
        },
        generateTransmittal() {
            this.actionType = 'generate';
            this.confirmationModalActive = true;
            this.promptIcon = 'file-plus';
            this.promptClass = 'primary';
            this.promptMessage = this.$t('are_you_sure_to_generate_transmittal');
        },
        exportTransmittal() {
            this.actionType = 'export';
            this.confirmationModalActive = true;
            this.promptIcon = 'download';
            this.promptClass = 'success';
            this.promptMessage = this.$t('are_you_sure_to_export_transmittal');
        },
        confirmed() {
            if (this.actionType === 'generate') {
                this.performGenerate();
            } else if (this.actionType === 'export') {
                this.performExport();
            }
        },
        performGenerate() {
            this.loading = true;
            const payload = {
                period_start: this.getPeriodStart(),
                period_end: this.getPeriodEnd()
            };

            axiosPost(TRANSMITTALS_GENERATE, payload).then(response => {
                if (response.data.status) {
                    this.$toastr.s(response.data.message);
                    this.$hub.$emit('reload-transmittal-table');
                    this.loadSummaryData();
                } else {
                    this.$toastr.e(response.data.message);
                }
            }).catch(error => {
                this.$toastr.e('Failed to generate transmittals');
            }).finally(() => {
                this.loading = false;
                this.confirmationModalActive = false;
            });
        },
        performExport() {
            this.loading = true;
            axiosPost(TRANSMITTALS_EXPORT_EXCEL, {}, {
                responseType: 'blob'
            }).then(response => {
                const url = window.URL.createObjectURL(new Blob([response.data]));
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', `transmittals-${new Date().toISOString().split('T')[0]}.xlsx`);
                document.body.appendChild(link);
                link.click();
                link.remove();
                this.$toastr.s('Transmittals exported successfully');
            }).catch(error => {
                this.$toastr.e('Failed to export transmittals');
            }).finally(() => {
                this.loading = false;
                this.confirmationModalActive = false;
            });
        },
        loadSummaryData() {
            axiosGet(TRANSMITTALS_SUMMARY).then(response => {
                if (response.data.status) {
                    this.summaryData = response.data.data;
                }
            }).catch(error => {
                console.error('Failed to load summary data:', error);
            });
        },
        getPeriodStart() {
            // Get start date based on current period filter
            const now = new Date();
            const year = now.getFullYear();
            const month = now.getMonth();
            return new Date(year, month, 1).toISOString().split('T')[0];
        },
        getPeriodEnd() {
            // Get end date based on current period filter
            const now = new Date();
            const year = now.getFullYear();
            const month = now.getMonth();
            return new Date(year, month + 1, 0).toISOString().split('T')[0];
        }
    }
}
</script>

<style scoped>
</style>
