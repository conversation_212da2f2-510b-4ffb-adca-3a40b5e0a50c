<template>
    <div class="content-wrapper">
        <app-page-top-section :title="$t('transmittal')"/>

        <div class="card border-0">
            <div class="card-header d-flex align-items-center justify-content-between primary-card-color">
                <h5 class="card-title mb-0">{{ $t('transmittal_management') }}</h5>
                <div class="btn-group">
                    <button
                        type="button"
                        class="btn btn-primary"
                        @click="openCreateModal"
                        v-if="$can('create_transmittal')"
                    >
                        <i class="fas fa-plus"></i>
                        {{ $t('add_transmittal') }}
                    </button>
                </div>
            </div>

            <div class="card-body">
                <div class="row">
                    <div class="col-12">
                        <app-table
                            :id="tableId"
                            :options="options"
                            @action="triggerActions"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- Create/Edit Modal -->
        <app-modal
            modal-id="transmittal-modal"
            modal-size="large"
            :modal-title="isEdit ? $t('edit_transmittal') : $t('add_transmittal')"
            @close-modal="closeModal"
        >
            <transmittal-form
                :selected-url="selectedUrl"
                :is-edit="isEdit"
                @close-modal="closeModal"
                @reload-table="reloadTable"
            />
        </app-modal>
    </div>
</template>

<script>
import {TRANSMITTALS} from "../../../Config/ApiUrl";
import TransmittalForm from "./TransmittalForm";

export default {
    name: "Transmittal",
    components: {
        TransmittalForm
    },
    data() {
        return {
            tableId: 'transmittal-table',
            selectedUrl: '',
            isEdit: false,
            options: {
                name: this.$t('transmittal'),
                url: TRANSMITTALS,
                showHeader: true,
                columns: [
                    {
                        title: this.$t('id'),
                        type: 'text',
                        key: 'id',
                        isVisible: true
                    },
                    {
                        title: this.$t('reference_number'),
                        type: 'text',
                        key: 'reference_number',
                        isVisible: true
                    },
                    {
                        title: this.$t('department'),
                        type: 'text',
                        key: 'department',
                        isVisible: true
                    },
                    {
                        title: this.$t('total_employees'),
                        type: 'text',
                        key: 'total_employees',
                        isVisible: true
                    },
                    {
                        title: this.$t('total_amount'),
                        type: 'text',
                        key: 'total_amount',
                        isVisible: true
                    },
                    {
                        title: this.$t('created_at'),
                        type: 'text',
                        key: 'created_at',
                        isVisible: true
                    },
                    {
                        title: this.$t('actions'),
                        type: 'action',
                        key: 'actions',
                        isVisible: true
                    }
                ],
                actionType: "dropdown",
                actions: [
                    {
                        title: this.$t('edit'),
                        icon: 'fas fa-edit',
                        type: 'edit',
                        modifier: 'primary'
                    },
                    {
                        title: this.$t('view_pdf'),
                        icon: 'fas fa-eye',
                        type: 'view-pdf',
                        modifier: 'info'
                    },
                    {
                        title: this.$t('download_pdf'),
                        icon: 'fas fa-download',
                        type: 'download-pdf',
                        modifier: 'danger'
                    },
                    {
                        title: this.$t('download_csv'),
                        icon: 'fas fa-file-csv',
                        type: 'download-csv',
                        modifier: 'warning'
                    },
                    {
                        title: this.$t('delete'),
                        icon: 'fas fa-trash',
                        type: 'delete',
                        modifier: 'danger'
                    }
                ],
                filters: [],
                paginationType: "pagination",
                responsive: true,
                rowLimit: 10,
                showAction: true,
                orderBy: 'desc',
                actionType: "default",
                actions: [
                    {
                        title: this.$t('edit'),
                        icon: 'edit',
                        type: 'modal',
                        component: 'app-transmittal-modal',
                        modalId: 'transmittal-modal',
                        url: TRANSMITTALS,
                        name: 'edit',
                        modifier: 'btn-outline-dark'
                    },
                    {
                        title: this.$t('delete'),
                        icon: 'trash-2',
                        type: 'modal',
                        component: 'app-confirmation-modal',
                        modalId: 'app-confirmation-modal',
                        url: TRANSMITTALS,
                        name: 'delete',
                        modifier: 'btn-outline-danger'
                    }
                ]
            }
        }
    },
    methods: {
        openCreateModal() {
            this.isEdit = false;
            this.selectedUrl = '';
            $('#transmittal-modal').modal('show');
        },

        closeModal() {
            this.isEdit = false;
            this.selectedUrl = '';
            $('#transmittal-modal').modal('hide');
        },

        reloadTable() {
            this.$hub.$emit('reload-' + this.tableId);
        },

        triggerActions(row, action, active) {
            if (action.type === 'edit') {
                this.isEdit = true;
                this.selectedUrl = `${TRANSMITTALS}/${row.id}`;
                $('#transmittal-modal').modal('show');
            } else if (action.type === 'view-pdf') {
                this.viewPdf(row.id);
            } else if (action.type === 'download-pdf') {
                this.downloadPdf(row.id);
            } else if (action.type === 'download-csv') {
                this.downloadCsv(row.id);
            } else if (action.type === 'delete') {
                this.deleteTransmittal(row.id);
            }
        },

        viewPdf(transmittalId) {
            const url = `${TRANSMITTALS}/${transmittalId}/export-pdf`;
            window.open(url, '_blank');
        },

        downloadPdf(transmittalId) {
            this.$toastr.i(this.$t('preparing_download'));
            const url = `${TRANSMITTALS}/${transmittalId}/export-pdf?download=1`;
            const link = document.createElement('a');
            link.href = url;
            link.download = `transmittal-${transmittalId}-${new Date().toISOString().split('T')[0]}.pdf`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Show success message after a short delay
            setTimeout(() => {
                this.$toastr.s(this.$t('download_started'));
            }, 1000);
        },

        downloadCsv(transmittalId) {
            this.$toastr.i(this.$t('preparing_download'));
            const url = `${TRANSMITTALS}/${transmittalId}/export-csv`;
            const link = document.createElement('a');
            link.href = url;
            link.download = `transmittal-${transmittalId}-${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Show success message after a short delay
            setTimeout(() => {
                this.$toastr.s(this.$t('download_started'));
            }, 1000);
        },

        deleteTransmittal(id) {
            this.$confirm({
                title: this.$t('are_you_sure'),
                message: this.$t('you_will_not_be_able_to_revert_this'),
                button: {
                    no: this.$t('cancel'),
                    yes: this.$t('delete')
                },
                callback: confirm => {
                    if (confirm) {
                        this.axiosDelete(`${TRANSMITTALS}/${id}`)
                            .then(response => {
                                this.$toastr.s(response.data.message);
                                this.reloadTable();
                            })
                            .catch(error => {
                                this.$toastr.e(error.response.data.message);
                            });
                    }
                }
            });
        }
    }
}
</script>

<style scoped>
.primary-card-color {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}
</style>
