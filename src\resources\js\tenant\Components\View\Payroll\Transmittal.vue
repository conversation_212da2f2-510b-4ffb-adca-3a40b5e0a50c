<template>
    <div class="content-wrapper">
        <app-page-top-section :title="$t('transmittal')">
            <app-default-button
                v-if="$can('export_transmittal')"
                btn-class="btn btn-success mr-1"
                :title="$t('export_transmittal')"
                @click="exportTransmittal()"
            />
            <app-default-button
                v-if="$can('generate_transmittal')"
                btn-class="btn btn-primary"
                :title="$t('generate_transmittal')"
                @click="generateTransmittal()"
            />
        </app-page-top-section>

        <div class="card card-with-shadow border-0" style="min-height: 400px;">
            <div class="card-header d-flex align-items-center justify-content-between primary-card-color p-primary mb-primary">
                <app-month-calendar/>
                <app-period-calendar :filters="periodFilters" :initial-filter-value="'lastMonth'"/>
            </div>

            <app-table
                id="transmittal-table"
                :options="tableOptions"
                @action="triggerActions"
            />
        </div>

        <app-confirmation-modal
            v-if="confirmationModalActive"
            :loading="loading"
            :message="promptMessage"
            :modal-class="promptClass"
            :icon="promptIcon"
            modal-id="app-confirmation-modal"
            @confirmed="confirmed"
            @cancelled="confirmationModalActive = false"
            :self-close="false"
        />
    </div>
</template>

<script>
import {axiosGet, urlGenerator} from "../../../../common/Helper/AxiosHelper";

export default {
    name: "Transmittal",
    data() {
        return {
            urlGenerator,
            confirmationModalActive: false,
            promptClass: '',
            promptIcon: '',
            promptMessage: '',
            loading: false,
            periodFilters: [
                {
                    name: this.$t('last_month'),
                    value: 'lastMonth'
                },
                {
                    name: this.$t('this_month'),
                    value: 'thisMonth'
                },
                {
                    name: this.$t('last_week'),
                    value: 'lastWeek'
                },
                {
                    name: this.$t('this_week'),
                    value: 'thisWeek'
                }
            ],
            tableOptions: {
                name: this.$t('transmittal'),
                url: '',
                showHeader: true,
                enableRowSelect: false,
                showCount: true,
                showClearFilter: true,
                showFilter: true,
                showSearch: true,
                tablePaddingClass: "px-0",
                tableShadow: false,
                columns: [
                    {
                        title: this.$t('employee'),
                        type: 'custom-html',
                        key: 'employee',
                        isVisible: true,
                        modifier: (val, row) => {
                            return `<div class="media align-items-center">
                                        <div class="media-body">
                                            <p class="text-main-color mb-0">${val.full_name}</p>
                                            <p class="text-muted font-size-90 mb-0">${val.email}</p>
                                        </div>
                                    </div>`;
                        }
                    },
                    {
                        title: this.$t('period'),
                        type: 'text',
                        key: 'period',
                        isVisible: true
                    },
                    {
                        title: this.$t('amount'),
                        type: 'custom-html',
                        key: 'amount',
                        isVisible: true,
                        modifier: (val) => {
                            return `<span class="text-success font-weight-bold">${val}</span>`;
                        }
                    },
                    {
                        title: this.$t('status'),
                        type: 'custom-html',
                        key: 'status',
                        isVisible: true,
                        modifier: (val) => {
                            return `<span class="badge badge-pill badge-${val.class}">${val.translated_name}</span>`;
                        }
                    }
                ],
                filters: [],
                paginationType: "pagination",
                responsive: true,
                rowLimit: 10,
                showAction: true,
                orderBy: 'desc',
                actionType: "dropdown",
                actions: [
                    {
                        title: this.$t('view'),
                        actionName: 'view',
                        modifier: () => this.$can('view_transmittal')
                    },
                    {
                        title: this.$t('download'),
                        actionName: 'download',
                        modifier: () => this.$can('download_transmittal')
                    }
                ]
            }
        }
    },
    methods: {
        triggerActions(row, action) {
            if (action.actionName === 'view') {
                this.viewTransmittal(row);
            } else if (action.actionName === 'download') {
                this.downloadTransmittal(row);
            }
        },
        viewTransmittal(row) {
            // Implementation for viewing transmittal
            console.log('View transmittal:', row);
        },
        downloadTransmittal(row) {
            // Implementation for downloading transmittal
            console.log('Download transmittal:', row);
        },
        generateTransmittal() {
            this.confirmationModalActive = true;
            this.promptIcon = 'file-plus';
            this.promptClass = 'primary';
            this.promptMessage = this.$t('are_you_sure_to_generate_transmittal');
        },
        exportTransmittal() {
            this.confirmationModalActive = true;
            this.promptIcon = 'download';
            this.promptClass = 'success';
            this.promptMessage = this.$t('are_you_sure_to_export_transmittal');
        },
        confirmed() {
            // Implementation for confirmed actions
            this.loading = true;
            setTimeout(() => {
                this.loading = false;
                this.confirmationModalActive = false;
                this.$toastr.s(this.$t('operation_completed_successfully'));
            }, 2000);
        }
    }
}
</script>

<style scoped>
</style>
