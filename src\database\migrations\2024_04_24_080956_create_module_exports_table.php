<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('module_exports', function (Blueprint $table) {
            $table->id();
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('status_id')->constrained();
            $table->text('requested_modules')->nullable();
            $table->text('completed_modules')->nullable();
            $table->timestamp('requested_on')->nullable();
            $table->timestamp('completed_on')->nullable();
            $table->string('path')->nullable();
            $table->text('data')->nullable();
            $table->unsignedBigInteger('tenant_id')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('module_exports');
    }
};
