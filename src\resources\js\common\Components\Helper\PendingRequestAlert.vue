<template>
    <div class="text-center py-5">
      <div class="pending-request-container">
        <i class="fas fa-clock text-warning mb-3" style="font-size: 3rem;"></i>
        <h4 class="mb-3">{{ title }}</h4>
        <p class="text-muted mb-4" v-html="message"></p>
        
        <div class="alert alert-info" v-if="showDetails">
          <div class="text-left">
            <div v-if="pendingRequest?.plan_name">
              <strong>Requested Plan:</strong> {{ pendingRequest.plan_name }}
            </div>
            <div v-if="pendingRequest?.type">
              <strong>Request Type:</strong> {{ capitalizeFirst(pendingRequest.type) }}
            </div>
            <div v-if="pendingRequest?.requested_at">
              <strong>Requested Date:</strong> {{ formatDate(pendingRequest.requested_at) }}
            </div>
            <!-- Additional custom fields -->
            <div v-for="(value, key) in additionalDetails" :key="key">
              <strong>{{ capitalizeFirst(key.replace('_', ' ')) }}:</strong> {{ value }}
            </div>
          </div>
        </div>
        
        <p class="text-muted small" v-if="footerMessage">
          {{ footerMessage }}
        </p>
        
        <!-- Optional action buttons -->
        <div v-if="showActions" class="mt-3">
          <button 
            v-if="showCancelButton" 
            @click="$emit('cancel-request')"
            class="btn btn-outline-danger btn-sm mr-2"
          >
            <i class="fas fa-times mr-1"></i>
            Cancel Request
          </button>
          <button 
            v-if="showRefreshButton" 
            @click="$emit('refresh')"
            class="btn btn-outline-primary btn-sm"
          >
            <i class="fas fa-sync-alt mr-1"></i>
            Refresh Status
          </button>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    name: "PendingRequest",
    props: {
      // Main content props
      title: {
        type: String,
        default: "You have a pending request"
      },
      message: {
        type: String,
        default: ""
      },
      footerMessage: {
        type: String,
        default: "Our team will review your request and contact you shortly."
      },
      
      // Pending request data
      pendingRequest: {
        type: Object,
        default: null
      },
      
      // Additional custom details to display
      additionalDetails: {
        type: Object,
        default: () => ({})
      },
      
      // Display options
      showDetails: {
        type: Boolean,
        default: true
      },
      showActions: {
        type: Boolean,
        default: false
      },
      showCancelButton: {
        type: Boolean,
        default: false
      },
      showRefreshButton: {
        type: Boolean,
        default: false
      },
      
      // Custom styling
      iconClass: {
        type: String,
        default: "fas fa-clock text-warning"
      },
      alertClass: {
        type: String,
        default: "alert-info"
      }
    },
    computed: {
      computedMessage() {
        if (this.message) return this.message;
        
        if (this.pendingRequest?.plan_name) {
          return `You have already requested to upgrade to <strong>${this.pendingRequest.plan_name}</strong>. Please wait for admin approval.`;
        }
        
        return "You have a pending request. Please wait for admin approval.";
      }
    },
    methods: {
      formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      },
      capitalizeFirst(str) {
        if (!str) return '';
        return str.charAt(0).toUpperCase() + str.slice(1);
      }
    }
  };
  </script>
  
  <style scoped>
  .pending-request-container {
    max-width: 500px;
    margin: 0 auto;
    padding: 2rem;
  }
  
  .alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
  }
  
  .alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
  }
  
  .alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
  }
  </style>