<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Plans extends Model
{
    use HasFactory;

    protected $fillable = [
        'plan_name',
        'plan_type',
        'plan_description',
        'features',
        'employee_limit',
        'price',
    ];

    /**
     * Cast features JSON to array.
     */
    protected $casts = [
        'features' => 'array',
        'price' => 'decimal:2',
    ];

    /**
     * Relationship: A plan has many subscriptions.
     */
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    public function planRequests()
    {
        return $this->hasMany(PlanRequest::class);
    }
}
