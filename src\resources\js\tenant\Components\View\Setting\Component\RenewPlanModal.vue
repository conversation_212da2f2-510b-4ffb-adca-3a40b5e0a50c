<template>
  <div class="modal fade" :id="modalId" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-md" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title font-weight-semibold">Renew Your Current Plan</h3>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">
              <i class="fas fa-times"></i>
            </span>
          </button>
        </div>

        <div class="modal-body">
          <div v-if="loading" class="d-flex align-items-center justify-content-center" style="height: 300px;">
            <div class="spinner-border text-primary" role="status">
              <span class="sr-only">Loading...</span>
            </div>
          </div>

          <!-- Pending Request Component -->
          <PendingRequestAlert
            v-else-if="hasPendingRequest"
            :pending-request="pendingRequest"
            :show-actions="true"
            :show-refresh-button="true"
            @refresh="refreshPlans"
          />

          <!-- Active Plan Display (only when no pending request) -->
          <div v-else-if="activePlan">
            <div>
              <div class="card h-100 m-4 current-plan">
                <div class="current-badge">
                  <i class="fas fa-check-circle me-1"></i> Current Plan
                </div>

                <div class="card-header text-center my-6">
                  <h4 class="my-2">{{ activePlan.plan_name }}</h4>
                  <div class="my-3">
                    <h2 v-if="parseFloat(activePlan.price) === 0">₱ Premium</h2>
                    <h2 v-else>₱{{ activePlan.price }}</h2>
                  </div>
                  <h6 class="text-secondary mb-0">Up to {{ activePlan.employee_limit }} employees</h6>
                </div>

                <div class="card-body">
                  <p class="text-muted text-center mb-4">{{ activePlan.plan_description }}</p>
                  <ul class="list-unstyled">
                    <li class="mb-2" v-for="(feature, i) in activePlan.features" :key="i">
                      <i class="fas fa-check text-success mr-2"></i> {{ feature }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="pb-4 px-4">
              <button class="btn btn-primary w-100" @click="renewCurrentPlan" :disabled="loading || !activePlan">
                Renew this Plan
              </button>
            </div>
          </div>

          <!-- No Active Plan Message -->
          <div v-else class="text-center py-4">
            <p class="text-muted">No active plan found.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { axiosGet, axiosPost } from "../../../../../common/Helper/AxiosHelper";
import { PLANS, RENEW_PLAN } from "../../../../Config/ApiUrl";
import PendingRequestAlert from "../../../../../common/Components/Helper/PendingRequestAlert.vue";

export default {
  name: "RenewPlanModal",
  components: {
    PendingRequestAlert,
  },
  props: {
    modalId: { type: String, default: 'renewPlanModal' },
    currentPlanId: { type: [Number, String], default: null }
  },
  data() {
    return {
      loading: false,
      activePlan: null,
      hasPendingRequest: false,
      pendingRequest: null,
      error: null,
      hasLoadedPlans: false,
      isModalVisible: false
    };
  },
  mounted() {
    // Set up modal event listeners instead of immediately fetching
    $(`#${this.modalId}`).on('show.bs.modal', () => {
      this.isModalVisible = true;
      this.onModalShow();
    });
    
    $(`#${this.modalId}`).on('hidden.bs.modal', () => {
      this.isModalVisible = false;
    });
  },
  beforeDestroy() {
    // Clean up event listeners
    $(`#${this.modalId}`).off('show.bs.modal');
    $(`#${this.modalId}`).off('hidden.bs.modal');
  },
  methods: {
    // Called when modal is about to be shown
    async onModalShow() {
      if (!this.hasLoadedPlans) {
        await this.fetchPlans();
      }
    },
    
    async fetchPlans() {
      this.loading = true;
      this.error = null;
      try {
        const response = await axiosGet(PLANS);
        
        if (response.data) {
          this.activePlan = response.data.activePlan || null;
          this.hasPendingRequest = response.data.hasPendingRequest || false;
          this.pendingRequest = response.data.pendingRequest || null;
        }
        
        this.hasLoadedPlans = true;
        
      } catch (error) {
        this.error = error.response?.data?.message || 'Failed to load plan';
        this.$toastr.e('', this.error);
      } finally {
        this.loading = false;
      }
    },
    
    async renewCurrentPlan() {
      if (!this.activePlan) return;
      try {
        this.loading = true;
        await axiosPost(RENEW_PLAN, { plan_id: this.activePlan.id });
        this.$emit('plan-renewed', this.activePlan);
        this.$toastr.s('Renewal request sent successfully!');
        this.hide();
      } catch (error) {
        this.$toastr.e('Failed to renew plan. Please try again.');
      } finally {
        this.loading = false;
      }
    },
    
    // Enhanced showModal method that ensures data is loaded
    async showModal() {
      if (!this.hasLoadedPlans && !this.isModalVisible) {
        // Pre-load data before showing modal for better UX
        await this.fetchPlans();
      }
      $(`#${this.modalId}`).modal('show');
    },
    
    show() {
      this.showModal();
    },
    
    hide() {
      $(`#${this.modalId}`).modal('hide');
    },
    
    // Method to manually refresh plans (called by PendingRequestAlert)
    refreshPlans() {
      this.hasLoadedPlans = false;
      this.fetchPlans();
    }
  }
};
</script>

<style scoped>
.current-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #28a745;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  z-index: 10;
}

.card-header {
  border-radius: 10px 10px 0 0;
  margin: 2rem 0rem;
  background-color: transparent !important;
  border-bottom: none !important;
}

.current-plan {
  border: 1px solid #28a745;
}
</style>