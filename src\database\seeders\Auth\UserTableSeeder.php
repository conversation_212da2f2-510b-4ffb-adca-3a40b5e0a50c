<?php

namespace Database\Seeders\Auth;

use App\Models\Core\Auth\User;
use App\Models\Core\Status;
use Database\Seeders\Traits\DisableForeignKeys;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

/**
 * Class UserTableSeeder.
 */
class UserTableSeeder extends Seeder
{
    use DisableForeignKeys;

    /**
     * Run the database seed.
     */
    public function run()
    {
        $this->disableForeignKeys();

        // Add the master administrator, user id of 1
        User::query()->create([
            'first_name' => '<PERSON>',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('123456'),
            'status_id' => Status::findByNameAndType('status_active')->id,
            'is_in_employee' => 1,
        ]);

        $this->enableForeignKeys();
    }
}
