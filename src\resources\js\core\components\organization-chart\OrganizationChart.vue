<template>
    <div dir="ltr">
        <organization-chart 
            :datasource="chartData" 
            @node-click="clickedNote" 
            :pan="druggable"
            :render-content="renderContent"
        >
            <template #default="{ nodeData }">
                <div class="custom-node">
                    <div class="avatar-section" v-if="nodeData.avatar || nodeData.title">
                        <div class="avatar-container">
                            <img 
                                v-if="nodeData.avatar && !isInitials(nodeData.avatar)"
                                :src="getFullAvatarUrl(nodeData.avatar)" 
                                :alt="nodeData.title || 'Avatar'"
                                class="avatar-image"
                                @error="handleImageError"
                            >
                            <div 
                                v-else
                                class="avatar-initials"
                                :class="getInitialsClass(nodeData.title)"
                            >
                                {{ nodeData.avatar || getInitials(nodeData.title) }}
                            </div>
                        </div>
                        <div class="manager-name" v-if="nodeData.title">
                            {{ nodeData.title }}
                        </div>
                    </div>
                    <div class="department-name">
                        {{ nodeData.name }}
                    </div>
                </div>
            </template>
        </organization-chart>
    </div>
</template>

<script>
import OrganizationChart from 'vue-organization-chart'
import 'vue-organization-chart/dist/orgchart.css'

export default {
    name: "AppOrganizationChart",
    components: {
        OrganizationChart
    },
    props: {
        chartData: {
            type: Object,
            require: true
        },
        renderContent: {
            type: Function,
 
        },
        height: {
            type: Number,
            default: 760
        },
        druggable: {
            type: Boolean,
            default: false,
        }
    },
    mounted() {
        this.updateContainerHeight();
    },
    watch: {
        height: {
            handler() {
                this.updateContainerHeight();
            },
            immediate: true
        },
        chartData: {
            handler() {
                // Recalculate height when data changes
                this.$nextTick(() => {
                    this.updateContainerHeight();
                });
            },
            deep: true
        }
    },
    methods: {
        updateContainerHeight() {
            this.$nextTick(() => {
                const container = $(".orgchart-container");
                if (container.length) {
                    container.css({
                        "height": this.height + "px",
                        "min-height": "760px"
                    });
                }
            });
        },
        
        clickedNote(value){
            this.$emit('selected-item', value);
        },
        
        isInitials(avatar) {
            return avatar && avatar.length <= 3 && /^[A-Z]+$/.test(avatar);
        },
        
        getInitials(name) {
            if (!name) return '';
            return name.split(' ')
                .map(n => n.charAt(0).toUpperCase())
                .join('')
                .substring(0, 2);
        },
        
        getInitialsClass(name) {
            // Generate consistent color class based on name
            if (!name) return 'bg-gray';
            const colors = ['bg-blue', 'bg-green', 'bg-purple', 'bg-orange', 'bg-red', 'bg-teal'];
            const index = name.charCodeAt(0) % colors.length;
            return colors[index];
        },
        
        getFullAvatarUrl(avatar) {
            // Handle both full URLs and relative paths
            if (avatar.startsWith('http')) {
                return avatar;
            }
            // For relative paths, construct the full URL
            // Adjust this based on your Laravel setup
            const baseUrl = window.location.origin;
            return `${baseUrl}/storage/${avatar}`;
        },
        
        handleImageError(event) {
            console.warn('Image failed to load:', event.target.src);
            // Hide broken image and show initials instead
            event.target.style.display = 'none';
            // You could also emit an event to parent to handle this
            this.$emit('image-load-error', event.target.src);
        }
    }
}
</script>

<style scoped>
.custom-node {
    text-align: center;
    border-radius: 8px;
    box-shadow: 0;
    border: 2px solid #0099ff;
}

.avatar-section {
    margin-bottom: 4px;
}

.avatar-container {
    padding: 1.3rem;
    display: flex;
    justify-content: center;
}

.avatar-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-initials {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    color: white;
}

.bg-blue { background-color: #3b82f6; }
.bg-green { background-color: #10b981; }
.bg-purple { background-color: #8b5cf6; }
.bg-orange { background-color: #f59e0b; }
.bg-red { background-color: #ef4444; }
.bg-teal { background-color: #14b8a6; }
.bg-gray { background-color: #6b7280; }

.manager-name {
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;
}

.department-name {
    font-size: 12px;
    font-weight: 600;
    color: #fff;
    background-color: #0099ff;
    padding: 2px 2px;
}

/* Ensure the chart can expand properly */
:deep(.orgchart-container) {
    overflow: visible !important;
}

:deep(.orgchart) {
    min-height: 100%;
}
</style>