<?php

namespace App\Exports;

use App\Models\Core\Auth\User;
use App\Models\Tenant\Export\ModuleExport;
use App\Models\Tenant\WorkingShift\WorkingShift;
use App\Notifications\Tenant\ExportFailedNotification;
use App\Repositories\Core\Status\StatusRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;

class EmployeeModuleExport implements
    FromQuery,
    WithMapping,
    ShouldQueue,
//    WithCustomChunkSize,
    WithEvents,
    WithHeadings,
    WithTitle,
    ShouldAutoSize
{
    use Exportable, RegistersEventListeners;

    protected int|null $module_export_id;
    private WorkingShift $defaultWorkingShift;

    public function __construct($module_export_id = null)
    {
        $this->defaultWorkingShift = WorkingShift::getDefault();
        $this->module_export_id = $module_export_id;
    }

//    public function chunkSize(): int
//    {
//        return 1000;
//    }

    public static function afterSheet(AfterSheet $event)
    {
        // Example event listener, you can define additional events if needed
        // update completed_modules employee
        if($event->getConcernable()?->module_export_id) {
            $module_export = ModuleExport::query()->find($event->getConcernable()->module_export_id);
            if($module_export) {
                $completed_modules = array_values(array_unique(array_merge($module_export->completed_modules, ['employee'])));
                $module_export->update([
                    'completed_modules' => $completed_modules,
                ]);
            }
        }
         Log::info('Employee: After sheet');
    }
    public function failed(\Throwable $exception): void
    {
        // handle failed export
        // update status rejected
        if($this->module_export_id) {
            $module_export = ModuleExport::query()->find($this->module_export_id);
            if($module_export) {
                $module_export->update([
                    'status_id' => resolve(StatusRepository::class)->exportRejected(),
                    'data' => [
                        'message' => 'Employee export failed.'
                    ],
                ]);
                // notify user failed
                notify()
                    ->on('export_failed')
                    ->mergeAudiences($module_export->created_by)
                    ->send(ExportFailedNotification::class);
            }
        }
        // notify user failed
        Log::info('employee: failed');
    }

    public function query()
    {
        return User::query()
            ->select('id', 'email', 'first_name', 'last_name')
            ->with([
                'department:id,name,manager_id,department_id',
                'department.parentDepartment',
                'workingShift',
                'employmentStatus:id,name',
            ]);
    }

    public function map($user): array
    {
        return [
            $user->email,
            $user->first_name,
            $user->last_name,
            $this->getDepartment($user),
            $this->getManagerInfo($user),
            $this->getParentDepartment($user),
            $this->getWorkingShift($user),
            $this->getEmploymentStatus($user),
        ];
    }

    public function headings(): array
    {
        return [
            'Email',
            'First_name',
            'Last_name',
            'Department_name',
            'Is_department_manager',
            'Parent_department',
            'WorkShift',
            'Employment_status',
        ];
    }

    public function title(): string
    {
        return __t('employees');
    }

    // mapping functions
    private function getDepartment($user): string
    {
        if (isset($user->department) && isset($user->department->name)) {
            return $user->department->name;
        }
        return '';
    }

    private function getManagerInfo($user): string
    {
        if (isset($user->department) && isset($user->department->manager_id)) {
            return $user->id == $user->department->manager_id ? '1' : '0';
        }
        return '0';
    }

    private function getParentDepartment($user): string
    {
        if (isset($user->department) && isset($user->department->parentDepartment) && isset($user->department->parentDepartment->name)) {
            return $user->department->parentDepartment->name ?? '';
        }
        return '';
    }

    private function getWorkingShift($user): string
    {
        if (isset($user->workingShift) && isset($user->workingShift->name)) {
            return $user->workingShift->name;
        }
        return $this->defaultWorkingShift->name ?? '';
    }

    private function getEmploymentStatus($user): string
    {
        if (isset($user->employmentStatus) && isset($user->employmentStatus->name)) {
            return $user->employmentStatus->name;
        }
        return '';
    }
}
