<?php

use App\Http\Controllers\Tenant\Payroll\BeneficiaryBadgeController;
use App\Http\Controllers\Tenant\Payroll\ConsolidationController;
use App\Http\Controllers\Tenant\Payroll\ManualPayrunController;
use App\Http\Controllers\Tenant\Payroll\PayrollSettingController;
use App\Http\Controllers\Tenant\Payroll\PayrunController;
use App\Http\Controllers\Tenant\Payroll\PayslipController;
use App\Http\Controllers\Tenant\Payroll\RunDefaultPayrun;
use App\Http\Controllers\Tenant\Payroll\SignatureController;
use App\Http\Controllers\Tenant\Payroll\TransmittalController;
use Illuminate\Routing\Router;

Route::group(['prefix' => 'app', ], function (Router $router) {

    $router->get('settings/payrun', [PayrollSettingController::class, 'index'])
        ->name('payroll-settings.index');

    $router->post('settings/payrun', [PayrollSettingController::class, 'updateDefault'])
        ->name('payrun-period.update');

    $router->post('settings/payrun/audience', [PayrollSettingController::class, 'updateAudience'])
        ->name('payrun-audience.update');

    $router->post('settings/payrun/beneficiaries', [PayrollSettingController::class, 'updateBeneficiaries'])
        ->name('payrun-beneficiary.update');

    $router->apiResource('beneficiaries', BeneficiaryBadgeController::class);

    $router->get('payslip', [PayslipController::class, 'index'])
        ->name('payslips.index');

    $router->get('payslip/{payslip}/send', [PayslipController::class, 'sendPayslip'])
        ->name('individual-payslip.send');

    $router->get('payslip/{payslip}/delete', [PayslipController::class, 'destroy'])
        ->name('payslip.delete');

    $router->get('payslip/{payslip}/pdf', [PayslipController::class, 'showPdf'])
        ->name('payslip-pdf.index');

    $router->patch('payslip/{payslip}/update', [PayslipController::class, 'update'])
        ->name('payslip.update');

    $router->get('payslip/send-monthly', [PayslipController::class, 'sendMonthlyPayslip'])
        ->name('bulk-payslip.send');

    $router->post('payrun/default', [RunDefaultPayrun::class, 'store'])
        ->name('default-payrun.run');

    $router->post('payrun/manual', [ManualPayrunController::class, 'store'])
        ->name('manual-payrun.run');

    $router->get('payruns', [PayrunController::class, 'index'])
        ->name('payruns.index');

    $router->delete('payruns/{payrun}', [PayrunController::class, 'delete'])
        ->name('payruns.delete');

    $router->patch('payruns/{payrun}', [ManualPayrunController::class, 'update'])
        ->name('payruns.update');

    $router->get('payruns/{payrun}', [ManualPayrunController::class, 'index'])
        ->name('payruns.index');

    $router->get('payruns/{payrun}/send-payslip', [PayrunController::class, 'sendPayslips'])
        ->name('payrun-payslips.send');

    // Transmittal routes
    $router->get('transmittals', [TransmittalController::class, 'index'])
        ->name('transmittals.index')
        ->middleware('can:view_transmittal');

    $router->post('transmittals/generate', [TransmittalController::class, 'generate'])
        ->name('transmittals.generate')
        ->middleware('can:generate_transmittal');

    $router->get('transmittals/{transmittal}', [TransmittalController::class, 'show'])
        ->name('transmittals.show')
        ->middleware('can:view_transmittal');

    $router->delete('transmittals/{transmittal}', [TransmittalController::class, 'destroy'])
        ->name('transmittals.destroy')
        ->middleware('can:delete_transmittal');

    $router->post('transmittals/export-pdf', [TransmittalController::class, 'exportPdf'])
        ->name('transmittals.export-pdf')
        ->middleware('can:export_transmittal');

    $router->post('transmittals/export-excel', [TransmittalController::class, 'exportExcel'])
        ->name('transmittals.export-excel')
        ->middleware('can:export_transmittal');

    $router->get('transmittals-summary', [TransmittalController::class, 'summary'])
        ->name('transmittals.summary')
        ->middleware('can:view_transmittal');

    $router->get('transmittals/payruns', [TransmittalController::class, 'getPayruns'])
        ->name('transmittals.payruns')
        ->middleware('can:view_transmittal');

    $router->get('transmittals/payruns/{payrun}/departments', [TransmittalController::class, 'getPayrunDepartments'])
        ->name('transmittals.payrun-departments')
        ->middleware('can:view_transmittal');

    // Signature routes
    $router->apiResource('signatures', SignatureController::class)
        ->middleware('can:view_signature');

    $router->post('signatures/{signature}/toggle-status', [SignatureController::class, 'toggleStatus'])
        ->name('signatures.toggle-status')
        ->middleware('can:manage_signatures');

    $router->post('signatures/{signature}/set-default', [SignatureController::class, 'setDefault'])
        ->name('signatures.set-default')
        ->middleware('can:manage_signatures');

    $router->get('signature-types', [SignatureController::class, 'getTypes'])
        ->name('signature-types')
        ->middleware('can:view_signature');

    $router->get('default-signature', [SignatureController::class, 'getDefault'])
        ->name('default-signature')
        ->middleware('can:view_signature');

    // Consolidation routes
    $router->get('consolidations', [ConsolidationController::class, 'index'])
        ->name('consolidations.index')
        ->middleware('can:view_consolidation');

    $router->post('consolidations/generate', [ConsolidationController::class, 'generate'])
        ->name('consolidations.generate')
        ->middleware('can:generate_consolidation');

    $router->get('consolidations/summary', [ConsolidationController::class, 'summary'])
        ->name('consolidations.summary')
        ->middleware('can:view_consolidation');

    $router->post('consolidations/export-pdf', [ConsolidationController::class, 'exportPdf'])
        ->name('consolidations.export-pdf')
        ->middleware('can:export_consolidation');

    $router->post('consolidations/export-excel', [ConsolidationController::class, 'exportExcel'])
        ->name('consolidations.export-excel')
        ->middleware('can:export_consolidation');

    $router->get('consolidations/department-details', [ConsolidationController::class, 'departmentDetails'])
        ->name('consolidations.department-details')
        ->middleware('can:view_consolidation');

});
