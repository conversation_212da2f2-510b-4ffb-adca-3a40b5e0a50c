<?php

namespace App\Http\Controllers\Tenant\Payroll;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Payroll\Transmittal;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class TransmittalController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $transmittals = Transmittal::query()
            ->when($request->get('search'), function ($query, $search) {
                $query->where('title', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
            })
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 10));

        return response()->json($transmittals);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'data' => 'nullable|array'
        ]);

        $transmittal = Transmittal::create([
            'title' => $request->title,
            'description' => $request->description,
            'data' => $request->data ?? [],
            'created_by' => auth()->id(),
        ]);

        return response()->json([
            'message' => __t('transmittal_created_successfully'),
            'data' => $transmittal
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Transmittal $transmittal): JsonResponse
    {
        return response()->json($transmittal);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Transmittal $transmittal): JsonResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'data' => 'nullable|array'
        ]);

        $transmittal->update([
            'title' => $request->title,
            'description' => $request->description,
            'data' => $request->data ?? [],
            'updated_by' => auth()->id(),
        ]);

        return response()->json([
            'message' => __t('transmittal_updated_successfully'),
            'data' => $transmittal
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Transmittal $transmittal): JsonResponse
    {
        $transmittal->delete();

        return response()->json([
            'message' => __t('transmittal_deleted_successfully')
        ]);
    }

    /**
     * Export transmittals to Excel
     */
    public function export(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        return \Maatwebsite\Excel\Facades\Excel::download(
            new \App\Exports\TransmittalExport(),
            'transmittals.xlsx'
        );
    }
}
