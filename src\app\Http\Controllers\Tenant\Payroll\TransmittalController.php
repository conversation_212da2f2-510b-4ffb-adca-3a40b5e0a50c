<?php

namespace App\Http\Controllers\Tenant\Payroll;

use App\Exceptions\GeneralException;
use App\Exports\TransmittalExport;
use App\Http\Controllers\Controller;
use App\Models\Tenant\Payroll\Payslip;
use App\Models\Tenant\Payroll\Payrun;
use App\Models\Tenant\Payroll\Transmittal;
use App\Repositories\Tenant\Employee\EmployeeRepository;
use App\Services\Tenant\Payroll\TransmittalService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Barryvdh\DomPDF\Facade\Pdf;

class TransmittalController extends Controller
{
    protected $transmittalService;
    protected $employeeRepository;

    public function __construct(EmployeeRepository $employeeRepository, TransmittalService $transmittalService)
    {
        $this->employeeRepository = $employeeRepository;
        $this->transmittalService = $transmittalService;
    }

    /**
     * Display a listing of transmittals
     */
    public function index(Request $request)
    {
        if (!authorize_any(['view_transmittal'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $query = Transmittal::with(['department', 'payrun', 'payslips.user.department', 'payslips.user.designation', 'status', 'generatedBy'])
            ->when($request->period, function ($q) use ($request) {
                switch ($request->period) {
                    case 'thisMonth':
                        return $q->whereMonth('created_at', now()->month)
                                 ->whereYear('created_at', now()->year);
                    case 'lastMonth':
                        return $q->whereMonth('created_at', now()->subMonth()->month)
                                 ->whereYear('created_at', now()->subMonth()->year);
                    case 'thisWeek':
                        return $q->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                    case 'lastWeek':
                        return $q->whereBetween('created_at', [
                            now()->subWeek()->startOfWeek(),
                            now()->subWeek()->endOfWeek()
                        ]);
                }
            })
            ->when($request->search, function ($q) use ($request) {
                return $q->whereHas('payslips.user', function ($query) use ($request) {
                    $query->where('first_name', 'like', "%{$request->search}%")
                          ->orWhere('last_name', 'like', "%{$request->search}%")
                          ->orWhere('email', 'like', "%{$request->search}%");
                });
            })
            ->when($request->filled('department_id'), function ($q) use ($request) {
                return $q->where('department_id', $request->department_id);
            })
            ->when($request->filled('payrun_id'), function ($q) use ($request) {
                return $q->where('payrun_id', $request->payrun_id);
            });

        $transmittals = $query->orderBy('created_at', 'desc')
                             ->paginate($request->per_page ?? 10);

        return response()->json([
            'data' => $transmittals->items(),
            'total' => $transmittals->total(),
            'per_page' => $transmittals->perPage(),
            'current_page' => $transmittals->currentPage(),
            'last_page' => $transmittals->lastPage()
        ]);
    }

    /**
     * Generate transmittal for a specific period
     */
    public function generate(Request $request)
    {
        if (!authorize_any(['generate_transmittal'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $request->validate([
            'payrun_id' => 'required|exists:payruns,id',
            'department_id' => 'nullable|exists:departments,id',
            'employee_ids' => 'nullable|array',
            'employee_ids.*' => 'exists:users,id'
        ]);

        try {
            $payrun = Payrun::findOrFail($request->payrun_id);

            $result = $this->transmittalService->generateFromPayrun($payrun, [
                'department_id' => $request->department_id,
                'employee_ids' => $request->employee_ids
            ]);

            return response()->json([
                'status' => true,
                'message' => 'Transmittals generated successfully',
                'data' => [
                    'count' => $result['count'],
                    'total_amount' => $result['total_amount']
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to generate transmittals: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show specific transmittal
     */
    public function show(Transmittal $transmittal)
    {
        if (!authorize_any(['view_transmittal'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $transmittal->load(['department', 'payrun', 'payslips.user.department', 'payslips.user.designation', 'status', 'generatedBy']);

        return response()->json([
            'status' => true,
            'data' => $transmittal
        ]);
    }

    /**
     * Export transmittal as PDF
     */
    public function exportPdf(Request $request)
    {
        if (!authorize_any(['export_transmittal'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $transmittalIds = $request->transmittal_ids ?? [];
        $payslipsForExport = $this->transmittalService->getPayslipsForExport($transmittalIds);

        $pdf = PDF::loadView('tenant.payroll.exports.transmittal-pdf', [
            'transmittals' => $payslipsForExport,
            'generated_at' => now(),
            'generated_by' => auth()->user()->full_name
        ]);

        return $pdf->download('transmittals-' . date('Y-m-d') . '.pdf');
    }

    /**
     * Export transmittal as Excel
     */
    public function exportExcel(Request $request)
    {
        if (!authorize_any(['export_transmittal'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $transmittalIds = $request->transmittal_ids ?? [];

        return Excel::download(
            new TransmittalExport($transmittalIds),
            'transmittals-' . date('Y-m-d') . '.xlsx'
        );
    }

    /**
     * Delete transmittal
     */
    public function destroy(Transmittal $transmittal)
    {
        if (!authorize_any(['delete_transmittal'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $transmittal->delete();

        return response()->json([
            'status' => true,
            'message' => 'Transmittal deleted successfully'
        ]);
    }

    /**
     * Get transmittal summary statistics
     */
    public function summary(Request $request)
    {
        if (!authorize_any(['view_transmittal'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $period = $request->period ?? 'thisMonth';

        $query = Transmittal::query();

        switch ($period) {
            case 'thisMonth':
                $query->whereMonth('created_at', now()->month)
                      ->whereYear('created_at', now()->year);
                break;
            case 'lastMonth':
                $query->whereMonth('created_at', now()->subMonth()->month)
                      ->whereYear('created_at', now()->subMonth()->year);
                break;
        }

        $summary = [
            'total_transmittals' => $query->count(),
            'total_amount' => $query->sum('total_amount'),
            'total_employees' => $query->distinct('employee_id')->count(),
            'by_status' => $query->with('status')
                                ->get()
                                ->groupBy('status.name')
                                ->map(function ($group) {
                                    return [
                                        'count' => $group->count(),
                                        'amount' => $group->sum('total_amount')
                                    ];
                                })
        ];

        return response()->json([
            'status' => true,
            'data' => $summary
        ]);
    }

    /**
     * Get available payruns for transmittal generation
     */
    public function getPayruns(Request $request)
    {
        if (!authorize_any(['view_transmittal'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $payruns = $this->transmittalService->getAvailablePayruns();

        return response()->json([
            'status' => true,
            'data' => $payruns
        ]);
    }

    /**
     * Get departments with payslips for a specific payrun
     */
    public function getPayrunDepartments(Request $request, Payrun $payrun)
    {
        if (!authorize_any(['view_transmittal'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $departments = $this->transmittalService->getDepartmentsWithPayslips($payrun);

        return response()->json([
            'status' => true,
            'data' => $departments
        ]);
    }
}
