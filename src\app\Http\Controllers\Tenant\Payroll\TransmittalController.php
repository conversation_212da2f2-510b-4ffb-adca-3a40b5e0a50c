<?php

namespace App\Http\Controllers\Tenant\Payroll;

use App\Exceptions\GeneralException;
use App\Exports\TransmittalExport;
use App\Http\Controllers\Controller;
use App\Models\Tenant\Payroll\Payslip;
use App\Models\Tenant\Payroll\Transmittal;
use App\Repositories\Tenant\Employee\EmployeeRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Barryvdh\DomPDF\Facade\Pdf;

class TransmittalController extends Controller
{
    protected $transmittalService;
    protected $employeeRepository;

    public function __construct(EmployeeRepository $employeeRepository)
    {
        $this->employeeRepository = $employeeRepository;
    }

    /**
     * Display a listing of transmittals
     */
    public function index(Request $request)
    {
        if (!authorize_any(['view_transmittal'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $query = Transmittal::with(['employee', 'status'])
            ->when($request->period, function ($q) use ($request) {
                switch ($request->period) {
                    case 'thisMonth':
                        return $q->whereMonth('created_at', now()->month)
                                 ->whereYear('created_at', now()->year);
                    case 'lastMonth':
                        return $q->whereMonth('created_at', now()->subMonth()->month)
                                 ->whereYear('created_at', now()->subMonth()->year);
                    case 'thisWeek':
                        return $q->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                    case 'lastWeek':
                        return $q->whereBetween('created_at', [
                            now()->subWeek()->startOfWeek(),
                            now()->subWeek()->endOfWeek()
                        ]);
                }
            })
            ->when($request->search, function ($q) use ($request) {
                return $q->whereHas('employee', function ($query) use ($request) {
                    $query->where('first_name', 'like', "%{$request->search}%")
                          ->orWhere('last_name', 'like', "%{$request->search}%")
                          ->orWhere('email', 'like', "%{$request->search}%");
                });
            });

        $transmittals = $query->orderBy('created_at', 'desc')
                             ->paginate($request->per_page ?? 10);

        return response()->json([
            'data' => $transmittals->items(),
            'total' => $transmittals->total(),
            'per_page' => $transmittals->perPage(),
            'current_page' => $transmittals->currentPage(),
            'last_page' => $transmittals->lastPage()
        ]);
    }

    /**
     * Generate transmittal for a specific period
     */
    public function generate(Request $request)
    {
        if (!authorize_any(['generate_transmittal'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $request->validate([
            'period_start' => 'required|date',
            'period_end' => 'required|date|after_or_equal:period_start',
            'department_id' => 'nullable|exists:departments,id',
            'employee_ids' => 'nullable|array',
            'employee_ids.*' => 'exists:users,id'
        ]);

        try {
            DB::beginTransaction();

            // Get payslips for the specified period
            $payslipsQuery = Payslip::with(['user', 'payrun', 'beneficiaries'])
                ->whereBetween('start_date', [$request->period_start, $request->period_end])
                ->where('status_id', '!=', null); // Only processed payslips

            if ($request->department_id) {
                $payslipsQuery->whereHas('user.department', function ($q) use ($request) {
                    $q->where('id', $request->department_id);
                });
            }

            if ($request->employee_ids) {
                $payslipsQuery->whereIn('user_id', $request->employee_ids);
            }

            $payslips = $payslipsQuery->get();

            if ($payslips->isEmpty()) {
                return response()->json([
                    'status' => false,
                    'message' => 'No payslips found for the specified criteria'
                ], 400);
            }

            // Group payslips by employee and create transmittals
            $transmittals = [];
            foreach ($payslips->groupBy('user_id') as $employeePayslips) {
                $employee = $employeePayslips->first()->user;
                $totalAmount = $employeePayslips->sum('net_salary');

                $transmittal = Transmittal::create([
                    'employee_id' => $employee->id,
                    'period_start' => $request->period_start,
                    'period_end' => $request->period_end,
                    'total_amount' => $totalAmount,
                    'payslip_count' => $employeePayslips->count(),
                    'status_id' => 1, // Generated status
                    'generated_by' => auth()->id(),
                    'metadata' => [
                        'payslip_ids' => $employeePayslips->pluck('id')->toArray(),
                        'department' => $employee->department->name ?? null,
                        'position' => $employee->designation->name ?? null
                    ]
                ]);

                $transmittals[] = $transmittal;
            }

            DB::commit();

            return response()->json([
                'status' => true,
                'message' => 'Transmittals generated successfully',
                'data' => [
                    'count' => count($transmittals),
                    'total_amount' => collect($transmittals)->sum('total_amount')
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => false,
                'message' => 'Failed to generate transmittals: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show specific transmittal
     */
    public function show(Transmittal $transmittal)
    {
        if (!authorize_any(['view_transmittal'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $transmittal->load(['employee.department', 'employee.designation', 'status', 'generatedBy']);

        return response()->json([
            'status' => true,
            'data' => $transmittal
        ]);
    }

    /**
     * Export transmittal as PDF
     */
    public function exportPdf(Request $request)
    {
        if (!authorize_any(['export_transmittal'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $transmittalIds = $request->transmittal_ids ?? [];
        $transmittals = Transmittal::with(['employee.department', 'employee.designation'])
            ->whereIn('id', $transmittalIds)
            ->get();

        $pdf = PDF::loadView('tenant.payroll.exports.transmittal-pdf', [
            'transmittals' => $transmittals,
            'generated_at' => now(),
            'generated_by' => auth()->user()->full_name
        ]);

        return $pdf->download('transmittals-' . date('Y-m-d') . '.pdf');
    }

    /**
     * Export transmittal as Excel
     */
    public function exportExcel(Request $request)
    {
        if (!authorize_any(['export_transmittal'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $transmittalIds = $request->transmittal_ids ?? [];

        return Excel::download(
            new TransmittalExport($transmittalIds),
            'transmittals-' . date('Y-m-d') . '.xlsx'
        );
    }

    /**
     * Delete transmittal
     */
    public function destroy(Transmittal $transmittal)
    {
        if (!authorize_any(['delete_transmittal'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $transmittal->delete();

        return response()->json([
            'status' => true,
            'message' => 'Transmittal deleted successfully'
        ]);
    }

    /**
     * Get transmittal summary statistics
     */
    public function summary(Request $request)
    {
        if (!authorize_any(['view_transmittal'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $period = $request->period ?? 'thisMonth';

        $query = Transmittal::query();

        switch ($period) {
            case 'thisMonth':
                $query->whereMonth('created_at', now()->month)
                      ->whereYear('created_at', now()->year);
                break;
            case 'lastMonth':
                $query->whereMonth('created_at', now()->subMonth()->month)
                      ->whereYear('created_at', now()->subMonth()->year);
                break;
        }

        $summary = [
            'total_transmittals' => $query->count(),
            'total_amount' => $query->sum('total_amount'),
            'total_employees' => $query->distinct('employee_id')->count(),
            'by_status' => $query->with('status')
                                ->get()
                                ->groupBy('status.name')
                                ->map(function ($group) {
                                    return [
                                        'count' => $group->count(),
                                        'amount' => $group->sum('total_amount')
                                    ];
                                })
        ];

        return response()->json([
            'status' => true,
            'data' => $summary
        ]);
    }
}
