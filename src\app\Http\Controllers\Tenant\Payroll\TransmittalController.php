<?php

namespace App\Http\Controllers\Tenant\Payroll;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Payroll\Transmittal;
use App\Models\Tenant\Payroll\Payslip;
use App\Exports\TransmittalPayrollExport;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Maatwebsite\Excel\Facades\Excel;
use Barryvdh\DomPDF\Facade\Pdf;

class TransmittalController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $transmittals = Transmittal::query()
            ->when($request->get('search'), function ($query, $search) {
                $query->where('title', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
            })
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 10));

        return response()->json($transmittals);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'payslip_ids' => 'nullable|array',
            'payslip_ids.*' => 'exists:payslips,id',
            'department' => 'nullable|string',
            'payroll_period_start' => 'nullable|date',
            'payroll_period_end' => 'nullable|date',
            'prepared_by' => 'nullable|string',
            'prepared_by_title' => 'nullable|string'
        ]);

        // Calculate totals from selected payslips
        $totals = $this->calculatePayslipTotals($request->payslip_ids ?? []);

        $transmittal = Transmittal::create([
            'title' => $request->title,
            'description' => $request->description,
            'payslip_ids' => $request->payslip_ids ?? [],
            'department' => $request->department,
            'payroll_period_start' => $request->payroll_period_start,
            'payroll_period_end' => $request->payroll_period_end,
            'total_amount' => $totals['total_amount'],
            'total_employees' => $totals['total_employees'],
            'prepared_by' => $request->prepared_by,
            'prepared_by_title' => $request->prepared_by_title,
            'created_by' => auth()->id(),
        ]);

        return response()->json([
            'message' => __t('transmittal_created_successfully'),
            'data' => $transmittal
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Transmittal $transmittal): JsonResponse
    {
        return response()->json($transmittal);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Transmittal $transmittal): JsonResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'payslip_ids' => 'nullable|array',
            'payslip_ids.*' => 'exists:payslips,id',
            'department' => 'nullable|string',
            'payroll_period_start' => 'nullable|date',
            'payroll_period_end' => 'nullable|date',
            'prepared_by' => 'nullable|string',
            'prepared_by_title' => 'nullable|string'
        ]);

        // Calculate totals from selected payslips
        $totals = $this->calculatePayslipTotals($request->payslip_ids ?? []);

        $transmittal->update([
            'title' => $request->title,
            'description' => $request->description,
            'payslip_ids' => $request->payslip_ids ?? [],
            'department' => $request->department,
            'payroll_period_start' => $request->payroll_period_start,
            'payroll_period_end' => $request->payroll_period_end,
            'total_amount' => $totals['total_amount'],
            'total_employees' => $totals['total_employees'],
            'prepared_by' => $request->prepared_by,
            'prepared_by_title' => $request->prepared_by_title,
            'updated_by' => auth()->id(),
        ]);

        return response()->json([
            'message' => __t('transmittal_updated_successfully'),
            'data' => $transmittal
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Transmittal $transmittal): JsonResponse
    {
        $transmittal->delete();

        return response()->json([
            'message' => __t('transmittal_deleted_successfully')
        ]);
    }

    /**
     * Get available payslips for transmittal selection.
     */
    public function getPayslips(Request $request): JsonResponse
    {
        $payslips = Payslip::with(['user.profile', 'user.department', 'user.designation', 'payrun'])
            ->when($request->get('department'), function ($query, $department) {
                $query->whereHas('user.department', function ($q) use ($department) {
                    $q->where('name', $department);
                });
            })
            ->when($request->get('payrun_id'), function ($query, $payrunId) {
                $query->where('payrun_id', $payrunId);
            })
            ->when($request->get('period'), function ($query, $period) {
                $query->where('period', $period);
            })
            ->when($request->get('search'), function ($query, $search) {
                $query->whereHas('user', function ($q) use ($search) {
                    $q->where('first_name', 'like', "%{$search}%")
                      ->orWhere('last_name', 'like', "%{$search}%");
                });
            })
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json($payslips);
    }

    /**
     * Export transmittal as Excel.
     */
    public function exportExcel(Transmittal $transmittal)
    {
        $fileName = 'transmittal-' . $transmittal->id . '-' . date('Y-m-d') . '.xlsx';

        return Excel::download(new TransmittalPayrollExport($transmittal), $fileName);
    }

    /**
     * Export transmittal as PDF.
     */
    public function exportPdf(Transmittal $transmittal)
    {
        $payslips = $transmittal->payslips();

        $pdf = Pdf::loadView('exports.transmittal-payroll-pdf', [
            'transmittal' => $transmittal,
            'payslips' => $payslips,
            'companyName' => config('app.name', 'Company Name'),
            'companyAddress' => config('app.company_address', 'Company Address')
        ]);

        $fileName = 'transmittal-' . $transmittal->id . '-' . date('Y-m-d') . '.pdf';

        return $pdf->download($fileName);
    }

    /**
     * Calculate totals from selected payslips.
     */
    private function calculatePayslipTotals(array $payslipIds): array
    {
        if (empty($payslipIds)) {
            return ['total_amount' => 0, 'total_employees' => 0];
        }

        $payslips = Payslip::whereIn('id', $payslipIds)->get();

        return [
            'total_amount' => $payslips->sum('net_salary'),
            'total_employees' => $payslips->count()
        ];
    }

    /**
     * Export transmittals to Excel
     */
    public function export(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        return \Maatwebsite\Excel\Facades\Excel::download(
            new \App\Exports\TransmittalExport(),
            'transmittals.xlsx'
        );
    }
}
