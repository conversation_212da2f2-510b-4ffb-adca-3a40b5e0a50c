<template>
    <div class="content-wrapper">
        <app-page-top-section :title="$t('all_employees')" icon="briefcase">
            <div v-if="isAllDataLoaded">
                <div v-if="!isEmployeeLimitReached">
                    <app-default-button btn-class="btn btn-success mr-2"
                        :title="$fieldTitle('add', 'employee', true)"
                        @click="isEmployeeCreateOpenModalActive = true"
                        v-if="$can('add_employees')"
                    />
                        
                    <app-default-button
                        v-if="$can('invite_employees')"
                        @click="isModalActive = true"
                        :title="$fieldTitle('invite', 'employee', true)"
                    />
                </div>
            </div>
        </app-page-top-section>

        <div v-if="isEmployeeLimitReached" class="alert alert-warning d-flex justify-content-between flex-wrap p-3">
            <div class="d-flex align-items-start">
                <i class="fas fa-exclamation-triangle text-warning mr-3 fa-lg"></i>
                <div>
                    <h5 class="mb-1 font-weight-bold">Employee Limit Reached</h5>
                    <p class="mb-1">
                        You've reached the maximum number of employees allowed for your current subscription plan.
                        To add more employees, please upgrade your plan.
                    </p>
                    <p class="text-secondary mb-0">Current usage: {{ tenantMembers }}/{{ employeeLimit }} employees</p>
                </div>
            </div>
            <div>
                <app-default-button
                    btn-class="btn btn-danger mt-3 mt-md-0 ml-md-4"
                    :title="'Upgrade Plan'"
                    @click="isPlanModalOpen = true"
                />
            </div>
        </div>

        <app-table
            id="employee-table"
            :options="options"
            :card-view="true"
            @getRows="getSelectedRows"
            @action="triggerActions"
        />

        <app-employee-invite
            v-if="isModalActive"
            v-model="isModalActive"
            :selected-url="selectedUrl"
        />

        <employee-create-edit-model
            v-if="isEmployeeCreateOpenModalActive"
            v-model="isEmployeeCreateOpenModalActive"
            :selected-url="''"
        />

        <app-confirmation-modal
            :title="promptTitle"
            :message="promptMessage"
            :modal-class="modalClass"
            :icon="promptIcon"
            v-if="confirmationModalActive"
            modal-id="app-confirmation-modal"
            @confirmed="triggerConfirm"
            @cancelled="cancelled"
            :loading="loading"
            :self-close="false"
        />

        <app-employment-status-modal
            v-if="employmentStatusModalActive"
            v-model="employmentStatusModalActive"
            :id="employeeId"
        />

        <app-employee-termination-reason-modal
            v-if="isTerminationReasonModalActive"
            v-model="isTerminationReasonModalActive"
            :id="employeeId"
        />

        <employee-context-menu
            v-if="isContextMenuOpen"
            :employees="selectedEmployees"
            @close="isContextMenuOpen = false"
        />

        <app-attendance-create-edit-modal
            v-if="attendanceModalActive"
            v-model="attendanceModalActive"
            :employee="employee"
        />

        <app-leave-create-edit-modal
            v-if="leaveModalActive"
            v-model="leaveModalActive"
            :employee="employee"
        />

        <job-history-edit-modal
            v-if="isJobHistoryEditModalActive"
            v-model="isJobHistoryEditModalActive"
            :modalType="modalAction"
            :employee="employee"
            @reload="reloadEmployeeTable"
        />

        <plan-modal 
            v-if="isPlanModalOpen"
            :current-plan-id="subscriptionData?.plan?.id"
            @plan-selected="handlePlanSelected"
            @close="isPlanModalOpen = false"
        />
    </div>
</template>

<script>
import EmployeeMixin from "../../Mixins/EmployeeMixin";
import EmployeeContextMenu from "./Components/EmployeeContextMenu";
import JobHistoryEditModal from "./Components/JobHistory/components/JobHistoryEditModal";
import { axiosGet } from "../../../../common/Helper/AxiosHelper";
import EmployeeCreateEditModel from "./EmployeeCreateEditModal";
import PlanModal from "../Setting/Component/PlanModal.vue";
import { ACTIVE_SUBSCRIPTION } from "../../../Config/ApiUrl";

export default {
    components: {
        EmployeeContextMenu,
        JobHistoryEditModal,
        EmployeeCreateEditModel,
        PlanModal
    },
    mixins: [EmployeeMixin],
    mounted() {
        this.getSalaryRange();
        this.getTenantMembers();
    },
    data() {
        return {
            tenantMembers: 0,
            subscriptionData: null,
            isSubscriptionDataLoaded: false,
            isSalaryRangeLoaded: false,
            isPlanModalOpen: false
        };
    },
    computed: {
        isAllDataLoaded() {
            return this.isSubscriptionDataLoaded && this.isSalaryRangeLoaded;
        },
        isEmployeeLimitReached() {
            if (!this.subscriptionData || !this.subscriptionData.plan) {
                return false;
            }
            return this.tenantMembers >= this.subscriptionData.plan.employee_limit;
        },
        employeeLimit() {
            return this.subscriptionData?.plan?.employee_limit || 0;
        },
        remainingEmployeeSlots() {
            return Math.max(0, this.employeeLimit - this.tenantMembers);
        }
    },
    methods: {
        getSalaryRange() {
            axiosGet(this.apiUrl.SALARY_RANGE).then(({ data }) => {
                let salaryFilter = this.options.filters.find(item => item.key === 'salary');
                salaryFilter.maxRange = data.max_salary;
                salaryFilter.minRange = data.min_salary < data.max_salary ? data.min_salary : 0;
                this.isSalaryRangeLoaded = true;
            }).catch(() => {
                this.isSalaryRangeLoaded = true;
            });
        },
        getTenantMembers() {
            axiosGet(ACTIVE_SUBSCRIPTION).then(({ data }) => {
                this.subscriptionData = data.data;
                this.tenantMembers = data.tenant_members;
                this.isSubscriptionDataLoaded = true;
            }).catch(() => {
                this.isSubscriptionDataLoaded = true;
            });
        },
        handleUpgradePlan() {
            window.open('https://corehrm.ph/', '_blank');
        },
        handlePlanSelected(plan) {
            console.log("Plan selected:", plan);
            this.isPlanModalOpen = false;
            this.getTenantMembers(); // Refresh the limit info
        }
    }
}
</script>
