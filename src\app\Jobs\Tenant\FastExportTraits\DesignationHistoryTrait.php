<?php

namespace App\Jobs\Tenant\FastExportTraits;

use App\Helpers\Traits\DateTimeHelper;
use App\Models\Tenant\Employee\DesignationUser;

trait DesignationHistoryTrait
{
    use DateTimeHelper;
    // Define DesignationHistory generator function
    public function DesignationHistoryDataGenerator(): \Generator
    {
        foreach (
            DesignationUser::query()
                ->select(['start_date', 'end_date'])
                ->selectSub(function ($query) {
                    $query->select('name')
                        ->from('designations')
                        ->whereColumn('designations.id', 'designation_user.designation_id')
                        ->limit(1);
                }, 'designation')
                ->selectSub(function ($query) {
                    $query->select('email')
                        ->from('users')
                        ->whereColumn('users.id', 'designation_user.user_id')
                        ->limit(1);
                }, 'user')
                ->cursor() as $row) {
            $row->sheet_name = 'designation_history';
            yield $row;
        }
    }

    // Define DesignationHistory map function
    public function designationHistoryMap($row): array {
        return [
            'User' => $row->user,
            'Designation' => $row->designation,
            'Start_date' => $this->getDesignationHistoryStartDate($row),
            'End_date' => $this->getDesignationHistoryEndDate($row),
        ];
    }

    // mapping helper functions
    private function getDesignationHistoryStartDate($row) {
        if(isset($row->start_date)) {
            return $this->carbon($row->start_date)->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
    private function getDesignationHistoryEndDate($row) {
        if(isset($row->end_date)) {
            return $this->carbon($row->end_date)->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
}