<template>
    <app-overlay-loader v-if="preloader"/>
    <form v-else @submit.prevent="submitData" ref="form" :data-url="apiUrl.ATTENDANCE_SETTINGS">
        <app-form-group
            :label="$t('Enable Biometric Device')"
            v-model="formData.enable_biometric_device"
            page="page"
            type="switch"
            :error-message="$errorMessage(errors, 'auto_approval')"
            label-alignment="top">
            <template slot="suggestion">
                {{ biometric_suggestion }}
            </template>
        </app-form-group>
        <div v-show="formData.enable_biometric_device">
            <div class="row mt-6">
                <div class="col-3">
                    <label>Device ID</label><br/>
                    <small class="text-muted font-italic">Manage biometric device ID</small>
                </div>
                <div class="col-9">
                    <app-input
                        type="text"
                        v-model="formData.biometric_device_id"
                        :placeholder="'Enter the device ID'"
                    />
                </div>
            </div>
            <div class="row mt-6">
                <div class="col-3">
                    <label>Device name</label>
                </div>
                <div class="col-9">
                    <app-input
                        type="text"
                        v-model="formData.biometric_device_name"
                        :placeholder="'Enter the device name'"
                    />
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-3"></div>
                <div class="col-9">
                    <app-submit-button :label="$t('save')" :loading="loading" @click="submitData"/>
                </div>
            </div>
        </div>
        <!-- <div v-show="formData.enable_biometric_device">
            <div class="row mt-6">
                <div class="col-3">
                    <label>{{ $t('manage_audience') }}</label><br/>
                    <small class="text-muted font-italic">{{ $t('manage_audience_message') }}</small>
                </div>
                <div class="col-9">
                    <app-form-group-selectable
                        type="multi-select"
                        :label="$t('by_role')"
                        list-value-field="name"
                        v-model="formData.roles"
                        :error-message="$errorMessage(errors, 'roles')"
                        :fetch-url="apiUrl.TENANT_SELECTABLE_ROLES"
                        :chooseAll="true"
                    />

                    <app-form-group-selectable
                        type="multi-select"
                        :label="$t('by_user')"
                        list-value-field="full_name"
                        v-model="formData.users"
                        :error-message="$errorMessage(errors, 'users')"
                        :fetch-url="apiUrl.TENANT_SELECTABLE_ATTENDANCE_USERS"
                        :chooseAll="true"
                    />
                </div>
            </div>
            <div class="row">
                <div class="col-3"></div>
                <div class="col-9">
                    <app-submit-button :label="$t('save')" :loading="loading" @click="submitData"/>
                </div>
            </div>
        </div> -->
    </form>
</template>

<script>
import FormHelperMixins from "../../../../../common/Mixin/Global/FormHelperMixins";
import {axiosGet} from "../../../../../common/Helper/AxiosHelper";

export default {
    name: "BiometricSettings",
    mixins: [FormHelperMixins],
    data() {
        return {
            formData: {
                biometric_device_id: "",
                biometric_device_name: "",
                enable_biometric_device: false
            },
            biometric_suggestion: this.$t('enabled_biometric_suggestion')
        }
    },
    methods: {
        afterSuccess(response) {
            this.toastAndReload(response.data.message)
            console.log('attendance preference settings')
        },
        getSetting() {
            this.preloader = true;
            axiosGet(this.apiUrl.ATTENDANCE_SETTINGS).then(({data}) => {
                console.log(data)
                this.formData = {
                    biometric_device_id: data.biometric_device_id,
                    biometric_device_name: data.biometric_device_name,
                    enable_biometric_device: Boolean(parseInt(data.enable_biometric_device))
                }
            }).finally(() => {
                this.preloader = false;
            })
        }
    },
    mounted() {
        this.getSetting();
    },
    watch: {
        'formData.enable_biometric_device': {
            handler: function (value) {
                if (!value) {
                    this.formData = {
                        biometric_device_id: "",
                        biometric_device_name: "",
                        enable_biometric_device: false
                    }
                    this.submitData();
                    return this.biometric_suggestion = this.$t('enabled_biometric_suggestion')
                }
                this.biometric_suggestion = this.$t('disabled_biometric_suggestion')
            }
        }
    }
}
</script>
