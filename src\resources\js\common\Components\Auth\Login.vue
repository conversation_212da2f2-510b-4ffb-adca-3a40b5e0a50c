<template>
    <div class="login-form d-flex align-items-center">
        <form class="sign-in-sign-up-form w-100" ref="form" data-url="admin/users/login" >
            <div class="text-center mb-4">
                <img :src="logoUrl" alt="" class="img-fluid logo">
            </div>
            <div class="form-row">
                <div class="form-group col-12">
                    <h6 class="text-center mb-0">{{ $t('hi', {object: $t('there')}) }}!</h6>
                    <label class="text-center d-block">{{ $t('login_to_your_dashboard') }}</label>
                </div>
            </div>

            <app-form-group
                type="select"
                v-if="demoCredentials"
                v-model="loginRole"
                :label="$t('login_as')"
                list-value-field="name"
                :list="selectableRole"
            />

            <app-form-group
                    :label="$t('email')"
                    type="email"
                    v-model="formData.email"
                    :placeholder="$placeholder('your', 'email')"
                    :error-message="$errorMessage(errors, 'email')"
            />

            <app-form-group
                    :label="$t('password')"
                    type="password"
                    v-model="formData.password"
                    :placeholder="$placeholder('your', 'password')"
                    :error-message="$errorMessage(errors, 'password')"
                    :show-password="true"
            />

            <div class="form-row">
                <div class="form-group col-12">
                    <app-submit-button
                        btn-class="d-inline-flex btn-block text-center btn-primary"
                        :label="$t('login')"
                        :loading="loading"
                        @click="submitData"
                    />
                </div>
            </div>

            <div class="form-row">
                <div class="col-6">
                    <app-input
                        class="mb-primary"
                        type="single-checkbox"
                        :list-value-field="$t('remember_me')"
                        v-model="formData.remember_me"
                    />
                </div>

                <div class="col-6 text-right">
                    <a :href="urlGenerator('/users/password-reset')" class="bluish-text">
                        <i data-feather="lock" class="pr-2"/>{{ $t('forget', {subject: $t('password')}) }}?
                    </a>
                </div>
            </div>

            <div class="form-group" v-if="demoCredentials">
                <div class="d-flex align-items-center justify-content-between">
                    <a :href="urlGenerator('privacy-policy')" target="_blank">{{ $t('privacy_policy') }}</a>
                    <a :href="urlGenerator('terms-and-condition')" target="_blank">{{ $t('terms_and_conditions') }}</a>
                </div>
                <div class="col-12">
                    <p class="text-center mt-5 footer-copy">
                        {{ $t('copyright') }} @ {{ new Date().getFullYear() }} {{ $t('by') }} {{ appName }}
                    </p>
                </div>
            </div>

            <!-- Subscription Plan Modal Trigger Button -->
            <div class="form-group text-center mt-4">
                <button type="button" class="btn view-subscription-btn" @click="showSubscriptionModal = true">
                    View Subscription Plans
                </button>
            </div>
        </form>

        <!-- Subscription Plan Modal -->
        <div v-if="showSubscriptionModal" class="subscription-modal-overlay" @click.self="showSubscriptionModal = false">
            <div class="subscription-modal">
                <button class="close-modal" @click="showSubscriptionModal = false">&times;</button>
                <subscription-plan />
            </div>
        </div>
    </div>
</template>

<script>
    import ThemeMixin from "../../Mixin/Global/ThemeMixin";
    import FormHelperMixins from "../../Mixin/Global/FormHelperMixins";

    export default {
        name: "Login",
        mixins: [ThemeMixin, FormHelperMixins],
        props: {
            logoUrl: {
                required: false
            },
            appName: {
                required: false
            },
            previousPage: {
                required: false
            },
            demo: {}
        },
        data() {
            return {
                loginRole: '',
                showSubscriptionModal: false
            }
        },
        methods: {
            submitData() {
                this.message = '';
                this.loading = true;
                this.save(this.formData);
            },

            afterSuccess({ data }) {
                window.location.href = data;
            },
            afterFinalResponse(){},
        },
        computed: {
            demoCredentials(){
                return this.demo ? JSON.parse(this.demo) : '';
            },
            selectableRole(){
                return [{ id: '', name: this.$t('select_role') }]
                    .concat(Object.keys(this.demoCredentials).map((item) => {
                        return {
                            id: item,
                            name: this.demoCredentials[item].role,
                        }
                    }))
            },
            loginRoleUpdate(){
                return this.loginRole
            }
        },
        watch: {
            loginRoleUpdate: {
                handler: function (role){
                    this.formData.password = role ? this.demoCredentials[role].password : '';
                    this.formData.email = role ? this.demoCredentials[role].email: '';
                }
            }
        }

    }
</script>

<style scoped>
    .subscription-modal-overlay {
        position: fixed;
        top: 0; left: 0; right: 0; bottom: 0;
        background: rgba(30, 32, 38, 0.85);
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .subscription-modal {
        background: #23272f;
        color: #fff;
        border-radius: 28px;
        max-width: 1200px;
        width: 98vw;
        min-width: 320px;
        min-height: 0;
        max-height: 96vh;
        overflow: visible;
        box-shadow: 0 8px 32px 0 rgba(0,0,0,0.25);
        position: relative;
        padding: 2.5rem 2rem 2rem 2rem;
        animation: modalPopIn 0.25s cubic-bezier(.22,1,.36,1);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        border: 3px solid #e53935;
        box-sizing: border-box;
        /* Glowing effect */
        box-shadow: 0 0 0 0 #e53935, 0 0 16px 2px #e53935, 0 0 32px 8px rgba(229,57,53,0.18);
        animation: modal-glow 2s infinite alternate;
    }
    .subscription-modal ::v-deep .subscription-plan-page {
        background: transparent !important;
        min-height: unset !important;
        box-shadow: none !important;
        padding: 0 !important;
        margin: 0 !important;
    }
    .subscription-modal ::v-deep .plans-header {
        margin-top: 0 !important;
        margin-bottom: 2.5rem !important;
    }
    .subscription-modal ::v-deep .plans-container {
        margin-bottom: 0 !important;
    }
    .subscription-modal ::v-deep .plan-card {
        margin-bottom: 0 !important;
    }
    .close-modal {
        position: absolute;
        top: 18px;
        right: 24px;
        background: none;
        border: none;
        color: #fff;
        font-size: 2.2rem;
        font-weight: bold;
        cursor: pointer;
        z-index: 2;
        transition: color 0.2s;
    }
    .close-modal:hover {
        color: #e53935;
    }
    @media (max-width: 900px) {
        .subscription-modal {
            max-width: 99vw;
            padding: 1.2rem 0.2rem 1.2rem 0.2rem;
        }
        .close-modal {
            top: 10px;
            right: 10px;
            font-size: 2rem;
        }
    }
    @media (max-width: 700px) {
        .subscription-modal {
            padding: 0.5rem 0.1rem 0.5rem 0.1rem;
            max-width: 100vw;
            width: 100vw;
            min-width: 0;
            border-radius: 0;
            max-height: 100vh;
            overflow-y: auto;
        }
        .subscription-modal ::v-deep .plans-header {
            padding: 0 0.5rem;
        }
        .subscription-modal ::v-deep .plans-title {
            font-size: 1.5rem !important;
            line-height: 1.2 !important;
        }
        .subscription-modal ::v-deep .plans-container {
            flex-direction: column !important;
            align-items: center !important;
            gap: 1.2rem !important;
        }
        .subscription-modal ::v-deep .plan-card {
            max-width: 98vw !important;
            width: 98vw !important;
            min-width: 0 !important;
            padding: 1.2rem 0.5rem 1rem 0.5rem !important;
            font-size: 0.98rem !important;
        }
        .close-modal {
            font-size: 2.2rem;
            top: 8px;
            right: 8px;
        }
    }

    /* Glowing effect for the View Subscription Plans button */
    .view-subscription-btn {
        border: 2px solid #2196f3;
        color: #2196f3;
        background: transparent;
        transition: box-shadow 0.3s, border-color 0.3s, color 0.3s;
        box-shadow: 0 0 8px 0 rgba(33,150,243,0.3);
        position: relative;
        font-weight: 500;
        font-size: 1.08rem;
    }
    .view-subscription-btn:hover, .view-subscription-btn:focus {
        color: #fff;
        background: #2196f3;
        border-color: #2196f3;
        box-shadow: 0 0 16px 2px #2196f3, 0 0 32px 8px rgba(33,150,243,0.25);
        outline: none;
    }
    @keyframes glow {
        0% { box-shadow: 0 0 8px 0 #2196f3, 0 0 0px 0 #2196f3; }
        50% { box-shadow: 0 0 16px 2px #2196f3, 0 0 32px 8px rgba(33,150,243,0.25); }
        100% { box-shadow: 0 0 8px 0 #2196f3, 0 0 0px 0 #2196f3; }
    }
    .view-subscription-btn {
        animation: glow 2s infinite alternate;
    }
    @keyframes modal-glow {
        0% {
            box-shadow: 0 0 0 0 #e53935, 0 0 8px 0 #e53935, 0 0 0px 0 #e53935;
        }
        50% {
            box-shadow: 0 0 0 0 #e53935, 0 0 16px 2px #e53935, 0 0 32px 8px rgba(229,57,53,0.18);
        }
        100% {
            box-shadow: 0 0 0 0 #e53935, 0 0 8px 0 #e53935, 0 0 0px 0 #e53935;
        }
    }
</style>
