{"name": "@gainhq/jobpoint", "author": "gainhq", "private": true, "scripts": {"dev": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "mix --production"}, "devDependencies": {"@coreui/coreui": "^2.1.12", "@fortawesome/fontawesome-free": "^5.9.0", "@vue/test-utils": "^1.0.0-beta.29", "axios": "^0.21.1", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.8.0", "bootstrap": "^4.3.1", "cross-env": "^6.0.3", "cross-var": "^1.1.0", "jest": "^24.8.0", "jquery": "^3.5.1", "laravel-mix": "^6.0.16", "laravel-mix-merge-manifest": "^0.1.2", "lodash": "^4.17.13", "pace": "github:HubSpot/pace#v1.0.2", "perfect-scrollbar": "^1.4.0", "popper.js": "^1.15.0", "postcss": "^8.3.6", "pusher-js": "^4.4.0", "resolve-url-loader": "^3.1.1", "sass": "1.32.*", "sass-loader": "^8.0.0", "sweetalert2": "^9.3.5", "vue": "^2.6.10", "vue-jest": "^3.0.4", "vue-loader": "^15.9.5", "vue-template-compiler": "^2.6.10"}, "browserslist": {"production": [">0.2%", ">2%, Firefox ESR", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"testURL": "http://localhost", "roots": ["<rootDir>/tests/Javascript/"], "moduleNameMapper": {"^@/(.*)$": "<rootDir>/resources/js/$1"}, "moduleFileExtensions": ["js", "vue"], "transform": {"^.+\\.js$": "babel-jest", ".*\\.(vue)$": "vue-jest"}}, "engines": {"node": ">=6.0.0"}, "dependencies": {"@babel/core": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.10.4", "@babel/preset-env": "^7.10.4", "@fullcalendar/core": "^5.5.0", "@fullcalendar/daygrid": "^5.5.0", "@fullcalendar/interaction": "^5.5.0", "@fullcalendar/timegrid": "^5.5.0", "@fullcalendar/vue": "^5.5.0", "accounting": "^0.4.1", "babel-loader": "^8.1.0", "chart.js": "^2.9.3", "dropzone": "5.7.0", "esm": "^3.2.25", "feather-icons": "^4.26.0", "fecha": "^4.1.0", "laravel-echo": "^1.8.1", "moment": "^2.24.0", "nouislider": "^14.1.1", "query-string": "^6.13.6", "v-calendar": "^2.1.1", "vue-chartjs": "^3.5.0", "vue-cookies": "^1.7.0", "vue-organization-chart": "^1.1.6", "vue-rangedate-picker": "^1.0.0", "vue-tel-input": "^4.3.0", "vue-toastr": "^2.1.2", "vuedraggable": "^2.23.2", "vuex": "^3.1.2", "vuex-i18n": "^1.13.1", "vue-json-pretty": "^1.8.1", "vue-recaptcha": "^1.3.0", "animate.css": "^4.0.0", "@popperjs/core": "^2.10.1", "acorn": "^8.5.0"}}