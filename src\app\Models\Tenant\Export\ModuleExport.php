<?php

namespace App\Models\Tenant\Export;

use App\Helpers\Core\Traits\FileHandler;
use App\Models\Core\Traits\CreatedByRelationship;
use App\Models\Core\Traits\StatusRelationship;
use App\Models\Tenant\TenantModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Casts\Attribute;

class ModuleExport extends TenantModel
{
    use HasFactory, StatusRelationship, CreatedByRelationship, FileHandler;

    protected $fillable = ['created_by', 'status_id', 'requested_modules', 'completed_modules', 'requested_on', 'completed_on', 'path', 'data', 'tenant_id'];

    protected $appends = ['progress'];

    protected $casts = [
        'requested_modules' => 'array',
        'completed_modules' => 'array',
        'data' => 'array',
    ];

    protected function fullUrl(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                $file_system = config('filesystems.default');

                $path = $this->removeStorage($this->path);

                if (in_array($file_system, ['local', 'public'])) {
                    return request()->root() . (Storage::disk($file_system)->url($path));
                }
                return Storage::disk($file_system)->url($path);
            },
        );
    }
    protected function progress(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                return round(((count($this->completed_modules) / count($this->requested_modules)) * 100), 2);
            },
        );
    }
}
