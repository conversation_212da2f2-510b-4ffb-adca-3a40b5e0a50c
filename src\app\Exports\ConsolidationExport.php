<?php

namespace App\Exports;

use App\Models\Tenant\Payroll\ConsolidationReport;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ConsolidationExport implements WithMultipleSheets
{
    protected $reportId;

    public function __construct($reportId)
    {
        $this->reportId = $reportId;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $report = ConsolidationReport::findOrFail($this->reportId);
        
        $sheets = [];
        
        // Summary sheet
        $sheets[] = new ConsolidationSummarySheet($report);
        
        // Detailed sheets based on report type
        if ($report->report_type === 'departmental') {
            $sheets[] = new ConsolidationDepartmentalSheet($report);
        } elseif ($report->report_type === 'detailed') {
            $sheets[] = new ConsolidationDetailedSheet($report);
        }
        
        return $sheets;
    }
}

class ConsolidationSummarySheet implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $report;

    public function __construct($report)
    {
        $this->report = $report;
    }

    public function collection()
    {
        $data = $this->report->report_data;
        
        if ($this->report->report_type === 'departmental') {
            return collect($data)->map(function ($dept) {
                return (object) $dept;
            });
        }
        
        return collect([$data]);
    }

    public function headings(): array
    {
        if ($this->report->report_type === 'departmental') {
            return [
                'Department',
                'Employee Count',
                'Total Gross',
                'Total Net',
                'Average per Employee'
            ];
        }
        
        return [
            'Total Employees',
            'Total Payslips',
            'Total Gross',
            'Total Net',
            'Total Departments'
        ];
    }

    public function map($row): array
    {
        if ($this->report->report_type === 'departmental') {
            return [
                $row->department ?? 'Unassigned',
                $row->employee_count ?? 0,
                number_format($row->total_gross ?? 0, 2),
                number_format($row->total_net ?? 0, 2),
                $row->employee_count > 0 ? number_format(($row->total_net ?? 0) / $row->employee_count, 2) : '0.00'
            ];
        }
        
        return [
            $row->total_employees ?? 0,
            $row->total_payslips ?? 0,
            number_format($row->total_gross ?? 0, 2),
            number_format($row->total_net ?? 0, 2),
            $row->departments ?? 0
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFE2E2E2']
                ]
            ]
        ];
    }
}

class ConsolidationDepartmentalSheet implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $report;

    public function __construct($report)
    {
        $this->report = $report;
    }

    public function collection()
    {
        $data = $this->report->report_data;
        $employees = collect();
        
        foreach ($data as $dept) {
            if (isset($dept['employees'])) {
                foreach ($dept['employees'] as $employee) {
                    $employee['department'] = $dept['department'];
                    $employees->push((object) $employee);
                }
            }
        }
        
        return $employees;
    }

    public function headings(): array
    {
        return [
            'Department',
            'Employee Name',
            'Position',
            'Net Salary'
        ];
    }

    public function map($employee): array
    {
        return [
            $employee->department ?? 'Unassigned',
            $employee->name ?? 'N/A',
            $employee->position ?? 'N/A',
            number_format($employee->net_salary ?? 0, 2)
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFE2E2E2']
                ]
            ]
        ];
    }
}

class ConsolidationDetailedSheet implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $report;

    public function __construct($report)
    {
        $this->report = $report;
    }

    public function collection()
    {
        $data = $this->report->report_data;
        return collect($data)->map(function ($item) {
            return (object) $item;
        });
    }

    public function headings(): array
    {
        return [
            'Employee Name',
            'Department',
            'Position',
            'Period',
            'Gross Salary',
            'Net Salary',
            'Deductions'
        ];
    }

    public function map($item): array
    {
        return [
            $item->employee_name ?? 'N/A',
            $item->department ?? 'Unassigned',
            $item->position ?? 'N/A',
            $item->period ?? 'N/A',
            number_format($item->gross_salary ?? 0, 2),
            number_format($item->net_salary ?? 0, 2),
            number_format($item->deductions ?? 0, 2)
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFE2E2E2']
                ]
            ]
        ];
    }
}
