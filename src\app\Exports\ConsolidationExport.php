<?php

namespace App\Exports;

use App\Models\Tenant\Payroll\ConsolidationReport;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ConsolidationExport implements FromCollection, WithHeadings, WithMapping, WithStyles
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return ConsolidationReport::with(['creator', 'updater'])->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            'Report Name',
            'Period',
            'Total Employees',
            'Total Amount',
            'Average Per Employee',
            'Created By',
            'Created At',
            'Updated By',
            'Updated At'
        ];
    }

    /**
     * @param mixed $consolidation
     * @return array
     */
    public function map($consolidation): array
    {
        return [
            $consolidation->id,
            $consolidation->report_name,
            $consolidation->period,
            $consolidation->total_employees,
            $consolidation->formatted_total_amount,
            $consolidation->formatted_average_amount_per_employee,
            $consolidation->creator ? $consolidation->creator->full_name : 'N/A',
            $consolidation->formatted_created_at,
            $consolidation->updater ? $consolidation->updater->full_name : 'N/A',
            $consolidation->formatted_updated_at
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true]],
        ];
    }
}
