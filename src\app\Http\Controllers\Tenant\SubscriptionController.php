<?php
/**
 * Subscription Controller
 *
 * This controller handles all subscription related functionality.
 *
 * PHP version 8.1
 *
 * @category Controllers
 * @package  App\Http\Controllers\Tenant
 * <AUTHOR> Team <<EMAIL>>
 * @license  MIT https://opensource.org/licenses/MIT
 * @version  GIT: <git_id>
 * @link     https://example.com
 */

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Subscription;
use App\Models\Tenant\Plans;
use App\Models\Core\Auth\User;
use App\Models\Core\Setting\NotificationEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use App\Exceptions\GeneralException;
use App\Mail\RenewSubscriptionPlanRequest;
use App\Mail\PlanSubscriptionRequest;
use App\Models\Tenant\PlanRequest;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;

/**
 * Subscription Controller
 *
 * This controller handles all subscription related functionality.
 *
 * @category Controllers
 * @package  App\Http\Controllers\Tenant
 * <AUTHOR> Team <<EMAIL>>
 * @license  MIT https://opensource.org/licenses/MIT
 * @link     https://example.com
 */
class SubscriptionController extends Controller
{

    public function index()
    {
        $subscriptions = Subscription::with(['tenant', 'plan'])
            ->latest()
            ->paginate(request('per_page', 10));

        if (request()->wantsJson()) {
            return response()->json([
                'data' => $subscriptions->items(),
                'meta' => [
                    'current_page' => $subscriptions->currentPage(),
                    'last_page' => $subscriptions->lastPage(),
                    'per_page' => $subscriptions->perPage(),
                    'total' => $subscriptions->total(),
                ]
            ]);
        }

        return view(
            'tenant.subscription.index',
            compact('subscriptions')
        );
    }

    public function upgradePlan(Request $request)
    {
        $validated = $request->validate([
            'plan_id' => 'required|exists:plans,id',
        ]);
    
        try {
            DB::beginTransaction();
    
            $user = Auth::user();
    
            $subscription = Subscription::where('tenant_id', $user->id)->first();
    
            if (!$subscription) {
                throw new GeneralException('No active subscription found for this tenant.');
            }
    
            $selectedPlan = Plans::findOrFail($validated['plan_id']);
    
            // Prevent re-selecting the same plan
            if ($subscription->plan_id == $selectedPlan->id) {
                throw new GeneralException('You are already subscribed to this plan.');
            }
    
            // Initialize date values
            $startDate = now();
            $endDate = null;
            $renewalDate = null;
    
            // Determine duration based on plan ID or name
            switch ($selectedPlan->id) {
                case 1: // Monthly Plan
                    $endDate = $startDate->copy()->addMonth();
                    $renewalDate = $endDate->copy()->subDays(7);
                    break;
    
                case 2: // Yearly Plan
                    $endDate = $startDate->copy()->addYear();
                    $renewalDate = $endDate->copy()->subDays(14);
                    break;
    
                case 3: // Premium Plan - Lifetime
                    $endDate = null;
                    $renewalDate = null;
                    break;
    
                default:
                    throw new GeneralException('Invalid plan configuration.');
            }
    
            // Update the subscription
            $subscription->update([
                'plan_id' => $selectedPlan->id,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'renewal_date' => $renewalDate,
                'status' => 1 // Active
            ]);
    
            DB::commit();
    
            if ($request->wantsJson()) {
                return response()->json([
                    'message' => 'Plan upgraded successfully.',
                    'data' => $subscription->fresh(['tenant', 'plan'])
                ]);
            }
    
            return redirect()
                ->route('tenant.subscriptions.index')
                ->with('success', 'Plan upgraded successfully.');
    
        } catch (GeneralException $e) {
            DB::rollBack();
    
            if ($request->wantsJson()) {
                return response()->json([
                    'message' => $e->getMessage(),
                    'error' => $e->getMessage()
                ], 400);
            }
    
            return back()->with('error', $e->getMessage());
    
        } catch (\Exception $e) {
            DB::rollBack();
    
            if ($request->wantsJson()) {
                return response()->json([
                    'message' => 'Failed to upgrade plan.',
                    'error' => $e->getMessage()
                ], 500);
            }
    
            return back()->with('error', 'Failed to upgrade plan. ' . $e->getMessage());
        }
    }

    public function plan()
    {
        $user = Auth::user();
        $plans = Plans::all();
    
        $subscription = Subscription::with('plan')->where('tenant_id', $user->id)->first();
    
        $activePlan = $subscription?->plan ?? null;
    
        // Check for pending plan requests
        $pendingRequest = PlanRequest::with('plan')
            ->where('tenant_id', $user->id)
            ->where('status', 1) // pending status
            ->first();
    
        $response = [
            'data' => $plans,
            'activePlan' => $activePlan,
            'hasPendingRequest' => !is_null($pendingRequest),
        ];
    
        // If there's a pending request, include the details
        if ($pendingRequest) {
            $response['pendingRequest'] = [
                'id' => $pendingRequest->id,
                'plan_id' => $pendingRequest->plan_id,
                'plan_name' => $pendingRequest->plan->plan_name ?? null,
                'plan_type' => $pendingRequest->plan->plan_type ?? null,
                'type' => $pendingRequest->type, // upgrade/renewal/etc
                'requested_at' => $pendingRequest->created_at,
            ];
        }
    
        return response()->json($response);
    }
    
    public function store(Request $request)
    {
        $validated = $request->validate([
            'tenant_id' => 'required|exists:users,id',
            'plan_id' => 'required|exists:plans,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'renewal_date' => 'required|date|after:start_date',
            'status' => 'required|integer|in:1,2,3,4'  // 1: active, 2: pending, 3: expired, 4: canceled
        ]);
    
        try {
            DB::beginTransaction();
    
            // Check if tenant already has a subscription
            $existing = Subscription::where('tenant_id', $validated['tenant_id'])->first();
    
            if ($existing) {
                throw new \Exception('This tenant already has an active subscription.');
            }
    
            // Optional: map string statuses if needed
            if (!is_numeric($validated['status'])) {
                $statusMap = [
                    'active' => 1,
                    'pending' => 2,
                    'expired' => 3,
                    'canceled' => 4
                ];
                $validated['status'] = $statusMap[strtolower($validated['status'])] ?? 1;
            }
    
            $subscription = Subscription::create($validated);
    
            DB::commit();
    
            if ($request->wantsJson()) {
                return response()->json([
                    'message' => 'Subscription created successfully.',
                    'data' => $subscription->load(['tenant', 'plan'])
                ], 201);
            }
    
            return redirect()
                ->route('tenant.subscriptions.index')
                ->with('success', 'Subscription created successfully.');
                
        } catch (\Exception $e) {
            DB::rollBack();
    
            if ($request->wantsJson()) {
                return response()->json([
                    'message' => 'Failed to create subscription.',
                    'error' => $e->getMessage()
                ], 500);
            }
    
            return back()->with('error', 'Failed to create subscription. ' . $e->getMessage());
        }
    }

    public function show(Subscription $subscription)
    {
        $subscription->load(['tenant', 'plan']);
        
        if (request()->wantsJson()) {
            return response()->json([
                'data' => $subscription
            ]);
        }
        
        return view(
            'tenant.subscription.show',
            compact('subscription')
        );
    }

    public function update(Request $request, Subscription $subscription)
    {
        $validated = $request->validate([
            'end_date' => 'required|date|after:start_date',
            'renewal_date' => 'required|date|after:start_date',
        ]);

        try {
            DB::beginTransaction();
            
            $subscription->update([
                'end_date' => $validated['end_date'],
                'renewal_date' => $validated['renewal_date']
            ]);
            
            DB::commit();
            
            if ($request->wantsJson()) {
                return response()->json([
                    'message' => 'Subscription dates updated successfully.',
                    'data' => $subscription->fresh(['tenant', 'plan'])
                ]);
            }
            
            return redirect()
                ->route('tenant.subscriptions.index')
                ->with('success', 'Subscription dates updated successfully.');
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            if ($request->wantsJson()) {
                return response()->json([
                    'message' => 'Failed to update subscription dates.',
                    'error' => $e->getMessage()
                ], 500);
            }
            
            return back()->with('error', 'Failed to update subscription dates. ' . $e->getMessage());
        }
    }

    public function destroy(Subscription $subscription)
    {
        try {
            DB::beginTransaction();
            
            $subscription->delete();
            
            DB::commit();
            
            if (request()->wantsJson()) {
                return response()->json([
                    'message' => 'Subscription deleted successfully.'
                ]);
            }
            
            return redirect()
                ->route('tenant.subscriptions.index')
                ->with('success', 'Subscription deleted successfully.');
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            if (request()->wantsJson()) {
                return response()->json([
                    'message' => 'Failed to delete subscription.',
                    'error' => $e->getMessage()
                ], 500);
            }
            
            return back()->with('error', 'Failed to delete subscription. ' . $e->getMessage());
        }
    }
    
    public function activeSubscription()
    {
        $user = Auth::user();
    
        $subscription = Subscription::with('plan')
            ->where('tenant_id', $user->id)
            ->where('status', 1)
            ->first();
    
        $tenantMembers = User::where('is_in_employee', $user->id)
            ->where('status_id', 1)
            ->count();
    
        $limitChecker = ($tenantMembers > $subscription->plan->employee_limit);
    
        $today = Carbon::now()->startOfDay();
        $endDate = Carbon::parse($subscription->end_date)->startOfDay();
    
        // Days left until end of subscription
        $daysLeft = $today->diffInDays($endDate, false);
    
        // Renew alert if within 7 days from end
        $renewalChecker = $daysLeft >= 0 && $daysLeft <= 7;
    
        // Check if subscription is expired
        $isExpired = $today->greaterThanOrEqualTo($endDate);
    
        return response()->json([
            'data' => $subscription,
            'tenant_members' => $tenantMembers,
            'limit_checker' => $limitChecker,
            'renewal_checker' => $renewalChecker,
            'days_left' => $daysLeft,
            'is_expired' => $isExpired
        ]);
    }
    
    
    public function cancel(Subscription $subscription)
    {
        try {
            DB::beginTransaction();
            
            $subscription->update(['status' => 'canceled']);
            
            DB::commit();
            
            if (request()->wantsJson()) {
                return response()->json([
                    'message' => 'Subscription canceled successfully.',
                    'data' => $subscription->fresh(['plan'])
                ]);
            }
            
            return back()->with('success', 'Subscription canceled successfully.');
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            if (request()->wantsJson()) {
                return response()->json([
                    'message' => 'Failed to cancel subscription.',
                    'error' => $e->getMessage()
                ], 500);
            }
            
            return back()->with('error', 'Failed to cancel subscription. ' . $e->getMessage());
        }
    }

    //for upgrading the plan
    public function selectPlan(Request $request)
    {
        $user = Auth::user();
    
        $request->validate([
            'plan_id' => 'required|exists:plans,id',
        ]);
    
        $plan = Plans::findOrFail($request->plan_id);
    
        // ✅ Check if there's already a pending request for this plan
        $existingRequest = PlanRequest::where('tenant_id', $user->id)
            ->where('plan_id', $plan->id)
            ->where('type', 'upgrade')
            ->where('status', 1) // pending
            ->first();
    
        if ($existingRequest) {
            return response()->json([
                'message' => 'You already have a pending request for this plan.',
            ], 400);
        }
    
        // ✅ Log the request in the plan_requests table
        PlanRequest::create([
            'tenant_id' => $user->id,
            'plan_id' => $plan->id,
            'type' => 'upgrade',
            'status' => 1, // pending
        ]);
    
        $data = [
            'tenant_name' => $user->full_name ?? $user->name,
            'tenant_email' => $user->email,
            'plan' => [
                'plan_name' => $plan->plan_name,
                'plan_type' => $plan->plan_type,
                'price' => $plan->price,
            ],
            'requested_at' => now()->toDayDateTimeString(),
        ];
    
        $adminEmail = '<EMAIL>';
    
        // 📝 Log before sending
        \Log::info('Sending Plan Subscription Request Email with the following data:', $data);
    
        try {
            Mail::to($adminEmail)->send(new PlanSubscriptionRequest($data));
    
            \Log::info("Plan subscription request email sent to: {$adminEmail}");
    
            return response()->json([
                'message' => 'Plan request sent successfully.',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            \Log::error("Failed to send plan request email: " . $e->getMessage());
            \Log::error('Failed email data: ' . json_encode($data));
    
            return response()->json([
                'message' => 'Failed to send plan request email.',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    public function renew(Request $request)
    {
        try {
            DB::beginTransaction();
    
            $user = Auth::user();
    
            $subscription = Subscription::where('tenant_id', $user->id)
                ->where('status', 1)
                ->first(['id', 'plan_id', 'start_date', 'end_date']);
    
            if (!$subscription) {
                throw new GeneralException('No active subscription found for this tenant.');
            }
    
            $plan = Plans::findOrFail($subscription->plan_id);
    
            // ✅ Create Renewal Request (Pending Approval by Admin)
            PlanRequest::create([
                'tenant_id' => $user->id,
                'plan_id' => $plan->id,
                'type' => 'renewal',
                'status' => 1, // Pending
            ]);
    
            DB::commit();
    
            // ✅ Prepare and Send Email Notification
            $mailData = [
                'tenant_name' => $user->full_name ?? $user->name,
                'tenant_email' => $user->email,
                'plan' => [
                    'plan_name' => $plan->plan_name,
                    'plan_type' => $plan->plan_type,
                    'price' => $plan->price,
                ],
                'requested_at' => now()->toDayDateTimeString(),
                'action' => 'renewal',
            ];
    
            Mail::to('<EMAIL>')
                ->send(new RenewSubscriptionPlanRequest($mailData));
    
            return response()->json([
                'message' => 'Renewal request submitted successfully.',
                'data' => $subscription->fresh(['tenant', 'plan']),
            ]);
    
        } catch (GeneralException $e) {
            DB::rollBack();
            return response()->json(['message' => $e->getMessage()], 400);
    
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Failed to submit renewal request.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
