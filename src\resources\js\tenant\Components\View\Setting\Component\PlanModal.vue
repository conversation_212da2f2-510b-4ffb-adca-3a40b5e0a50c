<template>
  <div class="modal fade" :id="modalId" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title font-weight-semibold">Choose a Plan</h3>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">
              <i class="fas fa-times"></i>
            </span>
          </button>
        </div>

        <div class="modal-body">
          <div v-if="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="sr-only">Loading...</span>
            </div>
          </div>

          <!-- Pending Request Component -->
          <PendingRequestAlert
            v-else-if="hasPendingRequest"
            :pending-request="pendingRequest"
            :show-actions="true"
            :show-refresh-button="true"
            @refresh="refreshPlans"
          />

          <!-- Plans Display (only when no pending request) -->
          <div v-else class="row">
            <div 
              v-for="(plan, index) in plans" 
              :key="index"
              class="col-md-4 mb-4"
            >
              <div 
                class="card h-100 plan-card"
                :class="{
                  'current-plan': isCurrentPlan(plan.id),
                }"
              >
                <!-- Current Plan Badge -->
                <div v-if="isCurrentPlan(plan.id)" class="current-badge">
                  <i class="fas fa-check-circle me-1"></i>
                  Current Plan
                </div>

                <div class="card-header text-center my-6">
                  <h4 class="my-2">{{ plan.plan_name }}</h4>
                  <span class="badge badge-secondary">{{ plan.plan_type }}</span>
                  <h2 class="display-5 my-3">
                    <h2 class="display-5" v-if="parseFloat(plan.price) === 0">₱ Premium</h2>
                    <h2 class="display-5" v-else>₱{{ plan.price }}</h2>
                  </h2>
                  <h6 class="text-secondary mb-0">Up to {{ plan.employee_limit }} employees</h6>
                </div>

                <div class="card-body">
                  <p class="text-muted text-center mb-4">{{ plan.plan_description }}</p>
                  <ul class="list-unstyled">
                    <li class="mb-2" v-for="(feature, i) in plan.features" :key="i">
                      <i class="fas fa-check text-success mr-2"></i> {{ feature }}
                    </li>
                  </ul>
                </div>

                <div class="card-footer bg-transparent mb-2 border-top-0 text-center" v-if="!isCurrentPlan(plan.id)">
                  <button 
                    @click="selectPlan(plan)"
                    class="btn btn-block btn-primary"
                  >
                    Select Plan
                  </button>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { axiosGet, axiosPost } from "../../../../../common/Helper/AxiosHelper";
import { PLANS, SELECT_PLAN } from "../../../../Config/ApiUrl";
import PendingRequestAlert from "../../../../../common/Components/Helper/PendingRequestAlert.vue"; // Adjust the path as needed

export default {
  name: "PlanModal",
  components: {
    PendingRequestAlert,
},
  props: {
    modalId: {
      type: String,
      default: 'planModal',
      required: false
    },
    currentPlanId: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      loading: false,
      plans: [],
      activePlan: null,
      hasPendingRequest: false,
      pendingRequest: null,
      error: null,
      hasLoadedPlans: false // Flag to track if plans have been loaded
    };
  },
  methods: {
    async fetchPlans() {
      this.loading = true;
      try {
        const response = await axiosGet(PLANS);
        
        if (response.data) {
          this.plans = response.data.data || [];
          this.activePlan = response.data.activePlan || null;
          this.hasPendingRequest = response.data.hasPendingRequest || false;
          this.pendingRequest = response.data.pendingRequest || null;
        }
        
        if (Array.isArray(this.plans)) {
          this.plans.forEach((plan, index) => {
            // Any additional processing for plans
          });
        } else {
          console.log('Plans is not an array:', this.plans);
        }
        
        this.hasLoadedPlans = true; // Mark as loaded
        
      } catch (error) {
        this.error = error.response?.data?.message || 'Failed to load plans';
        this.$toastr.e('', this.error);
      } finally {
        this.loading = false;
      }
    },
    isCurrentPlan(planId) {
      // Check against currentPlanId prop first, then activePlan from API
      if (this.currentPlanId) {
        const result = parseInt(planId) === parseInt(this.currentPlanId);
        return result;
      }
      if (this.activePlan) {
        const result = parseInt(planId) === parseInt(this.activePlan.id);
        return result;
      }
      return false;
    },
    async selectPlan(plan) {
        try {
            this.loading = true;
            // Assuming you have an endpoint to handle plan selection and email
            const response = await axiosPost(SELECT_PLAN, {
                plan_id: plan.id,
            });
            
            this.$emit('plan-selected', plan);
            this.$toastr.s('Plan selection request sent successfully! Our team will contact you shortly.');
        } catch (error) {
            this.$toastr.e('Failed to process plan selection. Please try again.');
        } finally {
            this.loading = false;
            $(`#${this.modalId}`).modal('hide');
        }
    },
    // Method called by parent to show modal and fetch plans if needed
    async showModal() {
      if (!this.hasLoadedPlans) {
        await this.fetchPlans();
      }
      $(`#${this.modalId}`).modal('show');
    },
    hide() {
      $(`#${this.modalId}`).modal('hide');
    },
    // Method to manually refresh plans (if needed)
    refreshPlans() {
      this.hasLoadedPlans = false;
      this.fetchPlans();
    }
  }
};
</script>

<style scoped>
.plan-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 6px;
  position: relative;
}

.plan-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.current-plan {
  border-color: #28a745;
}

.current-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #28a745;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  z-index: 10;
}

.card-header {
  border-radius: 10px 10px 0 0;
  margin: 2rem 0rem;
  background-color: transparent !important;
  border-bottom: none !important;
}
</style>