<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Core\Auth\Permission;
use App\Models\Core\Auth\Type;

class PayrollPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the tenant type (based on existing seeders)
        $permissionType = Type::findByAlias('tenant');

        if (!$permissionType) {
            $this->command->error('Permission type not found. Please ensure the types table is seeded.');
            return;
        }

        $permissions = [
            // Transmittal permissions
            ['name' => 'view_transmittal', 'group_name' => 'payroll'],
            ['name' => 'generate_transmittal', 'group_name' => 'payroll'],
            ['name' => 'export_transmittal', 'group_name' => 'payroll'],
            ['name' => 'delete_transmittal', 'group_name' => 'payroll'],

            // Signature permissions
            ['name' => 'view_signature', 'group_name' => 'payroll'],
            ['name' => 'manage_signatures', 'group_name' => 'payroll'],

            // Consolidation permissions
            ['name' => 'view_consolidation', 'group_name' => 'payroll'],
            ['name' => 'generate_consolidation', 'group_name' => 'payroll'],
            ['name' => 'export_consolidation', 'group_name' => 'payroll'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate([
                'name' => $permission['name'],
                'type_id' => $permissionType->id,
                'group_name' => $permission['group_name']
            ]);
        }

        $this->command->info('Payroll permissions created successfully!');
    }
}
