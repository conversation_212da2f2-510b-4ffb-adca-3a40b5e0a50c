{"name": "gainhq/installer", "description": "User interface install ui", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"Tests\\": "tests/", "Gainhq\\Installer\\": "src/"}}, "require": {"php": "^8.1", "altek/eventually": "^2.0", "aws/aws-sdk-php": "^3.171", "erusev/parsedown": "^1.7", "intervention/image": "^2.7", "spatie/laravel-activitylog": "^4.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "require-dev": {"orchestra/testbench": "6.x-dev"}, "extra": {"laravel": {"providers": ["Gainhq\\Installer\\InstallerServiceProvider"]}}}