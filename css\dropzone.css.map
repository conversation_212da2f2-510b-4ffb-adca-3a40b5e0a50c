{"version": 3, "file": "css/dropzone.css", "mappings": "AA8BE,2BAqBA,GACE,UARA,0BAoBF,CARA,QACE,UAbA,uBA4BF,CAXA,GACE,UAlBA,2BAoCF,CACF,CAnDE,oBAwCA,GACE,UA3BA,0BAkFF,CAnDA,IACE,UAhCA,uBA0FF,CACF,CAzGE,iBAuDA,GAzCE,kBAiJF,CAvGA,IA1CE,oBAwJF,CA7GA,IA3CE,kBA+JF,CACF,CA/GA,sBACE,qBAiHF,CA/GA,UAqBE,gBADA,2BADA,iBAGA,YAgGF,CAhHE,uBACE,cAkHJ,CAhHI,yBACE,cAkHN,CA/GM,wEACE,cAiHR,CAtGI,iCACE,YAwGN,CApGE,wBACE,kBAsGJ,CArGI,oCACE,UAuGN,CApGE,sBAEE,aADA,iBAuGJ,CApGI,iCACE,gBAEA,YADA,cAIA,eADA,aAEA,gBAHA,SAyGN,CAhGE,sBAEE,qBAIA,YACA,iBANA,kBAGA,kBAmGJ,CA9FI,4BAEE,YA+FN,CAvFM,gDAEE,gBACA,6CAFA,kBA8FR,CAzFM,kDACE,SA2FR,CAvFI,uCACE,eAyFN,CAxFM,mDAzIF,6BAwOJ,CA1FI,iCAKE,YADA,eADA,cAFA,eACA,iBA+FN,CA3FM,uCACE,yBA6FR,CAzFI,wCACE,SA2FN,CAzFI,kCAgBE,gBALA,eAJA,OAaA,iBAPA,eADA,eAHA,UAKA,gBATA,kBAUA,kBATA,MAHA,UAoGN,CAjFM,2CAEE,eADA,iBAoFR,CAhFM,+CAEE,kBAiFR,CA9EU,0DAEE,uBADA,0BAiFZ,CA7EQ,2DAIE,gBACA,sBA4EV,CAhFU,gEACE,sBAkFZ,CAzEQ,oGACE,uBAEA,kBADA,cA4EV,CAlEQ,0CAxNJ,sCAmSJ,CArEI,gCACE,kBAvKkB,CA4KlB,cAFA,YA5KS,CA0KT,gBAGA,kBAFA,WA3KS,CA+KT,UAuEN,CArEM,oCACE,aAuER,CAjEM,kDA9OF,uDAsTJ,CAnEM,8CAnPF,iDAoPI,SA0ER,CApEI,4EAWE,cAEA,SACA,kBACA,iBARA,UAFA,oBAKA,kBAEA,QAJA,WAuEN,CA9DM,oFACE,cAEA,WAlBa,CAiBb,UAiER,CA3DI,iDACE,UApRF,yBAsVJ,CA/DI,+CACE,UAxRF,8BA8VJ,CAjEM,uDA7RF,gCAqWJ,CApEI,mCAgBE,qBAMA,kBAfA,YACA,SAKA,kBAHA,gBARA,UAsBA,gBAnBA,oBACA,kBAGA,QAUA,2BAPA,WATA,YAiFN,CA1DM,8CACE,gBACA,6CAIA,SADA,OAFA,kBACA,MA/TJ,iCAkUI,OAiER,CA1DM,iDACE,aA4DR,CA1DM,uDACE,UACA,mBA4DR,CAxDI,wCAgBE,kBAdQ,CAeR,mDANA,kBAQA,WAZA,cACA,aAIA,eAEA,WALA,UASA,mBAdA,oBAEA,kBAOA,UA/VF,4BAiWE,WAdQ,CAIR,YAyEN,CAxDM,8CASE,gCAFA,4BACA,6BAPA,WAKA,SAFA,UAFA,kBACA,SAEA,OA8DR,C", "sources": ["webpack://@gainhq/payday/./dropzone.scss"], "sourcesContent": ["/*\n * The MIT License\n * Copyright (c) 2012 <PERSON><PERSON>o <<EMAIL>>\n */\n\n// Permission is hereby granted, free of charge, to any person obtaining a copy of\n// this software and associated documentation files (the \"Software\"), to deal in\n// the Software without restriction, including without limitation the rights to\n// use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\n// of the Software, and to permit persons to whom the Software is furnished to do\n// so, subject to the following conditions:\n\n// The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n// SOFTWARE.\n\n@mixin keyframes($name) {\n  @-webkit-keyframes #{$name} {\n    @content; \n  }\n  @-moz-keyframes #{$name} {\n    @content;\n  }\n  @keyframes #{$name} {\n    @content;\n  } \n}\n\n\n@mixin prefix($map, $vendors: webkit moz ms o) {\n  @each $prop, $value in $map {\n    @if $vendors {\n      @each $vendor in $vendors {\n        #{\"-\" + $vendor + \"-\" + $prop}: #{$value};\n      }\n    }\n    // Dump regular property anyway\n    #{$prop}: #{$value};\n  }\n}\n\n\n@include keyframes(passing-through) {\n  \n  0% {\n    opacity: 0;\n    @include prefix((transform: translateY(40px)));\n  }\n\n  30%, 70% {\n    opacity: 1;\n    @include prefix((transform: translateY(0px)));\n  }\n\n  100% {\n    opacity: 0;\n    @include prefix((transform: translateY(-40px)));\n  }\n}\n\n\n@include keyframes(slide-in) {\n  \n  0% {\n    opacity: 0;\n    @include prefix((transform: translateY(40px)));\n  }\n\n  30% {\n    opacity: 1;\n    @include prefix((transform: translateY(0px)));\n  }\n}\n\n\n\n@include keyframes(pulse) {\n  \n  0% { @include prefix((transform: scale(1))); }\n  10% { @include prefix((transform: scale(1.1))); }\n  20% { @include prefix((transform: scale(1))); }\n\n}\n\n\n\n.dropzone, .dropzone * {\n  box-sizing: border-box;\n}\n.dropzone {\n\n  $image-size: 120px;\n\n  $image-border-radius: 20px;\n\n  &.dz-clickable {\n    cursor: pointer;\n\n    * {\n      cursor: default;\n    }\n    .dz-message {\n      &, * {\n        cursor: pointer;\n      }\n    }\n  }\n\n  min-height: 150px;\n  border: 2px solid rgba(0, 0, 0, 0.3);\n  background: white;\n  padding: 20px 20px;\n\n  &.dz-started {\n    .dz-message {\n      display: none;\n    }\n  }\n\n  &.dz-drag-hover {\n    border-style: solid;\n    .dz-message {\n      opacity: 0.5;\n    }\n  }\n  .dz-message {\n    text-align: center;\n    margin: 2em 0;\n\n    .dz-button {\n      background: none;\n      color: inherit;\n      border: none;\n      padding: 0;\n      font: inherit;\n      cursor: pointer;\n      outline: inherit;\n    }\n  }\n\n\n\n  .dz-preview {\n    position: relative;\n    display: inline-block;\n\n    vertical-align: top;\n\n    margin: 16px;\n    min-height: 100px;\n\n    &:hover {\n      // Making sure that always the hovered preview element is on top\n      z-index: 1000;\n      .dz-details {\n        opacity: 1;\n      }\n    }\n\n    &.dz-file-preview {\n\n      .dz-image {\n        border-radius: $image-border-radius;\n        background: #999;\n        background: linear-gradient(to bottom, #eee, #ddd);\n      }\n\n      .dz-details {\n        opacity: 1;\n      }\n    }\n\n    &.dz-image-preview {\n      background: white;\n      .dz-details {\n        @include prefix((transition: opacity 0.2s linear));\n      }\n    }\n\n    .dz-remove {\n      font-size: 14px;\n      text-align: center;\n      display: block;\n      cursor: pointer;\n      border: none;\n      &:hover {\n        text-decoration: underline;\n      }\n    }\n\n    &:hover .dz-details {\n      opacity: 1;\n    }\n    .dz-details {\n      $background-color: #444;\n\n      z-index: 20;\n\n      position: absolute;\n      top: 0;\n      left: 0;\n\n      opacity: 0;\n\n      font-size: 13px;\n      min-width: 100%;\n      max-width: 100%;\n      padding: 2em 1em;\n      text-align: center;\n      color: rgba(0, 0, 0, 0.9);\n\n      $width: 120px;\n\n      line-height: 150%;\n\n      .dz-size {\n        margin-bottom: 1em;\n        font-size: 16px;\n      }\n\n      .dz-filename {\n\n        white-space: nowrap;\n\n        &:hover {\n          span {\n            border: 1px solid rgba(200, 200, 200, 0.8);\n            background-color: rgba(255, 255, 255, 0.8);\n          }\n        }\n        &:not(:hover) {\n          span {\n            border: 1px solid transparent;\n          }\n          overflow: hidden;\n          text-overflow: ellipsis;\n        }\n\n      }\n\n      .dz-filename, .dz-size {\n        span {\n          background-color: rgba(255, 255, 255, 0.4);\n          padding: 0 0.4em;\n          border-radius: 3px;\n        }\n      }\n\n    }\n\n    &:hover {\n      .dz-image {\n        // opacity: 0.8;\n        img {\n          @include prefix((transform: scale(1.05, 1.05))); // Getting rid of that white bleed-in\n          @include prefix((filter: blur(8px)), webkit); // Getting rid of that white bleed-in\n        }\n      }\n    }\n    .dz-image {\n      border-radius: $image-border-radius;\n      overflow: hidden;\n      width: $image-size;\n      height: $image-size;\n      position: relative;\n      display: block;\n      z-index: 10;\n\n      img {\n        display: block;\n      }\n    }\n\n\n    &.dz-success {\n      .dz-success-mark {\n        @include prefix((animation: passing-through 3s cubic-bezier(0.770, 0.000, 0.175, 1.000)));\n      }\n    }\n    &.dz-error {\n      .dz-error-mark {\n        opacity: 1;\n        @include prefix((animation: slide-in 3s cubic-bezier(0.770, 0.000, 0.175, 1.000)));\n      }\n    }\n\n\n    .dz-success-mark, .dz-error-mark {\n\n      $image-height: 54px;\n      $image-width: 54px;\n\n      pointer-events: none;\n\n      opacity: 0;\n      z-index: 500;\n\n      position: absolute;\n      display: block;\n      top: 50%;\n      left: 50%;\n      margin-left: -($image-width/2);\n      margin-top: -($image-height/2);\n\n      svg {\n        display: block;\n        width: $image-width;\n        height: $image-height;\n      }\n    }\n    \n      \n    &.dz-processing .dz-progress {\n      opacity: 1;\n      @include prefix((transition: all 0.2s linear));\n    }\n    &.dz-complete .dz-progress {\n      opacity: 0;\n      @include prefix((transition: opacity 0.4s ease-in));\n    }\n\n    &:not(.dz-processing) {\n      .dz-progress {\n        @include prefix((animation: pulse 6s ease infinite));\n      }\n    }\n    .dz-progress {\n\n      opacity: 1;\n      z-index: 1000;\n\n      pointer-events: none;\n      position: absolute;\n      height: 16px;\n      left: 50%;\n      top: 50%;\n      margin-top: -8px;\n\n      width: 80px;\n      margin-left: -40px;\n\n      // border: 2px solid #333;\n      background: rgba(255, 255, 255, 0.9);\n\n      // Fix for chrome bug: https://code.google.com/p/chromium/issues/detail?id=157218\n      -webkit-transform: scale(1);\n\n\n      border-radius: 8px;\n\n      overflow: hidden;\n\n      .dz-upload {\n        background: #333;\n        background: linear-gradient(to bottom, #666, #444);\n        position: absolute;\n        top: 0;\n        left: 0;\n        bottom: 0;\n        width: 0;\n        @include prefix((transition: width 300ms ease-in-out));\n      }\n\n    }\n\n    &.dz-error {\n      .dz-error-message {\n        display: block;\n      }\n      &:hover .dz-error-message {\n        opacity: 1;\n        pointer-events: auto;\n      }\n    }\n\n    .dz-error-message {\n      $width: $image-size + 20px;\n      $color: rgb(190, 38, 38);\n\n      pointer-events: none;\n      z-index: 1000;\n      position: absolute;\n      display: block;\n      display: none;\n      opacity: 0;\n      @include prefix((transition: opacity 0.3s ease));\n      border-radius: 8px;\n      font-size: 13px;\n      top: $image-size + 10px;\n      left: -10px;\n      width: $width;\n      background: $color;\n      background: linear-gradient(to bottom, $color, darken($color, 5%));\n      padding: 0.5em 1.2em;\n      color: white;\n\n      // The triangle pointing up\n      &:after {\n        content: '';\n        position: absolute;\n        top: -6px;\n        left: $width / 2 - 6px;\n        width: 0; \n        height: 0; \n        border-left: 6px solid transparent;\n        border-right: 6px solid transparent;\n        border-bottom: 6px solid $color;\n      }\n    }\n\n  }\n}\n\n\n"], "names": [], "sourceRoot": ""}