<?php

namespace App\Http\Controllers\Tenant\Export;

use App\Exceptions\GeneralException;
use App\Export\Module\AttendanceModuleExport;
use App\Export\Module\EmployeeModuleExport;
use App\Export\Module\ExportModules;
use App\Export\Module\LeaveModuleExport;
use App\Export\Module\WorkShiftModuleExport;
use App\Exports\AllDataExport;
use App\Helpers\Traits\DateRangeHelper;
use App\Helpers\Traits\SettingKeyHelper;
use App\Http\Controllers\Controller;
use App\Jobs\Tenant\AllDataExportCompleted;
use App\Jobs\Tenant\FastExportJob;
use App\Models\Core\Auth\User;
use App\Models\Tenant\Attendance\Attendance;
use App\Models\Tenant\Export\ModuleExport;
use App\Models\Tenant\Leave\Leave;
use App\Models\Tenant\WorkingShift\WorkingShift;
use App\Repositories\Core\Status\StatusRepository;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ModuleExportController extends Controller
{
    use DateRangeHelper, SettingKeyHelper;


    public function getModuleExports()
    {
        return ModuleExport::query()
            ->select(['id', 'created_by', 'status_id', 'requested_modules', 'completed_modules', 'requested_on', 'completed_on', 'data'])
            ->with([
                'status:id,name,class',
                'createdBy:id,first_name,last_name',
                'createdBy.profilePicture:id,fileable_type,fileable_id,path',
            ])
            ->where('created_by', auth()->id())
            ->orderBy('created_at', 'desc')
            ->paginate(request('per_page', '5'));
    }

    public function download(ModuleExport $module_export)
    {
        if (auth()->id() != $module_export->created_by) {
            throw new GeneralException(__t('action_not_allowed'));
        }
        try {
            return Storage::disk('public')->download($module_export->path);
        } catch (\Exception $exception) {
            throw new GeneralException(__t('resource_not_found'), 404);
        }
    }

    public function retry(ModuleExport $module_export)
    {
        if (auth()->id() != $module_export->created_by) {
            throw new GeneralException(__t('action_not_allowed'));
        }
        $sheets = $module_export->requested_modules;
        $file_name = $module_export->path;

        // create module export
        $module_export->update([
            'status_id' => resolve(StatusRepository::class)->exportPending(),
            'requested_modules' => $sheets,
            'completed_modules' => [],
            'requested_on' => now(),
            'completed_on' => null,
            'data' => [],
        ]);

        FastExportJob::dispatch("$file_name", $sheets, $module_export->id, request('timeZone', timezone()));
//        (new AllDataExport($sheets, $module_export->id))->store("export/$file_name", config('filesystems.default'))->chain([
//            new AllDataExportCompleted($module_export->id)
//        ]);

        return response()->json([
            'message' => __t('export_file_in_progress'),
        ]);
    }

    public function getModuleExportsItem(ModuleExport $module_export)
    {
        return $module_export->load([
            'status:id,name,class',
            'createdBy:id,first_name,last_name',
            'createdBy.profilePicture:id,fileable_type,fileable_id,path',
        ]);
    }

    public function deleteModuleExport(ModuleExport $module_export)
    {
        try {
            Storage::disk('public')->delete($module_export->path);
            $module_export->delete();
        } catch (\Exception $exception) {
            throw new GeneralException($exception->getMessage());
        }
        return deleted_responses('export_file');
    }

    public function moduleExports(Request $request)
    {
        $request->validate([
            'fields' => ['required', 'array', 'min:1'],
            'fields.*' => [
                'required',
                'in:all_data,holiday,leave_type,employee,employment_history,designation_history,department_history,work_shift,attendance,leave,salary_history,department,leave_status_history'
            ],
        ]);

        [$sheets, $file_name] = $this->getSheetsAndFilename($request);

        // create module export
        $module_export = ModuleExport::query()->create([
            'created_by' => auth()->id(),
            'status_id' => resolve(StatusRepository::class)->exportPending(),
            'requested_modules' => $sheets,
            'completed_modules' => [],
            'requested_on' => now(),
            'completed_on' => null,
            'path' => "/export/$file_name",
            'data' => [],
        ]);

        FastExportJob::dispatch("/export/$file_name", $sheets, $module_export->id, request('timeZone', timezone()));

//        (new AllDataExport($sheets, $module_export->id))->store("export/$file_name", config('filesystems.default'))->chain([
//            new AllDataExportCompleted($module_export->id)
//        ]);

        return response()->json([
            'message' => __t('export_file_in_progress'),
        ]);
    }

    public function getSheetsAndFilename(Request $request): array
    {
        $sheets = [];
        $time = now(\request('timeZone', timezone()));

        if (in_array('all_data', $request->fields)) {
            $sheets = [
                'holiday',
                'leave_type',
                'employee',
                'employment_history',
                'designation_history',
                'department_history',
                'work_shift',
                'attendance',
                'leave',
                'salary_history',
                'department',
                'leave_status_history'
            ];
            $file_name = "all_data_{$time->format('Y-m-d_H-i-s')}.xlsx";
            return [$sheets, $file_name];
        }

        if (in_array('holiday', $request->fields)) {
            $sheets[] = 'holiday';
        }
        if (in_array('leave_type', $request->fields)) {
            $sheets[] = 'leave_type';
        }
        if (in_array('employee', $request->fields)) {
            $sheets[] = 'employee';
        }
        if (in_array('employment_history', $request->fields)) {
            $sheets[] = 'employment_history';
        }
        if (in_array('designation_history', $request->fields)) {
            $sheets[] = 'designation_history';
        }
        if (in_array('department_history', $request->fields)) {
            $sheets[] = 'department_history';
        }
        if (in_array('work_shift', $request->fields)) {
            $sheets[] = 'work_shift';
        }
        if (in_array('attendance', $request->fields)) {
            $sheets[] = 'attendance';
        }
        if (in_array('leave', $request->fields)) {
            $sheets[] = 'leave';
        }
        if (in_array('salary_history', $request->fields)) {
            $sheets[] = 'salary_history';
        }
        if (in_array('department', $request->fields)) {
            $sheets[] = 'department';
        }
        if (in_array('leave_status_history', $request->fields)) {
            $sheets[] = 'leave_status_history';
        }
        $file_name = implode('_', $sheets) . '_' . $time->format('Y-m-d_H-i-s') . '.xlsx';

        return [$sheets, $file_name];
    }


    //first version export method
    public function export(Request $request)
    {
        $request->validate([
            'fields' => ['required', 'array']
        ]);

        if (count($request->fields) == 1 && !in_array('all_data', $request->fields)) {
            $exportable = match ($request->fields[0]) {
                'employee' => $this->exportEmployee(),
                'leave' => $this->exportLeave(),
                'attendance' => $this->exportAttendance(),
                'work_shift' => $this->exportWorkShift(),
            };
            $file_name = $request->fields[0] . '_' . now()->toDateString() . '.xlsx';
            $exportable->store("export/$file_name", config('filesystems.default'));
            return response()->json([
                'message' => __t('export_file_saved_successfully'),
                'file_name' => $file_name
            ]);
        }

        $sheets = [];
        if (in_array('all_data', $request->fields)) {
            $sheets = [
                $this->exportEmployee(),
                $this->exportWorkShift(),
                $this->exportAttendance(),
                $this->exportLeave(),
            ];
            $file_name = 'all_data_' . nowFromApp()->toDateString() . '.xlsx';
        } else {
            if (in_array('employee', $request->fields)) {
                $sheets[] = $this->exportEmployee();
            }
            if (in_array('leave', $request->fields)) {
                $sheets[] = $this->exportLeave();
            }
            if (in_array('attendance', $request->fields)) {
                $sheets[] = $this->exportAttendance();
            }
            if (in_array('work_shift', $request->fields)) {
                $sheets[] = $this->exportWorkShift();
            }
            $file_name = implode('_', $request->fields) . '_' . nowFromApp()->toDateString() . '.xlsx';
        }

        (new ExportModules($sheets))->store("export/$file_name", config('filesystems.default'));
        return response()->json([
            'message' => __t('export_file_saved_successfully'),
            'file_name' => $file_name
        ]);
    }

    public function exportEmployee()
    {

        $users = User::query()
            ->select('id', 'email', 'first_name', 'last_name')
            ->with([
                'department:id,name,manager_id,department_id',
                'department.parentDepartment',
                'workingShift',
                'employmentStatus:id,name',

            ])->get();

        return (new EmployeeModuleExport($users, WorkingShift::getDefault()));

    }

    public function exportWorkShift()
    {

        $shifts = WorkingShift::query()
            ->with([
                'details',
            ])->get();

        return (new WorkShiftModuleExport($shifts));

    }

    public function exportAttendance()
    {

        $attendances = Attendance::query()
            ->select(['id', 'in_date', 'user_id', 'behavior'])
            ->with([
                'user:id,first_name,last_name,email',
                'details' => function (HasMany $details) {
                    $details->orderBy('in_time', 'DESC')
                        ->select([
                            'id',
                            'in_time',
                            'out_time',
                            'attendance_id',
                            'status_id',
                            'review_by',
                            'added_by',
                            'attendance_details_id',
                            'in_ip_data',
                            'out_ip_data'
                        ]);
                },
                'details.comments' => fn(MorphMany $morphMany) => $morphMany->orderBy('parent_id', 'DESC')
                    ->select('id', 'commentable_type', 'commentable_id', 'user_id', 'type', 'comment', 'parent_id'),
                'details.status',
                'details.breakTimes'
            ])->get();

        return (new AttendanceModuleExport($attendances));

    }

    public function exportLeave()
    {

        $leaves = Leave::query()
            ->with([
                'status:id,name,class',
                'type:id,name',
                'user:id,email',
                'lastReview.department:id,manager_id',
                'comments' => fn(MorphMany $many) => $many->orderBy('parent_id', 'DESC')
                    ->select('id', 'commentable_type', 'commentable_id', 'user_id', 'type', 'comment', 'parent_id'),
            ])
            ->latest('date')
            ->get();

        return (new LeaveModuleExport($leaves));

    }

}
