<template>
    <div class="content-wrapper">
        <app-page-top-section :title="$t('signature')">
            <app-default-button
                v-if="$can('manage_signatures')"
                btn-class="btn btn-primary"
                :title="$t('add_signature')"
                @click="openSignatureModal()"
            />
        </app-page-top-section>

        <div class="card card-with-shadow border-0" style="min-height: 400px;">
            <div class="card-header d-flex align-items-center justify-content-between primary-card-color p-primary mb-primary">
                <h5 class="mb-0">{{ $t('signature_management') }}</h5>
            </div>

            <app-table
                id="signature-table"
                :options="tableOptions"
                @action="triggerActions"
            />
        </div>

        <app-confirmation-modal
            v-if="confirmationModalActive"
            :loading="loading"
            :message="promptMessage"
            :modal-class="promptClass"
            :icon="promptIcon"
            modal-id="app-confirmation-modal"
            @confirmed="confirmed"
            @cancelled="confirmationModalActive = false"
            :self-close="false"
        />

        <!-- Signature Modal would go here -->
        <div v-if="signatureModalActive" class="modal fade show" style="display: block;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{{ $t('signature_details') }}</h5>
                        <button type="button" class="close" @click="signatureModalActive = false">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>{{ $t('signatory_name') }} <span class="text-danger">*</span></label>
                            <input
                                type="text"
                                class="form-control"
                                :class="{'is-invalid': errors.name}"
                                v-model="signatureForm.name"
                                @blur="validateField('name')"
                            >
                            <div v-if="errors.name" class="invalid-feedback">
                                {{ errors.name[0] }}
                            </div>
                        </div>
                        <div class="form-group">
                            <label>{{ $t('position') }} <span class="text-danger">*</span></label>
                            <input
                                type="text"
                                class="form-control"
                                :class="{'is-invalid': errors.position}"
                                v-model="signatureForm.position"
                                @blur="validateField('position')"
                            >
                            <div v-if="errors.position" class="invalid-feedback">
                                {{ errors.position[0] }}
                            </div>
                        </div>
                        <div class="form-group">
                            <label>{{ $t('department') }}</label>
                            <input
                                type="text"
                                class="form-control"
                                v-model="signatureForm.department"
                                :placeholder="$t('optional')"
                            >
                        </div>
                        <div class="form-group">
                            <label>{{ $t('signature_type') }} <span class="text-danger">*</span></label>
                            <select
                                class="form-control"
                                :class="{'is-invalid': errors.type}"
                                v-model="signatureForm.type"
                                @change="validateField('type')"
                            >
                                <option value="">{{ $t('select_type') }}</option>
                                <option value="digital">{{ $t('digital_signature') }}</option>
                                <option value="manual">{{ $t('manual_signature') }}</option>
                                <option value="stamp">{{ $t('official_stamp') }}</option>
                            </select>
                            <div v-if="errors.type" class="invalid-feedback">
                                {{ errors.type[0] }}
                            </div>
                        </div>
                        <div v-if="signatureForm.type === 'digital'" class="form-group">
                            <label>{{ $t('signature_file') }}</label>
                            <input
                                type="file"
                                class="form-control-file"
                                :class="{'is-invalid': errors.signature_file}"
                                @change="handleFileUpload"
                                accept="image/*"
                            >
                            <small class="form-text text-muted">
                                {{ $t('supported_formats') }}: PNG, JPG, JPEG, SVG (Max: 2MB)
                            </small>
                            <div v-if="errors.signature_file" class="invalid-feedback">
                                {{ errors.signature_file[0] }}
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-check">
                                <input
                                    type="checkbox"
                                    class="form-check-input"
                                    id="isDefault"
                                    v-model="signatureForm.is_default"
                                >
                                <label class="form-check-label" for="isDefault">
                                    {{ $t('set_as_default') }}
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" @click="signatureModalActive = false">
                            {{ $t('cancel') }}
                        </button>
                        <button type="button" class="btn btn-primary" @click="saveSignature()">
                            {{ $t('save') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {axiosGet, axiosPost, axiosPatch, axiosDelete, urlGenerator} from "../../../../common/Helper/AxiosHelper";
import {SIGNATURES, SIGNATURE_TYPES} from "../../../Config/ApiUrl";

export default {
    name: "Signature",
    data() {
        return {
            urlGenerator,
            confirmationModalActive: false,
            signatureModalActive: false,
            promptClass: '',
            promptIcon: '',
            promptMessage: '',
            loading: false,
            signatureForm: {
                name: '',
                position: '',
                department: '',
                type: '',
                signature_file: null,
                is_default: false,
                is_active: true
            },
            errors: {},
            tableOptions: {
                name: this.$t('signatures'),
                url: SIGNATURES,
                showHeader: true,
                enableRowSelect: false,
                showCount: true,
                showClearFilter: true,
                showFilter: true,
                showSearch: true,
                tablePaddingClass: "px-0",
                tableShadow: false,
                columns: [
                    {
                        title: this.$t('signatory_name'),
                        type: 'text',
                        key: 'name',
                        isVisible: true
                    },
                    {
                        title: this.$t('position'),
                        type: 'text',
                        key: 'position',
                        isVisible: true
                    },
                    {
                        title: this.$t('signature_type'),
                        type: 'custom-html',
                        key: 'type',
                        isVisible: true,
                        modifier: (val) => {
                            const badgeClass = val === 'digital' ? 'badge-primary' : 'badge-secondary';
                            return `<span class="badge ${badgeClass}">${this.$t(val + '_signature')}</span>`;
                        }
                    },
                    {
                        title: this.$t('status'),
                        type: 'custom-html',
                        key: 'status',
                        isVisible: true,
                        modifier: (val) => {
                            return `<span class="badge badge-pill badge-${val.class}">${val.translated_name}</span>`;
                        }
                    },
                    {
                        title: this.$t('created_at'),
                        type: 'text',
                        key: 'created_at',
                        isVisible: true
                    }
                ],
                filters: [],
                paginationType: "pagination",
                responsive: true,
                rowLimit: 10,
                showAction: true,
                orderBy: 'desc',
                actionType: "dropdown",
                actions: [
                    {
                        title: this.$t('edit'),
                        actionName: 'edit',
                        modifier: () => this.$can('manage_signatures')
                    },
                    {
                        title: this.$t('delete'),
                        actionName: 'delete',
                        modifier: () => this.$can('manage_signatures')
                    }
                ]
            }
        }
    },
    methods: {
        triggerActions(row, action) {
            if (action.actionName === 'edit') {
                this.editSignature(row);
            } else if (action.actionName === 'delete') {
                this.deleteSignature(row);
            }
        },
        openSignatureModal() {
            this.signatureForm = {
                name: '',
                position: '',
                department: '',
                type: '',
                signature_file: null,
                is_default: false,
                is_active: true
            };
            this.errors = {};
            this.signatureModalActive = true;
        },
        validateField(field) {
            this.errors = { ...this.errors };
            delete this.errors[field];

            switch (field) {
                case 'name':
                    if (!this.signatureForm.name || this.signatureForm.name.trim() === '') {
                        this.$set(this.errors, 'name', ['Signatory name is required']);
                    }
                    break;
                case 'position':
                    if (!this.signatureForm.position || this.signatureForm.position.trim() === '') {
                        this.$set(this.errors, 'position', ['Position is required']);
                    }
                    break;
                case 'type':
                    if (!this.signatureForm.type) {
                        this.$set(this.errors, 'type', ['Signature type is required']);
                    }
                    break;
            }
        },
        validateForm() {
            this.errors = {};
            this.validateField('name');
            this.validateField('position');
            this.validateField('type');

            return Object.keys(this.errors).length === 0;
        },
        handleFileUpload(event) {
            const file = event.target.files[0];
            if (file) {
                // Validate file size (2MB max)
                if (file.size > 2 * 1024 * 1024) {
                    this.$set(this.errors, 'signature_file', ['File size must be less than 2MB']);
                    return;
                }

                // Validate file type
                const allowedTypes = ['image/png', 'image/jpg', 'image/jpeg', 'image/svg+xml'];
                if (!allowedTypes.includes(file.type)) {
                    this.$set(this.errors, 'signature_file', ['Invalid file type. Only PNG, JPG, JPEG, SVG are allowed']);
                    return;
                }

                this.signatureForm.signature_file = file;
                delete this.errors.signature_file;
            }
        },
        editSignature(row) {
            this.signatureForm = { ...row };
            this.signatureModalActive = true;
        },
        deleteSignature(row) {
            this.confirmationModalActive = true;
            this.promptIcon = 'trash-2';
            this.promptClass = 'danger';
            this.promptMessage = this.$t('are_you_sure_to_delete_signature');
        },
        saveSignature() {
            if (!this.validateForm()) {
                this.$toastr.e('Please fix the validation errors');
                return;
            }

            this.loading = true;

            const formData = new FormData();
            formData.append('name', this.signatureForm.name);
            formData.append('position', this.signatureForm.position);
            formData.append('department', this.signatureForm.department || '');
            formData.append('type', this.signatureForm.type);
            formData.append('is_default', this.signatureForm.is_default ? '1' : '0');
            formData.append('is_active', this.signatureForm.is_active ? '1' : '0');

            if (this.signatureForm.signature_file) {
                formData.append('signature_file', this.signatureForm.signature_file);
            }

            const isEdit = this.signatureForm.id;
            const url = isEdit ? `${SIGNATURES}/${this.signatureForm.id}` : SIGNATURES;
            const method = isEdit ? 'patch' : 'post';

            this.$http[method](url, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            }).then(response => {
                if (response.data.status) {
                    this.$toastr.s(response.data.message);
                    this.signatureModalActive = false;
                    this.$hub.$emit('reload-signature-table');
                } else {
                    this.$toastr.e(response.data.message);
                }
            }).catch(error => {
                if (error.response && error.response.data.errors) {
                    this.errors = error.response.data.errors;
                } else {
                    this.$toastr.e('Failed to save signature');
                }
            }).finally(() => {
                this.loading = false;
            });
        },
        confirmed() {
            // Implementation for confirmed actions
            this.loading = true;
            setTimeout(() => {
                this.loading = false;
                this.confirmationModalActive = false;
                this.$toastr.s(this.$t('operation_completed_successfully'));
            }, 1000);
        }
    }
}
</script>

<style scoped>
.modal {
    background-color: rgba(0, 0, 0, 0.5);
}
</style>
