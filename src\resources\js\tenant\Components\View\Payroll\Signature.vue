<template>
    <div class="content-wrapper">
        <app-page-top-section :title="$t('signature')">
            <app-default-button
                v-if="$can('manage_signatures')"
                btn-class="btn btn-primary"
                :title="$t('add_signature')"
                @click="openSignatureModal()"
            />
        </app-page-top-section>

        <div class="card card-with-shadow border-0" style="min-height: 400px;">
            <div class="card-header d-flex align-items-center justify-content-between primary-card-color p-primary mb-primary">
                <h5 class="mb-0">{{ $t('signature_management') }}</h5>
            </div>

            <app-table
                id="signature-table"
                :options="tableOptions"
                @action="triggerActions"
            />
        </div>

        <app-confirmation-modal
            v-if="confirmationModalActive"
            :loading="loading"
            :message="promptMessage"
            :modal-class="promptClass"
            :icon="promptIcon"
            modal-id="app-confirmation-modal"
            @confirmed="confirmed"
            @cancelled="confirmationModalActive = false"
            :self-close="false"
        />

        <!-- Signature Modal would go here -->
        <div v-if="signatureModalActive" class="modal fade show" style="display: block;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{{ $t('signature_details') }}</h5>
                        <button type="button" class="close" @click="signatureModalActive = false">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>{{ $t('signatory_name') }}</label>
                            <input type="text" class="form-control" v-model="signatureForm.name">
                        </div>
                        <div class="form-group">
                            <label>{{ $t('position') }}</label>
                            <input type="text" class="form-control" v-model="signatureForm.position">
                        </div>
                        <div class="form-group">
                            <label>{{ $t('signature_type') }}</label>
                            <select class="form-control" v-model="signatureForm.type">
                                <option value="digital">{{ $t('digital_signature') }}</option>
                                <option value="manual">{{ $t('manual_signature') }}</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" @click="signatureModalActive = false">
                            {{ $t('cancel') }}
                        </button>
                        <button type="button" class="btn btn-primary" @click="saveSignature()">
                            {{ $t('save') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {axiosGet, urlGenerator} from "../../../../common/Helper/AxiosHelper";

export default {
    name: "Signature",
    data() {
        return {
            urlGenerator,
            confirmationModalActive: false,
            signatureModalActive: false,
            promptClass: '',
            promptIcon: '',
            promptMessage: '',
            loading: false,
            signatureForm: {
                name: '',
                position: '',
                type: 'digital'
            },
            tableOptions: {
                name: this.$t('signatures'),
                url: '',
                showHeader: true,
                enableRowSelect: false,
                showCount: true,
                showClearFilter: true,
                showFilter: true,
                showSearch: true,
                tablePaddingClass: "px-0",
                tableShadow: false,
                columns: [
                    {
                        title: this.$t('signatory_name'),
                        type: 'text',
                        key: 'name',
                        isVisible: true
                    },
                    {
                        title: this.$t('position'),
                        type: 'text',
                        key: 'position',
                        isVisible: true
                    },
                    {
                        title: this.$t('signature_type'),
                        type: 'custom-html',
                        key: 'type',
                        isVisible: true,
                        modifier: (val) => {
                            const badgeClass = val === 'digital' ? 'badge-primary' : 'badge-secondary';
                            return `<span class="badge ${badgeClass}">${this.$t(val + '_signature')}</span>`;
                        }
                    },
                    {
                        title: this.$t('status'),
                        type: 'custom-html',
                        key: 'status',
                        isVisible: true,
                        modifier: (val) => {
                            return `<span class="badge badge-pill badge-${val.class}">${val.translated_name}</span>`;
                        }
                    },
                    {
                        title: this.$t('created_at'),
                        type: 'text',
                        key: 'created_at',
                        isVisible: true
                    }
                ],
                filters: [],
                paginationType: "pagination",
                responsive: true,
                rowLimit: 10,
                showAction: true,
                orderBy: 'desc',
                actionType: "dropdown",
                actions: [
                    {
                        title: this.$t('edit'),
                        actionName: 'edit',
                        modifier: () => this.$can('manage_signatures')
                    },
                    {
                        title: this.$t('delete'),
                        actionName: 'delete',
                        modifier: () => this.$can('manage_signatures')
                    }
                ]
            }
        }
    },
    methods: {
        triggerActions(row, action) {
            if (action.actionName === 'edit') {
                this.editSignature(row);
            } else if (action.actionName === 'delete') {
                this.deleteSignature(row);
            }
        },
        openSignatureModal() {
            this.signatureForm = {
                name: '',
                position: '',
                type: 'digital'
            };
            this.signatureModalActive = true;
        },
        editSignature(row) {
            this.signatureForm = { ...row };
            this.signatureModalActive = true;
        },
        deleteSignature(row) {
            this.confirmationModalActive = true;
            this.promptIcon = 'trash-2';
            this.promptClass = 'danger';
            this.promptMessage = this.$t('are_you_sure_to_delete_signature');
        },
        saveSignature() {
            // Implementation for saving signature
            this.loading = true;
            setTimeout(() => {
                this.loading = false;
                this.signatureModalActive = false;
                this.$toastr.s(this.$t('signature_saved_successfully'));
            }, 1000);
        },
        confirmed() {
            // Implementation for confirmed actions
            this.loading = true;
            setTimeout(() => {
                this.loading = false;
                this.confirmationModalActive = false;
                this.$toastr.s(this.$t('operation_completed_successfully'));
            }, 1000);
        }
    }
}
</script>

<style scoped>
.modal {
    background-color: rgba(0, 0, 0, 0.5);
}
</style>
