<template>
    <div class="content-wrapper">
        <app-page-top-section :title="$t('signature')"/>
        
        <div class="card border-0">
            <div class="card-header d-flex align-items-center justify-content-between primary-card-color">
                <h5 class="card-title mb-0">{{ $t('signature_management') }}</h5>
                <div class="btn-group">
                    <button 
                        type="button" 
                        class="btn btn-primary"
                        @click="openCreateModal"
                        v-if="$can('create_signature')"
                    >
                        <i class="fas fa-plus"></i>
                        {{ $t('add_signature') }}
                    </button>
                </div>
            </div>
            
            <div class="card-body">
                <div class="row">
                    <div class="col-12">
                        <app-table
                            :id="tableId"
                            :options="options"
                            @action="triggerActions"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- Create/Edit Modal -->
        <app-modal
            modal-id="signature-modal"
            modal-size="large"
            :modal-title="isEdit ? $t('edit_signature') : $t('add_signature')"
            @close-modal="closeModal"
        >
            <signature-form
                :selected-url="selectedUrl"
                :is-edit="isEdit"
                @close-modal="closeModal"
                @reload-table="reloadTable"
            />
        </app-modal>
    </div>
</template>

<script>
import {TENANT_BASE_URL} from "../../../Config/ApiUrl";

export default {
    name: "Signature",
    data() {
        return {
            tableId: 'signature-table',
            selectedUrl: '',
            isEdit: false,
            options: {
                name: this.$t('signature'),
                url: `${TENANT_BASE_URL}app/signatures`,
                showHeader: true,
                columns: [
                    {
                        title: this.$t('id'),
                        type: 'text',
                        key: 'id',
                        isVisible: true
                    },
                    {
                        title: this.$t('name'),
                        type: 'text',
                        key: 'name',
                        isVisible: true
                    },
                    {
                        title: this.$t('position'),
                        type: 'text',
                        key: 'position',
                        isVisible: true
                    },
                    {
                        title: this.$t('signature_image'),
                        type: 'image',
                        key: 'signature_path',
                        isVisible: true
                    },
                    {
                        title: this.$t('created_at'),
                        type: 'text',
                        key: 'created_at',
                        isVisible: true
                    },
                    {
                        title: this.$t('actions'),
                        type: 'action',
                        key: 'actions',
                        isVisible: true
                    }
                ],
                filters: [],
                paginationType: "pagination",
                responsive: true,
                rowLimit: 10,
                showAction: true,
                orderBy: 'desc',
                actionType: "default",
                actions: [
                    {
                        title: this.$t('edit'),
                        icon: 'edit',
                        type: 'modal',
                        component: 'app-signature-modal',
                        modalId: 'signature-modal',
                        url: `${TENANT_BASE_URL}app/signatures`,
                        name: 'edit',
                        modifier: 'btn-outline-dark'
                    },
                    {
                        title: this.$t('delete'),
                        icon: 'trash-2',
                        type: 'modal',
                        component: 'app-confirmation-modal',
                        modalId: 'app-confirmation-modal',
                        url: `${TENANT_BASE_URL}app/signatures`,
                        name: 'delete',
                        modifier: 'btn-outline-danger'
                    }
                ]
            }
        }
    },
    methods: {
        openCreateModal() {
            this.isEdit = false;
            this.selectedUrl = '';
            $('#signature-modal').modal('show');
        },
        
        closeModal() {
            this.isEdit = false;
            this.selectedUrl = '';
            $('#signature-modal').modal('hide');
        },
        
        reloadTable() {
            this.$hub.$emit('reload-' + this.tableId);
        },
        
        triggerActions(row, action, active) {
            if (action.name === 'edit') {
                this.isEdit = true;
                this.selectedUrl = `${TENANT_BASE_URL}app/signatures/${row.id}`;
                $('#signature-modal').modal('show');
            } else if (action.name === 'delete') {
                this.deleteSignature(row.id);
            }
        },
        
        deleteSignature(id) {
            this.$confirm({
                title: this.$t('are_you_sure'),
                message: this.$t('you_will_not_be_able_to_revert_this'),
                button: {
                    no: this.$t('cancel'),
                    yes: this.$t('delete')
                },
                callback: confirm => {
                    if (confirm) {
                        this.axiosDelete(`${TENANT_BASE_URL}app/signatures/${id}`)
                            .then(response => {
                                this.$toastr.s(response.data.message);
                                this.reloadTable();
                            })
                            .catch(error => {
                                this.$toastr.e(error.response.data.message);
                            });
                    }
                }
            });
        }
    }
}
</script>

<style scoped>
.primary-card-color {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}
</style>
