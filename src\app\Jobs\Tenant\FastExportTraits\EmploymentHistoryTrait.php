<?php

namespace App\Jobs\Tenant\FastExportTraits;

use App\Helpers\Traits\DateTimeHelper;
use App\Models\Tenant\Employee\UserEmploymentStatus;

trait EmploymentHistoryTrait
{
    use DateTimeHelper;
    // Define EmploymentHistory generator function
    public function EmploymentHistoryDataGenerator(): \Generator
    {
        foreach (
            UserEmploymentStatus::query()
                ->select(['start_date', 'end_date'])
                ->selectSub(function ($query) {
                    $query->select('name')
                        ->from('employment_statuses')
                        ->whereColumn('employment_statuses.id', 'user_employment_status.employment_status_id')
                        ->limit(1);
                }, 'employment_status')
                ->selectSub(function ($query) {
                    $query->select('email')
                        ->from('users')
                        ->whereColumn('users.id', 'user_employment_status.user_id')
                        ->limit(1);
                }, 'user')
                ->cursor() as $row) {
            $row->sheet_name = 'employment_history';
            yield $row;
        }
    }

    // Define EmploymentHistory map function
    public function employmentHistoryMap($row): array {
        return [
            'User' => $row->user,
            'Employment_status' => $row->employment_status,
            'Start_date' => $this->getEmploymentHistoryStartDate($row),
            'End_date' => $this->getEmploymentHistoryEndDate($row),
        ];
    }

    // mapping helper functions
    private function getEmploymentHistoryStartDate($row) {
        if(isset($row->start_date)) {
            return $this->carbon($row->start_date)->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
    private function getEmploymentHistoryEndDate($row) {
        if(isset($row->end_date)) {
            return $this->carbon($row->end_date)->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
}