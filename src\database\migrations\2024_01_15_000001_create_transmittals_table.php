<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transmittals', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->json('data')->nullable();
            $table->json('payslip_ids')->nullable(); // Store selected payslip IDs
            $table->string('department')->nullable(); // Department name for the transmittal
            $table->date('payroll_period_start')->nullable(); // Payroll period start date
            $table->date('payroll_period_end')->nullable(); // Payroll period end date
            $table->decimal('total_amount', 15, 2)->default(0); // Total salary amount
            $table->integer('total_employees')->default(0); // Total number of employees
            $table->string('prepared_by')->nullable(); // Name of person who prepared
            $table->string('prepared_by_title')->nullable(); // Title of person who prepared
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();

            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transmittals');
    }
};
