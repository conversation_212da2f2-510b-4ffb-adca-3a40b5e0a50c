<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Transmittal Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        .report-info {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
        }
        .info-left, .info-right {
            width: 48%;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .total-row {
            background-color: #f9f9f9;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
        .signature-section {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
        }
        .signature-box {
            width: 200px;
            text-align: center;
        }
        .signature-line {
            border-top: 1px solid #333;
            margin-top: 50px;
            padding-top: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">{{ config('app.name') }}</div>
        <div class="report-title">TRANSMITTAL REPORT</div>
    </div>

    <div class="report-info">
        <div class="info-left">
            <strong>Generated Date:</strong> {{ $generated_at->format('F d, Y H:i:s') }}<br>
            <strong>Generated By:</strong> {{ $generated_by }}<br>
            <strong>Total Records:</strong> {{ $transmittals->count() }}
        </div>
        <div class="info-right">
            <strong>Period:</strong> 
            @if($transmittals->isNotEmpty())
                {{ $transmittals->first()->period_start->format('M d, Y') }} - 
                {{ $transmittals->first()->period_end->format('M d, Y') }}
            @endif
        </div>
    </div>

    <table>
        <thead>
            <tr>
                <th>Ref. No.</th>
                <th>Employee</th>
                <th>Department</th>
                <th>Period</th>
                <th class="text-right">Amount</th>
                <th class="text-center">Payslips</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            @php $totalAmount = 0; $totalPayslips = 0; @endphp
            @foreach($transmittals as $transmittal)
                <tr>
                    <td>{{ $transmittal->reference_number }}</td>
                    <td>
                        {{ $transmittal->employee->full_name }}<br>
                        <small>{{ $transmittal->employee->employee_id ?? 'N/A' }}</small>
                    </td>
                    <td>{{ $transmittal->employee->department->name ?? 'Unassigned' }}</td>
                    <td>{{ $transmittal->period_display }}</td>
                    <td class="text-right">₱{{ number_format($transmittal->total_amount, 2) }}</td>
                    <td class="text-center">{{ $transmittal->payslip_count }}</td>
                    <td>{{ $transmittal->status->name ?? 'Unknown' }}</td>
                </tr>
                @php 
                    $totalAmount += $transmittal->total_amount; 
                    $totalPayslips += $transmittal->payslip_count;
                @endphp
            @endforeach
            <tr class="total-row">
                <td colspan="4"><strong>TOTAL</strong></td>
                <td class="text-right"><strong>₱{{ number_format($totalAmount, 2) }}</strong></td>
                <td class="text-center"><strong>{{ $totalPayslips }}</strong></td>
                <td></td>
            </tr>
        </tbody>
    </table>

    <div class="signature-section">
        <div class="signature-box">
            <div class="signature-line">
                Prepared By
            </div>
        </div>
        <div class="signature-box">
            <div class="signature-line">
                Reviewed By
            </div>
        </div>
        <div class="signature-box">
            <div class="signature-line">
                Approved By
            </div>
        </div>
    </div>

    <div class="footer">
        <p>This is a computer-generated report. Generated on {{ $generated_at->format('F d, Y \a\t H:i:s') }}</p>
    </div>
</body>
</html>
