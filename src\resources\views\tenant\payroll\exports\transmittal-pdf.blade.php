<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Payroll Transmittal</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            margin: 0;
            padding: 15px;
            line-height: 1.2;
        }
        .header {
            text-align: center;
            margin-bottom: 25px;
        }
        .company-name {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 3px;
            text-transform: uppercase;
        }
        .company-address {
            font-size: 10px;
            font-style: italic;
            margin-bottom: 15px;
        }
        .department {
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            margin-bottom: 20px;
        }
        .report-info {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
        }
        .info-left, .info-right {
            width: 48%;
        }
        .period-info {
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 15px;
            text-transform: uppercase;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 2px solid #000;
        }
        th {
            border: 1px solid #000;
            padding: 6px 8px;
            text-align: center;
            font-weight: bold;
            font-size: 10px;
            background-color: #fff;
            text-transform: uppercase;
        }
        td {
            border: 1px solid #000;
            padding: 4px 8px;
            font-size: 10px;
        }
        .employee-number {
            text-align: center;
            width: 80px;
        }
        .employee-name {
            text-align: left;
            width: 200px;
        }
        .designation {
            text-align: left;
            width: 180px;
        }
        .salary {
            text-align: right;
            width: 100px;
        }
        .total-row {
            font-weight: bold;
            background-color: #fff;
        }
        .total-row td {
            border-top: 2px solid #000;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
        .signature-section {
            margin-top: 40px;
            margin-bottom: 20px;
        }
        .prepared-by {
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 40px;
        }
        .signature-box {
            margin-top: 20px;
            font-size: 10px;
        }
        .signature-name {
            font-weight: bold;
            margin-bottom: 2px;
        }
        .signature-title {
            font-size: 9px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">STA. CATALINA TOLONG MILLING CORPORATION</div>
        <div class="company-address">Sitio, Sicopong Brgy. Caranoche, Sta. Catalina, Negros Oriental</div>
        <div class="department">ELECTRICAL</div>
    </div>

    <div class="period-info">
        @if($transmittals->isNotEmpty())
            PAYROLL COVERED AS OF {{ strtoupper($transmittals->first()->period_start->format('M d')) }}-{{ strtoupper($transmittals->first()->period_end->format('M d, Y')) }}
        @else
            PAYROLL COVERED AS OF [PERIOD]
        @endif
    </div>

    <table>
        <thead>
            <tr>
                <th class="employee-number">EMPLOYEE<br>NUMBER</th>
                <th class="employee-name">NAME</th>
                <th class="designation">DESIGNATION</th>
                <th class="salary">TOTAL SALARY</th>
            </tr>
        </thead>
        <tbody>
            @php $totalAmount = 0; @endphp
            @foreach($transmittals as $transmittal)
                <tr>
                    <td class="employee-number">{{ $transmittal->employee->employee_id ?? $transmittal->reference_number }}</td>
                    <td class="employee-name">{{ $transmittal->employee->full_name }}</td>
                    <td class="designation">{{ $transmittal->employee->designation->name ?? 'N/A' }}</td>
                    <td class="salary">{{ number_format($transmittal->total_amount, 2) }}</td>
                </tr>
                @php $totalAmount += $transmittal->total_amount; @endphp
            @endforeach
            <tr class="total-row">
                <td class="employee-number"><strong>TOTAL</strong></td>
                <td class="employee-name"></td>
                <td class="designation"></td>
                <td class="salary"><strong>{{ number_format($totalAmount, 2) }}</strong></td>
            </tr>
        </tbody>
    </table>

    <div class="signature-section">
        <div class="prepared-by">Prepared By:</div>

        <div class="signature-box">
            <div class="signature-name">Via Mae N. Tembrevilla</div>
            <div class="signature-title">Payroll Analyst</div>
        </div>
    </div>
</body>
</html>
