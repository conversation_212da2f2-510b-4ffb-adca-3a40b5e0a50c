<template>
  <div :class="['subscription-plan-page', { 'dark': isDarkMode }]">
    <div class="plans-header">
      <h1 class="plans-title">Transforming Modern Workforces<br />with Smarter HR Solutions</h1>
      <div class="plans-subtitle">
        Choose Your Plan<br />
      </div>
    </div>
    <div class="plans-container">
      <div class="plan-card">
        <h2>Monthly Plan</h2>
        <div class="plan-sub">Per employee</div>
        <div class="plan-price">
          <span class="price-main">
            <span class="currency">₱</span>
            <span class="amount">70</span>
            <span class="per">/Month</span>
          </span>
        </div>
        <ul class="plan-features">
          <li>Full access to all HR modules</li>
          <li>Access to training and system guides</li>
          <li>Technical/Customer Support</li>
          <li>Custom reports</li>
        </ul>
        <button class="plan-btn" onclick="window.location.href='https://corehrm.ph/#contact-us'">Get Started Now!</button>
        <div class="plan-desc">Ideal for testing out Core HRM without a long-term commitment.</div>
      </div>
      <div class="plan-card popular">
        <div class="ribbon">Most Popular</div>
        <h2>Yearly Plan</h2>
        <div class="plan-sub">Per employee</div>
        <div class="plan-price">
          <span class="price-main">
            <span class="currency">₱</span>
            <span class="amount">840</span>
            <span class="per">/Year</span>
          </span>
        </div>
        <ul class="plan-features">
          <li>Full access to all HR modules</li>
          <li>Access to training and system guides</li>
          <li>Technical/Customer Support</li>
          <li>Custom reports</li>
        </ul>
        <button class="plan-btn" onclick="window.location.href='https://corehrm.ph/#contact-us'">Get Started Now!</button>
        <div class="plan-desc">All-in-one HR suite built for long-term efficiency and productivity.</div>
      </div>
      <div class="plan-card sale">
        <div class="ribbon sale">Sale</div>
        <h2>One-time Payment</h2>
        <div class="plan-sub">Perpetual License</div>
        <div class="plan-price premium">
          <span class="price-main">
            <span class="currency">₱</span>
            <span class="amount">Premium</span>
          </span>
        </div>
        <ul class="plan-features">
          <li>Lifetime access</li>
          <li>One-time setup and onboarding support</li>
          <li>Unlimited employee records</li>
          <li>Tailored-fit to your business</li>
        </ul>
        <button class="plan-btn" onclick="window.location.href='https://corehrm.ph/#contact-us'">Get Quote Now!</button>
        <div class="plan-desc">For organizations looking for a long-term HR solution with zero recurring fees.</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SubscriptionPlan',
  computed: {
    isDarkMode() {
      return this.$store && this.$store.state.theme && this.$store.state.theme.darkMode;
    }
  }
};
</script>

<style scoped>
.subscription-plan-page {
  padding: 2rem 0 2rem 0;
  background: #23272f;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  transition: background 0.3s, color 0.3s;
  color: #f3f3f3;
}
.subscription-plan-page.dark {
  background: #23272f;
  color: #f3f3f3;
}
.plans-header {
  width: 100%;
  max-width: 1100px;
  margin: 0 auto 2.5rem auto;
  text-align: center;
}
.plans-title {
  font-size: 2.8rem;
  font-weight: 800;
  margin-bottom: 1.2rem;
  line-height: 1.15;
  color: #fff;
  letter-spacing: -1px;
  transition: color 0.3s;
  background: transparent;
}
.subscription-plan-page.dark .plans-title {
  color: #f3f3f3;
}
.plans-subtitle {
  font-size: 1.1rem;
  color: #b0b0b0;
  margin-bottom: 0.5rem;
  font-weight: 400;
  transition: color 0.3s;
  background: transparent;
}
.subscription-plan-page.dark .plans-subtitle {
  color: #b0b0b0;
}
.plans-container {
  display: flex;
  gap: 2rem;
  justify-content: center;
  flex-wrap: wrap;
}
.plan-card {
  background: #23272f;
  color: #fff;
  border: 2px solid #444a58;
  border-radius: 20px;
  padding: 2rem 2.5rem 1.5rem 2.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 260px;
  max-width: 320px;
  position: relative;
  transition: background 0.3s, color 0.3s, border 0.3s, box-shadow 0.3s, transform 0.25s cubic-bezier(.22,1,.36,1);
  will-change: transform, box-shadow, border-color;
}
.plan-card:hover {
  transform: scale(1.045);
  box-shadow: 0 8px 32px 0 rgba(229,57,53,0.10), 0 1.5px 8px 0 rgba(0,0,0,0.10);
  border-color: #e53935;
  z-index: 3;
  /* Glowing effect on hover */
  box-shadow: 0 0 0 0 #e53935, 0 0 16px 2px #e53935, 0 0 32px 8px rgba(229,57,53,0.18);
  animation: card-glow 2s infinite alternate;
}
@keyframes card-glow {
  0% {
    box-shadow: 0 8px 32px 0 rgba(229,57,53,0.10), 0 1.5px 8px 0 rgba(0,0,0,0.10), 0 0 8px 0 #e53935, 0 0 0px 0 #e53935;
  }
  50% {
    box-shadow: 0 8px 32px 0 rgba(229,57,53,0.13), 0 1.5px 8px 0 rgba(0,0,0,0.13), 0 0 16px 2px #e53935, 0 0 32px 8px rgba(229,57,53,0.18);
  }
  100% {
    box-shadow: 0 8px 32px 0 rgba(229,57,53,0.10), 0 1.5px 8px 0 rgba(0,0,0,0.10), 0 0 8px 0 #e53935, 0 0 0px 0 #e53935;
  }
}
.subscription-plan-page.dark .plan-card {
  background: #2b303b;
  color: #f3f3f3;
  border: 2px solid #444a58;
}
.subscription-plan-page.dark .plan-card:hover {
  border-color: #ff5252;
  box-shadow: 0 8px 32px 0 rgba(255,82,82,0.13), 0 1.5px 8px 0 rgba(0,0,0,0.18);
}
.plan-card.popular {
  border: 2px solid #d9d9d9;
}
.subscription-plan-page.dark .plan-card.popular {
  border: 2px solid #444a58;
}
.plan-card.popular:hover {
  border-color: #e53935 !important;
  box-shadow: 0 8px 32px 0 rgba(229,57,53,0.13), 0 1.5px 8px 0 rgba(0,0,0,0.13);
}
.subscription-plan-page.dark .plan-card.popular:hover {
  border-color: #ff5252 !important;
  box-shadow: 0 8px 32px 0 rgba(255,82,82,0.16), 0 1.5px 8px 0 rgba(0,0,0,0.20);
}
.plan-card .ribbon {
  position: absolute;
  top: 18px;
  right: -30px;
  background: #e53935;
  color: #fff;
  padding: 4px 18px;
  font-size: 0.9rem;
  font-weight: bold;
  transform: rotate(45deg);
  z-index: 2;
}
.plan-card.sale .ribbon.sale {
  background: #ff3d00;
  right: -18px;
  top: 18px;
}
.plan-card h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  text-align: center;
}
.plan-sub {
  font-size: 1rem;
  color: #888;
  margin-bottom: 1rem;
}
.subscription-plan-page.dark .plan-sub {
  color: #b0b0b0;
}
.plan-price {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 1rem;
  position: relative;
}
.price-main {
  display: flex;
  align-items: baseline;
  font-size: 2.2rem;
  font-weight: 700;
  color: #fff;
  position: relative;
  z-index: 1;
}
.currency {
  font-size: 2.2rem;
  font-weight: 700;
  margin-right: 0.1em;
}
.amount {
  font-size: 2.2rem;
  font-weight: 700;
}
.per {
  font-size: 1.1rem;
  font-weight: 400;
  margin-left: 0.2em;
  color: #fff;
}
.price-underline {
  display: block;
  height: 3px;
  width: 100%;
  background: #e53935;
  border-radius: 2px;
  margin-top: 0.1em;
  position: absolute;
  left: 0;
  bottom: -6px;
  z-index: 0;
}
.plan-price.premium .amount {
  font-size: 2.2rem;
  font-weight: 900;
}
.plan-features {
  list-style: none;
  padding: 0;
  margin: 0 0 1.2rem 0;
  width: 100%;
}
.plan-features li {
  margin-bottom: 0.6rem;
  font-size: 1rem;
  color: #fff;
  position: relative;
  padding-left: 1.5em;
}
.subscription-plan-page.dark .plan-features li {
  color: #f3f3f3;
}
.plan-features li:before {
  content: '\2714';
  color: #e53935;
  position: absolute;
  left: 0;
  font-size: 1.1em;
}
.plan-btn {
  background: #e53935;
  color: #fff;
  border: none;
  border-radius: 20px;
  padding: 0.7rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}
.plan-btn:hover {
  background: #b71c1c;
}
.plan-desc {
  font-size: 0.95rem;
  color: #888;
  text-align: center;
  margin-top: auto;
}
.subscription-plan-page.dark .plan-desc {
  color: #b0b0b0;
}
@media (max-width: 1100px) {
  .plans-header {
    padding: 0 1rem;
  }
  .plans-container {
    flex-direction: column;
    align-items: center;
  }
  .plan-card {
    max-width: 400px;
    width: 100%;
  }
}
/* --- Fix for text color contrast in light mode --- */
.price-main,
.per {
  color: #fff;
}

.plan-card h2,
.plans-title {
  color: #fff;
}

/* Subtitle and plan subtext */
.plans-subtitle,
.plan-sub,
.plan-desc {
  color: #b0b0b0;
}

/* Features list */
.plan-features li {
  color: #fff;
}

/* --- Dark mode overrides --- */
.subscription-plan-page.dark .price-main,
.subscription-plan-page.dark .per,
.subscription-plan-page.dark .plan-card h2,
.subscription-plan-page.dark .plans-title,
.subscription-plan-page.dark .plans-subtitle,
.subscription-plan-page.dark .plan-sub,
.subscription-plan-page.dark .plan-desc,
.subscription-plan-page.dark .plan-features li {
  color: #f3f3f3;
}
</style>
