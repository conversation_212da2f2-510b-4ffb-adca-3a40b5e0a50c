<?php

namespace App\Jobs\Tenant;

use App\Jobs\Tenant\FastExportTraits\AttendanceTrait;
use App\Jobs\Tenant\FastExportTraits\DepartmentHistoryTrait;
use App\Jobs\Tenant\FastExportTraits\DepartmentTrait;
use App\Jobs\Tenant\FastExportTraits\DesignationHistoryTrait;
use App\Jobs\Tenant\FastExportTraits\EmployeeTrait;
use App\Jobs\Tenant\FastExportTraits\EmploymentHistoryTrait;
use App\Jobs\Tenant\FastExportTraits\HolidayTrait;
use App\Jobs\Tenant\FastExportTraits\LeaveHistoryTrait;
use App\Jobs\Tenant\FastExportTraits\LeaveTrait;
use App\Jobs\Tenant\FastExportTraits\LeaveTypeTrait;
use App\Jobs\Tenant\FastExportTraits\ModuleControllerTrait;
use App\Jobs\Tenant\FastExportTraits\SalaryHistoryTrait;
use App\Jobs\Tenant\FastExportTraits\WorkingShiftTrait;
use App\Models\Tenant\Employee\EmploymentStatus;
use App\Models\Tenant\WorkingShift\WorkingShift;
use App\Repositories\Core\Setting\SettingRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Rap2hpoutre\FastExcel\FastExcel;
use Rap2hpoutre\FastExcel\SheetCollection;

class FastExportJob implements ShouldQueue
{
    use Dispatchable;
    use ModuleControllerTrait, EmployeeTrait, LeaveTrait,
        WorkingShiftTrait, AttendanceTrait,
        LeaveTypeTrait, HolidayTrait,
        EmploymentHistoryTrait, DesignationHistoryTrait,
        DepartmentHistoryTrait, SalaryHistoryTrait, DepartmentTrait, LeaveHistoryTrait;

    protected string $file_path;
    protected WorkingShift $defaultWorkingShift;
    protected string $timezone;
    public function __construct($file_name, $requested_modules = [], $module_export_id = null, $timezone = null)
    {
        // Extract directory from full path
        $directory = dirname($file_name);
//        // Check if directory exists, if not, create it
        if (!Storage::disk('public')->exists($directory)) {
            Storage::disk('public')->makeDirectory($directory); // Recursive directory creation
        }
        $this->file_path = Storage::disk('public')->path($file_name);
        $this->requested_modules = $requested_modules;
        $this->module_export_id = $module_export_id;
        $this->defaultWorkingShift = WorkingShift::getDefault();
        $this->timezone = $timezone ?? timezone();
    }

    public function failed(\Throwable $exception)
    {
        // handle failed export
        $this->rejectExportAndNotify([
            'message' => $exception->getMessage(),
            // 'message' => 'Export failed.'
        ]);
        Log::info('Export : failed');
    }

    public function handle()
    {
        ini_set('max_execution_time', 600);
        ini_set('memory_limit', "512M");
        $sheets = $this->getSheets();
        if(count($sheets) <= 0) {
            // update status completed
            $this->completeExportAndNotify();
            Log::info('Export : completed with no sheet');
            return;
        };
        // Define data sets for each sheet
        $dataSets = new SheetCollection($sheets);

        // update status processing
        $this->processingExport();
        Log::info('Export : processing');

        $paidStatuses = '';
        $unpaidStatuses = '';
        if (in_array('leave_type', $this->requested_modules)) {
            $statuses = resolve(SettingRepository::class)->settings('leave')
                ->whereIn('name', ['statuses_for_paid_leave', 'statuses_for_unpaid_leave'])
                ->pluck('value', 'name')->toArray();
            $employmentStatuses = EmploymentStatus::query()->get(['id', 'name']);
            $paidStatuses = implode(',', $employmentStatuses->whereIn('id', json_decode($statuses['statuses_for_paid_leave']))->pluck('name')->toArray());
            $unpaidStatuses = implode(',', $employmentStatuses->whereIn('id', json_decode($statuses['statuses_for_unpaid_leave']))->pluck('name')->toArray());
        }
        // Export multiple sheets without chunking
        (new FastExcel($dataSets))->export($this->file_path, function ($row) use ($paidStatuses, $unpaidStatuses) {
            // Modify row data for holiday
            if ($row->sheet_name === 'holiday') {
                return $this->holidayMap($row);
            }
            // Modify row data for leave_type
            if ($row->sheet_name === 'leave_type') {
                $this->updateCompletedModule(['holiday']);
                return $this->leaveTypeMap($row, $paidStatuses, $unpaidStatuses);
            }
            // Modify row data for Employee
            if ($row->sheet_name === 'employee') {
                $this->updateCompletedModule(['holiday', 'leave_type']);
                return $this->employeeMap($row);
            }
            // Modify row data for employment_history
            if ($row->sheet_name === 'employment_history') {
                $this->updateCompletedModule(['holiday', 'leave_type', 'employee']);
                return $this->employmentHistoryMap($row);
            }
            // Modify row data for designation_history
            if ($row->sheet_name === 'designation_history') {
                $this->updateCompletedModule(['holiday', 'leave_type', 'employee', 'employment_history']);
                return $this->designationHistoryMap($row);
            }
            // Modify row data for department_history
            if ($row->sheet_name === 'department_history') {
                $this->updateCompletedModule(['holiday', 'leave_type', 'employee', 'employment_history', 'designation_history']);
                return $this->departmentHistoryMap($row);
            }
            // Modify row data for Working Shift
            if ($row->sheet_name === 'work_shift') {
                // employee completed
                $this->updateCompletedModule(['holiday', 'leave_type', 'employee', 'employment_history', 'designation_history', 'department_history']);
                return $this->workingShiftMap($row);
            }
            // Modify row data for Attendance
            if ($row->sheet_name === 'attendance') {
                // employee, Working Shift completed
                $this->updateCompletedModule(['holiday', 'leave_type', 'employee', 'employment_history', 'designation_history', 'department_history', 'work_shift']);
                return $this->attendanceMap($row);
            }
            // Modify row data for Leave
            if ($row->sheet_name === 'leave') {
                // employee, Working Shift, Attendance completed
                $this->updateCompletedModule(['holiday', 'leave_type', 'employee', 'employment_history', 'designation_history', 'department_history', 'work_shift', 'attendance']);
                return $this->leaveMap($row);
            }
            // Modify row data for Salary
            if ($row->sheet_name === 'salary_history') {
                // employee, Working Shift, Attendance completed
                $this->updateCompletedModule(['holiday', 'leave_type', 'employee', 'employment_history', 'designation_history', 'department_history', 'work_shift', 'attendance', 'leave']);
                return $this->salaryHistoryMap($row);
            }
            // Modify row data for Salary
            if ($row->sheet_name === 'department') {
                // employee, Working Shift, Attendance completed
                $this->updateCompletedModule(['holiday', 'leave_type', 'employee', 'employment_history', 'designation_history', 'department_history', 'work_shift', 'attendance', 'leave', 'salary_history']);
                return $this->departmentMap($row);
            }
            if ($row->sheet_name === 'leave_status_history') {
                // employee, Working Shift, Attendance completed
                $this->updateCompletedModule(['holiday', 'leave_type', 'employee', 'employment_history', 'designation_history', 'department_history', 'work_shift', 'attendance', 'leave', 'salary_history', 'department']);
                return $this->leaveHistoryMap($row);
            }

            return $row->toArray();
        });

        // employee, Working Shift, Leave completed
        $this->updateCompletedModule(['holiday', 'leave_type', 'employee', 'employment_history', 'designation_history', 'department_history', 'work_shift', 'attendance', 'leave', 'salary_history', 'department', 'leave_status_history']);

        // update status completed
        $this->completeExportAndNotify();
        Log::info('Export : completed');
    }
    protected function getSheets(): array {
        $sheets = [];
        if (in_array('holiday', $this->requested_modules)) {
            $sheets['Holiday'] = $this->HolidayDataGenerator();
        }
        if (in_array('leave_type', $this->requested_modules)) {
            $sheets['Leave_type'] = $this->LeaveTypeDataGenerator();
        }
        if (in_array('employee', $this->requested_modules)) {
            $sheets['Users'] = $this->EmployeeDataGenerator();
        }
        if (in_array('employment_history', $this->requested_modules)) {
            $sheets['Employment_status_history'] = $this->EmploymentHistoryDataGenerator();
        }
        if (in_array('designation_history', $this->requested_modules)) {
            $sheets['Designation_history'] = $this->DesignationHistoryDataGenerator();
        }
        if (in_array('department_history', $this->requested_modules)) {
            $sheets['Department_history'] = $this->DepartmentHistoryDataGenerator();
        }
        if (in_array('work_shift', $this->requested_modules)) {
            $sheets['Workshift'] = $this->WorkingShiftDataGenerator();
        }
        if (in_array('attendance', $this->requested_modules)) {
            $sheets['Timelog'] = $this->AttendanceDataGenerator();
        }
        if (in_array('leave', $this->requested_modules)) {
            $sheets['Leave'] = $this->LeaveDataGenerator();
        }
        if (in_array('salary_history', $this->requested_modules)) {
            $sheets['Salary History'] = $this->SalaryDataGenerator();
        }
        if (in_array('department', $this->requested_modules)) {
            $sheets['Department'] = $this->DepartmentDataGenerator();
        }
        if (in_array('leave_status_history', $this->requested_modules)) {
            $sheets['Leave_status_history'] = $this->LeaveHistoryDataGenerator();
        }
        return $sheets;
    }
}
