<?php

namespace App\Http\Controllers\Tenant\Payroll;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Payroll\ConsolidationReport;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ConsolidationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $consolidations = ConsolidationReport::query()
            ->when($request->get('search'), function ($query, $search) {
                $query->where('report_name', 'like', "%{$search}%")
                      ->orWhere('period', 'like', "%{$search}%");
            })
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 10));

        return response()->json($consolidations);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'report_name' => 'required|string|max:255',
            'period' => 'required|string|max:255',
            'total_employees' => 'required|integer|min:0',
            'total_amount' => 'required|numeric|min:0',
            'data' => 'nullable|array'
        ]);

        $consolidation = ConsolidationReport::create([
            'report_name' => $request->report_name,
            'period' => $request->period,
            'total_employees' => $request->total_employees,
            'total_amount' => $request->total_amount,
            'data' => $request->data ?? [],
            'created_by' => auth()->id(),
        ]);

        return response()->json([
            'message' => __t('consolidation_created_successfully'),
            'data' => $consolidation
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(ConsolidationReport $consolidation): JsonResponse
    {
        return response()->json($consolidation);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ConsolidationReport $consolidation): JsonResponse
    {
        $request->validate([
            'report_name' => 'required|string|max:255',
            'period' => 'required|string|max:255',
            'total_employees' => 'required|integer|min:0',
            'total_amount' => 'required|numeric|min:0',
            'data' => 'nullable|array'
        ]);

        $consolidation->update([
            'report_name' => $request->report_name,
            'period' => $request->period,
            'total_employees' => $request->total_employees,
            'total_amount' => $request->total_amount,
            'data' => $request->data ?? [],
            'updated_by' => auth()->id(),
        ]);

        return response()->json([
            'message' => __t('consolidation_updated_successfully'),
            'data' => $consolidation
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ConsolidationReport $consolidation): JsonResponse
    {
        $consolidation->delete();

        return response()->json([
            'message' => __t('consolidation_deleted_successfully')
        ]);
    }

    /**
     * Export consolidations to Excel
     */
    public function export(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        return \Maatwebsite\Excel\Facades\Excel::download(
            new \App\Exports\ConsolidationExport(),
            'consolidations.xlsx'
        );
    }

    /**
     * Generate consolidation report
     */
    public function generate(Request $request): JsonResponse
    {
        $request->validate([
            'period_start' => 'required|date',
            'period_end' => 'required|date|after_or_equal:period_start',
            'department_ids' => 'nullable|array',
            'employee_ids' => 'nullable|array'
        ]);

        // Logic to generate consolidation report based on payroll data
        // This would typically involve aggregating payroll data for the specified period
        
        $reportData = [
            'period' => $request->period_start . ' to ' . $request->period_end,
            'total_employees' => 0, // Calculate based on actual data
            'total_amount' => 0, // Calculate based on actual data
            'details' => [] // Detailed breakdown
        ];

        return response()->json([
            'message' => __t('consolidation_report_generated_successfully'),
            'data' => $reportData
        ]);
    }
}
