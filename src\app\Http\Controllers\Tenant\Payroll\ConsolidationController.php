<?php

namespace App\Http\Controllers\Tenant\Payroll;

use App\Exceptions\GeneralException;
use App\Exports\ConsolidationExport;
use App\Http\Controllers\Controller;
use App\Models\Tenant\Payroll\Payslip;
use App\Models\Tenant\Payroll\ConsolidationReport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Barryvdh\DomPDF\Facade\Pdf;

class ConsolidationController extends Controller
{
    /**
     * Display consolidation dashboard
     */
    public function index(Request $request)
    {
        if (!authorize_any(['view_consolidation'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $period = $request->period ?? 'thisMonth';
        $departmentId = $request->department_id;

        // Get date range based on period
        $dateRange = $this->getDateRange($period);

        // Get consolidation data by department
        $query = Payslip::with(['user.department', 'user.designation', 'beneficiaries'])
            ->whereBetween('start_date', $dateRange)
            ->whereNotNull('status_id');

        if ($departmentId) {
            $query->whereHas('user.department', function ($q) use ($departmentId) {
                $q->where('id', $departmentId);
            });
        }

        $payslips = $query->get();

        // Group by department and calculate totals
        $consolidationData = $payslips->groupBy('user.department.name')->map(function ($departmentPayslips, $departmentName) {
            $employees = $departmentPayslips->unique('user_id');
            $totalGross = $departmentPayslips->sum('basic_salary');
            $totalBeneficiaries = $departmentPayslips->flatMap->beneficiaries->sum('amount');
            $totalDeductions = $departmentPayslips->flatMap->beneficiaries
                ->where('beneficiary.type', 'deduction')->sum('amount');
            $totalNet = $departmentPayslips->sum('net_salary');

            return [
                'department' => $departmentName ?: 'Unassigned',
                'employee_count' => $employees->count(),
                'total_gross' => number_format($totalGross, 2),
                'total_beneficiaries' => number_format($totalBeneficiaries, 2),
                'total_deductions' => number_format($totalDeductions, 2),
                'net_amount' => number_format($totalNet, 2),
                'status' => [
                    'name' => 'Processed',
                    'class' => 'success',
                    'translated_name' => 'Processed'
                ]
            ];
        })->values();

        return response()->json([
            'data' => $consolidationData->toArray(),
            'total' => $consolidationData->count(),
            'per_page' => $consolidationData->count(),
            'current_page' => 1,
            'last_page' => 1
        ]);
    }

    /**
     * Generate consolidation report
     */
    public function generate(Request $request)
    {
        if (!authorize_any(['generate_consolidation'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $request->validate([
            'period_start' => 'required|date',
            'period_end' => 'required|date|after_or_equal:period_start',
            'department_ids' => 'nullable|array',
            'department_ids.*' => 'exists:departments,id',
            'report_type' => 'required|in:summary,detailed,departmental'
        ]);

        try {
            DB::beginTransaction();

            $payslipsQuery = Payslip::with(['user.department', 'user.designation', 'beneficiaries.beneficiary'])
                ->whereBetween('start_date', [$request->period_start, $request->period_end])
                ->whereNotNull('status_id');

            if ($request->department_ids) {
                $payslipsQuery->whereHas('user.department', function ($q) use ($request) {
                    $q->whereIn('id', $request->department_ids);
                });
            }

            $payslips = $payslipsQuery->get();

            if ($payslips->isEmpty()) {
                return response()->json([
                    'status' => false,
                    'message' => 'No payslips found for the specified criteria'
                ], 400);
            }

            // Generate report data based on type
            $reportData = $this->generateReportData($payslips, $request->report_type);

            // Create consolidation report record
            $consolidationReport = ConsolidationReport::create([
                'report_type' => $request->report_type,
                'period_start' => $request->period_start,
                'period_end' => $request->period_end,
                'department_ids' => $request->department_ids,
                'total_employees' => $payslips->unique('user_id')->count(),
                'total_amount' => $payslips->sum('net_salary'),
                'total_departments' => $payslips->unique('user.department.id')->count(),
                'report_data' => $reportData,
                'generated_by' => auth()->id(),
                'status_id' => 1 // Generated
            ]);

            DB::commit();

            return response()->json([
                'status' => true,
                'message' => 'Consolidation report generated successfully',
                'data' => $consolidationReport
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => false,
                'message' => 'Failed to generate consolidation report: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get consolidation summary statistics
     */
    public function summary(Request $request)
    {
        if (!authorize_any(['view_consolidation'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $period = $request->period ?? 'thisMonth';
        $dateRange = $this->getDateRange($period);

        $payslips = Payslip::with(['user.department', 'beneficiaries'])
            ->whereBetween('start_date', $dateRange)
            ->whereNotNull('status_id')
            ->get();

        $summary = [
            'totalEmployees' => $payslips->unique('user_id')->count(),
            'totalAmount' => '₱' . number_format($payslips->sum('net_salary'), 2),
            'totalDepartments' => $payslips->unique('user.department.id')->count(),
            'pendingApprovals' => ConsolidationReport::where('status_id', 1)->count(),
            'departmentBreakdown' => $payslips->groupBy('user.department.name')->map(function ($group, $dept) {
                return [
                    'department' => $dept ?: 'Unassigned',
                    'employee_count' => $group->unique('user_id')->count(),
                    'total_amount' => $group->sum('net_salary')
                ];
            })->values()
        ];

        return response()->json([
            'status' => true,
            'data' => $summary
        ]);
    }

    /**
     * Export consolidation report as PDF
     */
    public function exportPdf(Request $request)
    {
        if (!authorize_any(['export_consolidation'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $reportId = $request->report_id;
        $report = ConsolidationReport::with(['generatedBy'])->findOrFail($reportId);

        $pdf = PDF::loadView('tenant.payroll.exports.consolidation-pdf', [
            'report' => $report,
            'generated_at' => now(),
            'generated_by' => auth()->user()->full_name
        ]);

        return $pdf->download('consolidation-report-' . date('Y-m-d') . '.pdf');
    }

    /**
     * Export consolidation report as Excel
     */
    public function exportExcel(Request $request)
    {
        if (!authorize_any(['export_consolidation'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $reportId = $request->report_id;

        return Excel::download(
            new ConsolidationExport($reportId),
            'consolidation-report-' . date('Y-m-d') . '.xlsx'
        );
    }

    /**
     * Get department details for consolidation
     */
    public function departmentDetails(Request $request)
    {
        if (!authorize_any(['view_consolidation'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $departmentId = $request->department_id;
        $period = $request->period ?? 'thisMonth';
        $dateRange = $this->getDateRange($period);

        $payslips = Payslip::with(['user', 'beneficiaries.beneficiary'])
            ->whereHas('user.department', function ($q) use ($departmentId) {
                $q->where('id', $departmentId);
            })
            ->whereBetween('start_date', $dateRange)
            ->whereNotNull('status_id')
            ->get();

        $employeeDetails = $payslips->groupBy('user_id')->map(function ($employeePayslips) {
            $employee = $employeePayslips->first()->user;
            return [
                'employee_name' => $employee->full_name,
                'employee_id' => $employee->employee_id,
                'position' => $employee->designation->name ?? 'N/A',
                'payslip_count' => $employeePayslips->count(),
                'total_gross' => $employeePayslips->sum('basic_salary'),
                'total_net' => $employeePayslips->sum('net_salary'),
                'total_deductions' => $employeePayslips->flatMap->beneficiaries
                    ->where('beneficiary.type', 'deduction')->sum('amount')
            ];
        })->values();

        return response()->json([
            'status' => true,
            'data' => $employeeDetails
        ]);
    }

    /**
     * Get date range based on period
     */
    private function getDateRange($period)
    {
        switch ($period) {
            case 'thisMonth':
                return [now()->startOfMonth(), now()->endOfMonth()];
            case 'lastMonth':
                return [now()->subMonth()->startOfMonth(), now()->subMonth()->endOfMonth()];
            case 'thisQuarter':
                return [now()->startOfQuarter(), now()->endOfQuarter()];
            case 'lastQuarter':
                return [now()->subQuarter()->startOfQuarter(), now()->subQuarter()->endOfQuarter()];
            case 'thisYear':
                return [now()->startOfYear(), now()->endOfYear()];
            case 'lastYear':
                return [now()->subYear()->startOfYear(), now()->subYear()->endOfYear()];
            default:
                return [now()->startOfMonth(), now()->endOfMonth()];
        }
    }

    /**
     * Generate report data based on type
     */
    private function generateReportData($payslips, $reportType)
    {
        switch ($reportType) {
            case 'summary':
                return $this->generateSummaryReport($payslips);
            case 'detailed':
                return $this->generateDetailedReport($payslips);
            case 'departmental':
                return $this->generateDepartmentalReport($payslips);
            default:
                return $this->generateSummaryReport($payslips);
        }
    }

    private function generateSummaryReport($payslips)
    {
        return [
            'total_employees' => $payslips->unique('user_id')->count(),
            'total_payslips' => $payslips->count(),
            'total_gross' => $payslips->sum('basic_salary'),
            'total_net' => $payslips->sum('net_salary'),
            'departments' => $payslips->unique('user.department.id')->count()
        ];
    }

    private function generateDetailedReport($payslips)
    {
        return $payslips->map(function ($payslip) {
            return [
                'employee_name' => $payslip->user->full_name,
                'department' => $payslip->user->department->name ?? 'Unassigned',
                'position' => $payslip->user->designation->name ?? 'N/A',
                'period' => $payslip->start_date . ' to ' . $payslip->end_date,
                'gross_salary' => $payslip->basic_salary,
                'net_salary' => $payslip->net_salary,
                'deductions' => $payslip->beneficiaries->where('beneficiary.type', 'deduction')->sum('amount')
            ];
        });
    }

    private function generateDepartmentalReport($payslips)
    {
        return $payslips->groupBy('user.department.name')->map(function ($departmentPayslips, $departmentName) {
            return [
                'department' => $departmentName ?: 'Unassigned',
                'employee_count' => $departmentPayslips->unique('user_id')->count(),
                'total_gross' => $departmentPayslips->sum('basic_salary'),
                'total_net' => $departmentPayslips->sum('net_salary'),
                'employees' => $departmentPayslips->unique('user_id')->map(function ($payslip) {
                    return [
                        'name' => $payslip->user->full_name,
                        'position' => $payslip->user->designation->name ?? 'N/A',
                        'net_salary' => $payslip->net_salary
                    ];
                })
            ];
        });
    }
}
