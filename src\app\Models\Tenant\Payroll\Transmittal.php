<?php

namespace App\Models\Tenant\Payroll;

use App\Models\Core\BaseModel;
use App\Models\Core\Auth\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Transmittal extends BaseModel
{
    protected $fillable = [
        'department_id',
        'payrun_id',
        'status_id',
        'generated_by',
        'metadata',
        'reference_number',
        'notes',
        'approved_at',
        'approved_by',
        'payroll_period',
        'employee_ids',
        'remarks',
        'payslip_ids',
        'department',
        'payroll_period_start',
        'payroll_period_end',
        'total_amount',
        'total_employees',
        'prepared_by',
        'prepared_by_title'
    ];

    protected $casts = [
        'metadata' => 'array',
        'employee_ids' => 'array',
        'payslip_ids' => 'array',
        'payroll_period_start' => 'date',
        'payroll_period_end' => 'date',
        'total_amount' => 'decimal:2',
        'approved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Get the user who created this transmittal.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this transmittal.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the payslips associated with this transmittal.
     */
    public function payslips()
    {
        if (!$this->payslip_ids) {
            return collect();
        }

        return Payslip::whereIn('id', $this->payslip_ids)
            ->with(['user.profile', 'user.department', 'payrun'])
            ->get();
    }

    /**
     * Get formatted payroll period.
     */
    public function getFormattedPayrollPeriodAttribute(): string
    {
        if (!$this->payroll_period_start || !$this->payroll_period_end) {
            return '';
        }

        return $this->payroll_period_start->format('M d') . ' - ' . $this->payroll_period_end->format('M d, Y');
    }

    /**
     * Scope to filter by title or description.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%");
        });
    }

    /**
     * Get formatted created date.
     */
    public function getFormattedCreatedAtAttribute()
    {
        return $this->created_at->format('M d, Y h:i A');
    }

    /**
     * Get formatted updated date.
     */
    public function getFormattedUpdatedAtAttribute()
    {
        return $this->updated_at->format('M d, Y h:i A');
    }
}
