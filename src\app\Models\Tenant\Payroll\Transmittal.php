<?php

namespace App\Models\Tenant\Payroll;

use App\Models\Core\BaseModel;
use App\Models\Core\Auth\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Transmittal extends BaseModel
{
    protected $fillable = [
        'department_id',
        'payrun_id',
        'status_id',
        'generated_by',
        'metadata',
        'reference_number',
        'notes',
        'approved_at',
        'approved_by',
        'payroll_period',
        'employee_ids',
        'remarks',
        'payslip_ids',
        'department',
        'payroll_period_start',
        'payroll_period_end',
        'total_amount',
        'total_employees',
        'prepared_by',
        'prepared_by_title'
    ];

    protected $casts = [
        'metadata' => 'array',
        'employee_ids' => 'array',
        'payslip_ids' => 'array',
        'payroll_period_start' => 'date',
        'payroll_period_end' => 'date',
        'total_amount' => 'decimal:2',
        'approved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Get the user who generated this transmittal.
     */
    public function generator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'generated_by');
    }

    /**
     * Get the user who approved this transmittal.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the department associated with this transmittal.
     */
    public function departmentRelation(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Core\Auth\User::class, 'department_id');
    }

    /**
     * Get the payrun associated with this transmittal.
     */
    public function payrun(): BelongsTo
    {
        return $this->belongsTo(Payrun::class, 'payrun_id');
    }

    /**
     * Get the status of this transmittal.
     */
    public function status(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Core\Setting\Status::class, 'status_id');
    }

    /**
     * Get the payslips associated with this transmittal.
     */
    public function payslips()
    {
        if (!$this->payslip_ids) {
            // Return sample data for testing if no payslips are selected
            return collect([
                (object)[
                    'id' => 1,
                    'net_salary' => 11146.21,
                    'user' => (object)[
                        'id' => 1,
                        'full_name' => 'Arturo B. Silva, Jr.',
                        'employee_id' => '100-2024',
                        'designation' => (object)['name' => 'Electrical Supervisor'],
                        'department' => (object)['name' => 'Electrical']
                    ]
                ],
                (object)[
                    'id' => 2,
                    'net_salary' => 12071.85,
                    'user' => (object)[
                        'id' => 2,
                        'full_name' => 'Rochelle L. Tubongbanua',
                        'employee_id' => '101-2024',
                        'designation' => (object)['name' => 'Electrical Supervisor'],
                        'department' => (object)['name' => 'Electrical']
                    ]
                ],
                (object)[
                    'id' => 3,
                    'net_salary' => 11626.10,
                    'user' => (object)[
                        'id' => 3,
                        'full_name' => 'Mary Liliosa Angel T. Camancho',
                        'employee_id' => '102-2024',
                        'designation' => (object)['name' => 'Electrical Supervisor'],
                        'department' => (object)['name' => 'Electrical']
                    ]
                ]
            ]);
        }

        return Payslip::whereIn('id', $this->payslip_ids)
            ->with(['user.profile', 'user.department', 'user.designation', 'payrun'])
            ->get();
    }

    /**
     * Get formatted payroll period.
     */
    public function getFormattedPayrollPeriodAttribute(): string
    {
        if (!$this->payroll_period_start || !$this->payroll_period_end) {
            return '';
        }

        return $this->payroll_period_start->format('M d') . ' - ' . $this->payroll_period_end->format('M d, Y');
    }

    /**
     * Scope to filter by title or description.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%");
        });
    }

    /**
     * Get formatted created date.
     */
    public function getFormattedCreatedAtAttribute()
    {
        return $this->created_at->format('M d, Y h:i A');
    }

    /**
     * Get formatted updated date.
     */
    public function getFormattedUpdatedAtAttribute()
    {
        return $this->updated_at->format('M d, Y h:i A');
    }
}
