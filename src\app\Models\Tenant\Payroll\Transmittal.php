<?php

namespace App\Models\Tenant\Payroll;

use App\Models\Core\Auth\User;
use App\Models\Core\Status;
use App\Models\Core\Traits\StatusRelationship;
use App\Models\Tenant\TenantModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Transmittal extends TenantModel
{
    use HasFactory, StatusRelationship;

    protected $fillable = [
        'employee_id',
        'period_start',
        'period_end',
        'total_amount',
        'payslip_count',
        'status_id',
        'generated_by',
        'metadata',
        'reference_number',
        'notes',
        'approved_at',
        'approved_by'
    ];

    protected $casts = [
        'period_start' => 'date',
        'period_end' => 'date',
        'total_amount' => 'decimal:2',
        'metadata' => 'array',
        'approved_at' => 'datetime'
    ];

    protected $appends = [
        'formatted_amount',
        'period_display',
        'status_badge'
    ];

    /**
     * Get the employee that owns the transmittal
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'employee_id');
    }

    /**
     * Get the user who generated the transmittal
     */
    public function generatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'generated_by');
    }

    /**
     * Get the user who approved the transmittal
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the status of the transmittal
     */
    public function status(): BelongsTo
    {
        return $this->belongsTo(Status::class);
    }

    /**
     * Get formatted amount attribute
     */
    public function getFormattedAmountAttribute(): string
    {
        return '₱' . number_format($this->total_amount, 2);
    }

    /**
     * Get period display attribute
     */
    public function getPeriodDisplayAttribute(): string
    {
        return $this->period_start->format('M d, Y') . ' - ' . $this->period_end->format('M d, Y');
    }

    /**
     * Get status badge attribute
     */
    public function getStatusBadgeAttribute(): array
    {
        $status = $this->status;
        return [
            'name' => $status->name ?? 'unknown',
            'class' => $status->class ?? 'secondary',
            'translated_name' => $status->translated_name ?? 'Unknown'
        ];
    }

    /**
     * Scope for filtering by period
     */
    public function scopeForPeriod($query, $startDate, $endDate)
    {
        return $query->whereBetween('period_start', [$startDate, $endDate])
                    ->orWhereBetween('period_end', [$startDate, $endDate]);
    }

    /**
     * Scope for filtering by employee
     */
    public function scopeForEmployee($query, $employeeId)
    {
        return $query->where('employee_id', $employeeId);
    }

    /**
     * Scope for filtering by status
     */
    public function scopeWithStatus($query, $statusId)
    {
        return $query->where('status_id', $statusId);
    }

    /**
     * Scope for approved transmittals
     */
    public function scopeApproved($query)
    {
        return $query->whereNotNull('approved_at');
    }

    /**
     * Scope for pending transmittals
     */
    public function scopePending($query)
    {
        return $query->whereNull('approved_at');
    }

    /**
     * Generate reference number
     */
    public function generateReferenceNumber(): string
    {
        $prefix = 'TXM';
        $year = $this->created_at->format('Y');
        $month = $this->created_at->format('m');
        $sequence = str_pad($this->id, 4, '0', STR_PAD_LEFT);
        
        return "{$prefix}-{$year}{$month}-{$sequence}";
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::created(function ($transmittal) {
            if (!$transmittal->reference_number) {
                $transmittal->update([
                    'reference_number' => $transmittal->generateReferenceNumber()
                ]);
            }
        });
    }
}
