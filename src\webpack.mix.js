const mix = require('laravel-mix');
/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */

mix.setPublicPath('public')
    .setResourceRoot('../')
    .sass('resources/sass/payslip.scss', 'css/payslip.css')
    .sass('resources/sass/core/core.scss', 'css/core.css')
    .sass('node_modules/dropzone/src/dropzone.scss', 'css/dropzone.css')
    .sass('resources/sass/_global.scss', 'css/fontawesome.css')
    .js('resources/js/mainApp.js', 'js/core.js').vue()
    .extract([
        'jquery',
        'bootstrap',
        'popper.js',
        'axios',
        'sweetalert2',
        'lodash'
    ])
    .sourceMaps();

if (mix.inProduction()) {
    mix.version()
        .options({
            terser: {}
        });
}

// Always copy to root directories for deployment (after build)
mix.then(() => {
    const fs = require('fs-extra');

    // Copy directories
    fs.copySync('public/js', '../js');
    fs.copySync('public/css', '../css');
    fs.copySync('public/fonts', '../fonts');

    // Copy mix-manifest.json to root directory so Laravel can find it
    fs.copySync('public/mix-manifest.json', '../mix-manifest.json');

    console.log('Files copied to root directory successfully!');
});
