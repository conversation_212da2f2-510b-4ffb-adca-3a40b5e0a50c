<?php

namespace App\Exports;

use App\Models\Tenant\Payroll\Transmittal;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use Illuminate\Contracts\View\View;

class TransmittalPayrollExport implements FromView, WithStyles, ShouldAutoSize
{
    protected $transmittal;

    public function __construct(Transmittal $transmittal)
    {
        $this->transmittal = $transmittal;
    }

    public function view(): View
    {
        $payslips = $this->transmittal->payslips();
        
        // Group payslips by department if needed
        $groupedPayslips = $payslips->groupBy(function($payslip) {
            return $payslip->user->department->name ?? 'No Department';
        });

        return view('exports.transmittal-payroll', [
            'transmittal' => $this->transmittal,
            'payslips' => $payslips,
            'groupedPayslips' => $groupedPayslips,
            'companyName' => config('app.name', 'Company Name'),
            'companyAddress' => setting('company_address', 'Company Address')
        ]);
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Company name styling
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 14,
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],
            
            // Company address styling
            2 => [
                'font' => [
                    'italic' => true,
                    'size' => 10,
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],
            
            // Department header styling
            4 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],
            
            // Payroll period styling
            6 => [
                'font' => [
                    'bold' => true,
                    'size' => 10,
                ],
            ],
            
            // Table headers styling
            8 => [
                'font' => [
                    'bold' => true,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => [
                        'argb' => 'FFE0E0E0',
                    ],
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                    ],
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],
        ];
    }
}
