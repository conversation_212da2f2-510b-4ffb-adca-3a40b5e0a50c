9999999999O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:27:{i:0;O:8:"stdClass":5:{s:2:"id";i:1;s:4:"name";s:13:"status_active";s:4:"type";s:4:"user";s:5:"class";s:7:"success";s:15:"translated_name";s:6:"Active";}i:1;O:8:"stdClass":5:{s:2:"id";i:2;s:4:"name";s:15:"status_inactive";s:4:"type";s:4:"user";s:5:"class";s:6:"danger";s:15:"translated_name";s:8:"Inactive";}i:2;O:8:"stdClass":5:{s:2:"id";i:3;s:4:"name";s:14:"status_invited";s:4:"type";s:4:"user";s:5:"class";s:7:"warning";s:15:"translated_name";s:7:"Invited";}i:3;O:8:"stdClass":5:{s:2:"id";i:4;s:4:"name";s:13:"status_active";s:4:"type";s:10:"department";s:5:"class";s:7:"primary";s:15:"translated_name";s:6:"Active";}i:4;O:8:"stdClass":5:{s:2:"id";i:5;s:4:"name";s:15:"status_inactive";s:4:"type";s:10:"department";s:5:"class";s:6:"danger";s:15:"translated_name";s:8:"Inactive";}i:5;O:8:"stdClass":5:{s:2:"id";i:6;s:4:"name";s:14:"status_pending";s:4:"type";s:10:"attendance";s:5:"class";s:7:"warning";s:15:"translated_name";s:7:"Pending";}i:6;O:8:"stdClass":5:{s:2:"id";i:7;s:4:"name";s:14:"status_approve";s:4:"type";s:10:"attendance";s:5:"class";s:7:"success";s:15:"translated_name";s:8:"Approved";}i:7;O:8:"stdClass":5:{s:2:"id";i:8;s:4:"name";s:13:"status_reject";s:4:"type";s:10:"attendance";s:5:"class";s:6:"danger";s:15:"translated_name";s:8:"Rejected";}i:8;O:8:"stdClass":5:{s:2:"id";i:9;s:4:"name";s:10:"status_log";s:4:"type";s:10:"attendance";s:5:"class";s:9:"secondary";s:15:"translated_name";s:3:"Log";}i:9;O:8:"stdClass":5:{s:2:"id";i:10;s:4:"name";s:13:"status_cancel";s:4:"type";s:10:"attendance";s:5:"class";s:4:"dark";s:15:"translated_name";s:8:"Canceled";}i:10;O:8:"stdClass":5:{s:2:"id";i:11;s:4:"name";s:14:"status_pending";s:4:"type";s:5:"leave";s:5:"class";s:7:"warning";s:15:"translated_name";s:7:"Pending";}i:11;O:8:"stdClass":5:{s:2:"id";i:12;s:4:"name";s:15:"status_approved";s:4:"type";s:5:"leave";s:5:"class";s:7:"success";s:15:"translated_name";s:8:"Approved";}i:12;O:8:"stdClass":5:{s:2:"id";i:13;s:4:"name";s:15:"status_rejected";s:4:"type";s:5:"leave";s:5:"class";s:6:"danger";s:15:"translated_name";s:8:"Rejected";}i:13;O:8:"stdClass":5:{s:2:"id";i:14;s:4:"name";s:15:"status_bypassed";s:4:"type";s:5:"leave";s:5:"class";s:9:"secondary";s:15:"translated_name";s:8:"Bypassed";}i:14;O:8:"stdClass":5:{s:2:"id";i:15;s:4:"name";s:15:"status_canceled";s:4:"type";s:5:"leave";s:5:"class";s:4:"dark";s:15:"translated_name";s:8:"Canceled";}i:15;O:8:"stdClass":5:{s:2:"id";i:16;s:4:"name";s:16:"status_generated";s:4:"type";s:6:"payrun";s:5:"class";s:9:"secondary";s:15:"translated_name";s:9:"Generated";}i:16;O:8:"stdClass":5:{s:2:"id";i:17;s:4:"name";s:11:"status_sent";s:4:"type";s:6:"payrun";s:5:"class";s:7:"success";s:15:"translated_name";s:4:"Sent";}i:17;O:8:"stdClass":5:{s:2:"id";i:18;s:4:"name";s:16:"status_partially";s:4:"type";s:6:"payrun";s:5:"class";s:7:"warning";s:15:"translated_name";s:24:"default.status_partially";}i:18;O:8:"stdClass":5:{s:2:"id";i:19;s:4:"name";s:16:"status_generated";s:4:"type";s:7:"payslip";s:5:"class";s:7:"warning";s:15:"translated_name";s:9:"Generated";}i:19;O:8:"stdClass":5:{s:2:"id";i:20;s:4:"name";s:11:"status_sent";s:4:"type";s:7:"payslip";s:5:"class";s:7:"success";s:15:"translated_name";s:4:"Sent";}i:20;O:8:"stdClass":5:{s:2:"id";i:21;s:4:"name";s:14:"status_pending";s:4:"type";s:7:"payslip";s:5:"class";s:9:"secondary";s:15:"translated_name";s:7:"Pending";}i:21;O:8:"stdClass":5:{s:2:"id";i:22;s:4:"name";s:14:"status_pending";s:4:"type";s:6:"export";s:5:"class";s:9:"secondary";s:15:"translated_name";s:7:"Pending";}i:22;O:8:"stdClass":5:{s:2:"id";i:23;s:4:"name";s:17:"status_processing";s:4:"type";s:6:"export";s:5:"class";s:7:"primary";s:15:"translated_name";s:10:"Processing";}i:23;O:8:"stdClass":5:{s:2:"id";i:24;s:4:"name";s:15:"status_rejected";s:4:"type";s:6:"export";s:5:"class";s:6:"danger";s:15:"translated_name";s:8:"Rejected";}i:24;O:8:"stdClass":5:{s:2:"id";i:25;s:4:"name";s:16:"status_completed";s:4:"type";s:6:"export";s:5:"class";s:7:"success";s:15:"translated_name";s:9:"Completed";}i:25;O:8:"stdClass":5:{s:2:"id";i:26;s:4:"name";s:13:"status_active";s:4:"type";s:11:"transmittal";s:5:"class";s:7:"success";s:15:"translated_name";s:6:"Active";}i:26;O:8:"stdClass":5:{s:2:"id";i:27;s:4:"name";s:15:"status_inactive";s:4:"type";s:11:"transmittal";s:5:"class";s:6:"danger";s:15:"translated_name";s:8:"Inactive";}}s:28:" * escapeWhenCastingToString";b:0;}