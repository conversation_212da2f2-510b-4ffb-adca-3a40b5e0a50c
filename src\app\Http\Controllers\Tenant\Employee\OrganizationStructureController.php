<?php

namespace App\Http\Controllers\Tenant\Employee;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Employee\Department;

class OrganizationStructureController extends Controller
{
    public function index()
    {
        // Recursive function to load nested relationships
        $loadNested = function ($query, $depth = 1) use (&$loadNested) {
            $maxDepth = 20; // Maximum depth of 20 levels
            
            if ($depth > $maxDepth) {
                return;
            }
            
            $query->with([
                'manager:id,first_name,last_name,email',
                'manager.profilePicture',
                'childDepartments' => function ($subQuery) use ($loadNested, $depth) {
                    $loadNested($subQuery, $depth + 1);
                }
            ]);
        };

        // Start building the query with the base department
        $query = Department::with([
            'manager:id,first_name,last_name,email',
            'manager.profilePicture',
            'childDepartments' => function ($query) use ($loadNested) {
                $loadNested($query);
            }
        ])->whereNull('department_id');

        return $query->first();
    }
}
