<?php

namespace App\Exports;

use App\Models\Tenant\Payroll\Transmittal;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class TransmittalExport implements FromCollection, WithHeadings, WithMapping, WithStyles
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return Transmittal::with(['creator', 'updater'])->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            'Title',
            'Description',
            'Created By',
            'Created At',
            'Updated By',
            'Updated At'
        ];
    }

    /**
     * @param mixed $transmittal
     * @return array
     */
    public function map($transmittal): array
    {
        return [
            $transmittal->id,
            $transmittal->title,
            $transmittal->description,
            $transmittal->creator ? $transmittal->creator->full_name : 'N/A',
            $transmittal->formatted_created_at,
            $transmittal->updater ? $transmittal->updater->full_name : 'N/A',
            $transmittal->formatted_updated_at
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true]],
        ];
    }
}
