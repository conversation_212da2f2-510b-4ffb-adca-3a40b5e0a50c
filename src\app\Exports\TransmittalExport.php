<?php

namespace App\Exports;

use App\Models\Tenant\Payroll\Transmittal;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;

class TransmittalExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize, WithTitle, WithEvents
{
    protected $transmittalIds;

    public function __construct($transmittalIds = [])
    {
        $this->transmittalIds = $transmittalIds;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = Transmittal::with(['employee.department', 'employee.designation', 'status']);

        if (!empty($this->transmittalIds)) {
            $query->whereIn('id', $this->transmittalIds);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'EMPLOYEE NUMBER',
            'NAME',
            'DESIGNATION',
            'TOTAL SALARY'
        ];
    }

    /**
     * @param mixed $transmittal
     * @return array
     */
    public function map($transmittal): array
    {
        return [
            $transmittal->employee->employee_id ?? $transmittal->reference_number,
            $transmittal->employee->full_name,
            $transmittal->employee->designation->name ?? 'N/A',
            number_format($transmittal->total_amount, 2)
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Payroll Transmittal';
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();

                // Add company header
                $sheet->insertNewRowBefore(1, 4);
                $sheet->setCellValue('A1', 'STA. CATALINA TOLONG MILLING CORPORATION');
                $sheet->setCellValue('A2', 'Sitio, Sicopong Brgy. Caranoche, Sta. Catalina, Negros Oriental');
                $sheet->setCellValue('A3', 'ELECTRICAL');

                // Get period from first transmittal
                $transmittals = $this->collection();
                if ($transmittals->isNotEmpty()) {
                    $firstTransmittal = $transmittals->first();
                    $periodText = 'PAYROLL COVERED AS OF ' .
                        strtoupper($firstTransmittal->period_start->format('M d')) . '-' .
                        strtoupper($firstTransmittal->period_end->format('M d, Y'));
                    $sheet->setCellValue('A4', $periodText);
                }

                // Style company header
                $sheet->getStyle('A1:D1')->applyFromArray([
                    'font' => ['bold' => true, 'size' => 14],
                    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
                ]);
                $sheet->getStyle('A2:D2')->applyFromArray([
                    'font' => ['italic' => true, 'size' => 10],
                    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
                ]);
                $sheet->getStyle('A3:D3')->applyFromArray([
                    'font' => ['bold' => true, 'size' => 12],
                    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
                ]);
                $sheet->getStyle('A4:D4')->applyFromArray([
                    'font' => ['bold' => true, 'size' => 11],
                    'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
                ]);

                // Style table headers (now at row 5)
                $sheet->getStyle('A5:D5')->applyFromArray([
                    'font' => ['bold' => true],
                    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                    'borders' => [
                        'allBorders' => ['borderStyle' => Border::BORDER_THICK]
                    ]
                ]);

                // Add total row
                $lastRow = $sheet->getHighestRow();
                $totalAmount = 0;

                // Calculate total
                foreach ($transmittals as $transmittal) {
                    $totalAmount += $transmittal->total_amount;
                }

                $sheet->setCellValue('A' . ($lastRow + 1), 'TOTAL');
                $sheet->setCellValue('D' . ($lastRow + 1), number_format($totalAmount, 2));

                // Style total row
                $sheet->getStyle('A' . ($lastRow + 1) . ':D' . ($lastRow + 1))->applyFromArray([
                    'font' => ['bold' => true],
                    'borders' => [
                        'top' => ['borderStyle' => Border::BORDER_THICK]
                    ]
                ]);

                // Add signature section
                $signatureRow = $lastRow + 3;
                $sheet->setCellValue('A' . $signatureRow, 'Prepared By:');
                $sheet->setCellValue('A' . ($signatureRow + 2), 'Via Mae N. Tembrevilla');
                $sheet->setCellValue('A' . ($signatureRow + 3), 'Payroll Analyst');

                // Style signature
                $sheet->getStyle('A' . $signatureRow)->applyFromArray([
                    'font' => ['bold' => true]
                ]);
                $sheet->getStyle('A' . ($signatureRow + 2))->applyFromArray([
                    'font' => ['bold' => true]
                ]);
            }
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the data rows with borders
            'A:D' => [
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN]
                ]
            ]
        ];
    }
}
