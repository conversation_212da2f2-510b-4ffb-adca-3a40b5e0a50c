<?php

namespace App\Exports;

use App\Models\Tenant\Payroll\Transmittal;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class TransmittalExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $transmittalIds;

    public function __construct($transmittalIds = [])
    {
        $this->transmittalIds = $transmittalIds;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = Transmittal::with(['employee.department', 'employee.designation', 'status']);

        if (!empty($this->transmittalIds)) {
            $query->whereIn('id', $this->transmittalIds);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Reference Number',
            'Employee Name',
            'Employee ID',
            'Department',
            'Position',
            'Period Start',
            'Period End',
            'Total Amount',
            'Payslip Count',
            'Status',
            'Generated Date',
            'Generated By'
        ];
    }

    /**
     * @param mixed $transmittal
     * @return array
     */
    public function map($transmittal): array
    {
        return [
            $transmittal->reference_number,
            $transmittal->employee->full_name,
            $transmittal->employee->employee_id ?? 'N/A',
            $transmittal->employee->department->name ?? 'Unassigned',
            $transmittal->employee->designation->name ?? 'N/A',
            $transmittal->period_start->format('Y-m-d'),
            $transmittal->period_end->format('Y-m-d'),
            number_format($transmittal->total_amount, 2),
            $transmittal->payslip_count,
            $transmittal->status->name ?? 'Unknown',
            $transmittal->created_at->format('Y-m-d H:i:s'),
            $transmittal->generatedBy->full_name ?? 'System'
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text
            1 => ['font' => ['bold' => true]],
            
            // Style the header row with background color
            1 => [
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFE2E2E2']
                ]
            ]
        ];
    }
}
