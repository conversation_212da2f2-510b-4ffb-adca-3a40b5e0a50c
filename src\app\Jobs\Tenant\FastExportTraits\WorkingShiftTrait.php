<?php

namespace App\Jobs\Tenant\FastExportTraits;

use App\Helpers\Core\Traits\FileHandler;
use App\Models\Tenant\WorkingShift\WorkingShift;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

trait WorkingShiftTrait
{
    use FileHandler;
    // Define Working shift generator function
    public function WorkingShiftDataGenerator(): \Generator
    {
        foreach (WorkingShift::query()
                     ->select('name', 'working_shifts.start_date as start_date', 'working_shifts.end_date as end_date')
                     ->selectSub(function ($query) {
                         $query->select('start_at')
                             ->from('working_shift_details')
                             ->whereColumn('working_shifts.id', 'working_shift_details.working_shift_id')
                             ->where('weekday', 'mon')
                             ->limit(1);
                     }, 'mon_start')
                     ->selectSub(function ($query) {
                         $query->select('end_at')
                             ->from('working_shift_details')
                             ->whereColumn('working_shifts.id', 'working_shift_details.working_shift_id')
                             ->where('weekday', 'mon')
                             ->limit(1);
                     }, 'mon_end')
                     ->selectSub(function ($query) {
                         $query->select('start_at')
                             ->from('working_shift_details')
                             ->whereColumn('working_shifts.id', 'working_shift_details.working_shift_id')
                             ->where('weekday', 'tue')
                             ->limit(1);
                     }, 'tue_start')
                     ->selectSub(function ($query) {
                         $query->select('end_at')
                             ->from('working_shift_details')
                             ->whereColumn('working_shifts.id', 'working_shift_details.working_shift_id')
                             ->where('weekday', 'tue')
                             ->limit(1);
                     }, 'tue_end')
                     ->selectSub(function ($query) {
                         $query->select('start_at')
                             ->from('working_shift_details')
                             ->whereColumn('working_shifts.id', 'working_shift_details.working_shift_id')
                             ->where('weekday', 'wed')
                             ->limit(1);
                     }, 'wed_start')
                     ->selectSub(function ($query) {
                         $query->select('end_at')
                             ->from('working_shift_details')
                             ->whereColumn('working_shifts.id', 'working_shift_details.working_shift_id')
                             ->where('weekday', 'wed')
                             ->limit(1);
                     }, 'wed_end')
                     ->selectSub(function ($query) {
                         $query->select('start_at')
                             ->from('working_shift_details')
                             ->whereColumn('working_shifts.id', 'working_shift_details.working_shift_id')
                             ->where('weekday', 'thu')
                             ->limit(1);
                     }, 'thu_start')
                     ->selectSub(function ($query) {
                         $query->select('end_at')
                             ->from('working_shift_details')
                             ->whereColumn('working_shifts.id', 'working_shift_details.working_shift_id')
                             ->where('weekday', 'thu')
                             ->limit(1);
                     }, 'thu_end')
                     ->selectSub(function ($query) {
                         $query->select('start_at')
                             ->from('working_shift_details')
                             ->whereColumn('working_shifts.id', 'working_shift_details.working_shift_id')
                             ->where('weekday', 'fri')
                             ->limit(1);
                     }, 'fri_start')
                     ->selectSub(function ($query) {
                         $query->select('end_at')
                             ->from('working_shift_details')
                             ->whereColumn('working_shifts.id', 'working_shift_details.working_shift_id')
                             ->where('weekday', 'fri')
                             ->limit(1);
                     }, 'fri_end')
                     ->selectSub(function ($query) {
                         $query->select('start_at')
                             ->from('working_shift_details')
                             ->whereColumn('working_shifts.id', 'working_shift_details.working_shift_id')
                             ->where('weekday', 'sat')
                             ->limit(1);
                     }, 'sat_start')
                     ->selectSub(function ($query) {
                         $query->select('end_at')
                             ->from('working_shift_details')
                             ->whereColumn('working_shifts.id', 'working_shift_details.working_shift_id')
                             ->where('weekday', 'sat')
                             ->limit(1);
                     }, 'sat_end')
                     ->selectSub(function ($query) {
                         $query->select('start_at')
                             ->from('working_shift_details')
                             ->whereColumn('working_shifts.id', 'working_shift_details.working_shift_id')
                             ->where('weekday', 'sun')
                             ->limit(1);
                     }, 'sun_start')
                     ->selectSub(function ($query) {
                         $query->select('end_at')
                             ->from('working_shift_details')
                             ->whereColumn('working_shifts.id', 'working_shift_details.working_shift_id')
                             ->where('weekday', 'sun')
                             ->limit(1);
                     }, 'sun_end')
                     ->selectSub(function ($query) {
                         $query->select('name')
                             ->from(DB::raw('(SELECT id, name FROM departments) as departments'))
                             ->whereColumn('departments.id', 'department_working_shift.department_id')
                             ->limit(1);
                     }, 'department')
                     ->leftJoin('department_working_shift', 'department_working_shift.working_shift_id', '=', 'working_shifts.id')
                     ->cursor() as $row) {
            $row->sheet_name = 'work_shift';
            yield $row;
        }
    }

    // Define Working shift map function
    public function workingShiftMap($row): array {
        return [
            __t('monday') => $this->getShiftTime($row, 'mon'),
            __t('tuesday') => $this->getShiftTime($row, 'tue'),
            __t('wednesday') => $this->getShiftTime($row, 'wed'),
            __t('thursday') => $this->getShiftTime($row, 'thu'),
            __t('friday') => $this->getShiftTime($row, 'fri'),
            __t('saturday') => $this->getShiftTime($row, 'sat'),
            __t('sunday') => $this->getShiftTime($row, 'sun'),
            __t('name') => $row->name,
            __t('department') => $row->department,
            'Work_shift_start_date' => $this->getWorkingShiftStartAt($row),
            'Work_shift_end_date' => $this->getWorkingShiftEndAt($row),
        ];
    }

    // mapping helper functions
    private function getShiftTime($shift, $day): string
    {
        $start_key = $day."_start";
        $end_key = $day."_end";
        $start = $shift->$start_key;
        $end = $shift->$end_key;

        $start = $start ? Carbon::parse($start)->setTimezone($this->timezone)->format('H:i') : 'x';
        $end = $end ? Carbon::parse($end)->setTimezone($this->timezone)->format('H:i') : 'x';

        return $start == 'x' ? $start : "$start - $end";
    }
    private function getWorkingShiftStartAt($leave) {
        if(isset($leave->start_date)) {
            return $this->carbon($leave->start_date)->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
    private function getWorkingShiftEndAt($leave) {
        if(isset($leave->end_date)) {
            return $this->carbon($leave->end_date)->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
}