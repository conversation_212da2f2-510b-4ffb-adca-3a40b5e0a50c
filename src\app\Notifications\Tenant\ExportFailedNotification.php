<?php

namespace App\Notifications\Tenant;

use App\Mail\Tag\ExportTag;
use App\Notifications\BaseNotification;
use App\Notifications\Tenant\Helper\CommonParser;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;

class ExportFailedNotification extends BaseNotification implements ShouldQueue
{
    use Queueable;

    use CommonParser;


    public function __construct($templates, $via)
    {
        $this->templates = $templates;
        $this->via = $via;
        $this->auth = auth()->user();
        $this->tag =  ExportTag::class;
        parent::__construct();
    }

    public function parseNotification()
    {
        $this->databaseNotificationUrl = route('support.settings.import', ['tab' => 'Export']);

        $this->mailView = 'notification.mail.template';

        $this->mailSubject = optional($this->template()->mail())->parseSubject([

        ]);

        $this->databaseNotificationContent = optional($this->template()->database())->parse([

        ]);
    }

    public function tagParams($notifiable)
    {
        return [
            $this->model,
            $this->auth,
            $notifiable,
        ];
    }
}

