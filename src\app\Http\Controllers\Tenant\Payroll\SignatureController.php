<?php

namespace App\Http\Controllers\Tenant\Payroll;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Payroll\Signature;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;

class SignatureController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $signatures = Signature::query()
            ->when($request->get('search'), function ($query, $search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('position', 'like', "%{$search}%");
            })
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 10));

        return response()->json($signatures);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'signature_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean'
        ]);

        $signaturePath = null;
        if ($request->hasFile('signature_image')) {
            $signaturePath = $request->file('signature_image')->store('signatures', 'public');
        }

        $signature = Signature::create([
            'name' => $request->name,
            'position' => $request->position,
            'signature_path' => $signaturePath,
            'is_active' => $request->get('is_active', true),
            'created_by' => auth()->id(),
        ]);

        return response()->json([
            'message' => __t('signature_created_successfully'),
            'data' => $signature
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Signature $signature): JsonResponse
    {
        return response()->json($signature);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Signature $signature): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'signature_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean'
        ]);

        $signaturePath = $signature->signature_path;
        if ($request->hasFile('signature_image')) {
            // Delete old signature if exists
            if ($signaturePath && Storage::disk('public')->exists($signaturePath)) {
                Storage::disk('public')->delete($signaturePath);
            }
            $signaturePath = $request->file('signature_image')->store('signatures', 'public');
        }

        $signature->update([
            'name' => $request->name,
            'position' => $request->position,
            'signature_path' => $signaturePath,
            'is_active' => $request->get('is_active', $signature->is_active),
            'updated_by' => auth()->id(),
        ]);

        return response()->json([
            'message' => __t('signature_updated_successfully'),
            'data' => $signature
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Signature $signature): JsonResponse
    {
        // Delete signature file if exists
        if ($signature->signature_path && Storage::disk('public')->exists($signature->signature_path)) {
            Storage::disk('public')->delete($signature->signature_path);
        }

        $signature->delete();

        return response()->json([
            'message' => __t('signature_deleted_successfully')
        ]);
    }
}
