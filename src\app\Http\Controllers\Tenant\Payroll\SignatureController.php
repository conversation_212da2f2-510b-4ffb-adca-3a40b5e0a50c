<?php

namespace App\Http\Controllers\Tenant\Payroll;

use App\Exceptions\GeneralException;
use App\Http\Controllers\Controller;
use App\Models\Tenant\Payroll\Signature;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class SignatureController extends Controller
{
    /**
     * Display a listing of signatures
     */
    public function index(Request $request)
    {
        if (!authorize_any(['view_signature'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $query = Signature::with(['status', 'createdBy'])
            ->when($request->search, function ($q) use ($request) {
                return $q->where('name', 'like', "%{$request->search}%")
                        ->orWhere('position', 'like', "%{$request->search}%")
                        ->orWhere('department', 'like', "%{$request->search}%");
            })
            ->when($request->type, function ($q) use ($request) {
                return $q->where('type', $request->type);
            })
            ->when($request->status, function ($q) use ($request) {
                return $q->where('status_id', $request->status);
            });

        $signatures = $query->orderBy('created_at', 'desc')
                           ->paginate($request->per_page ?? 10);

        return response()->json([
            'data' => $signatures->items(),
            'total' => $signatures->total(),
            'per_page' => $signatures->perPage(),
            'current_page' => $signatures->currentPage(),
            'last_page' => $signatures->lastPage()
        ]);
    }

    /**
     * Store a newly created signature
     */
    public function store(Request $request)
    {
        if (!authorize_any(['manage_signatures'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'department' => 'nullable|string|max:255',
            'type' => 'required|in:digital,manual,stamp',
            'signature_file' => 'nullable|file|mimes:png,jpg,jpeg,svg|max:2048',
            'is_default' => 'boolean',
            'is_active' => 'boolean',
            'metadata' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $signatureData = $request->only([
                'name', 'position', 'department', 'type', 'is_default', 'is_active', 'metadata'
            ]);

            // Handle file upload for digital signatures
            if ($request->hasFile('signature_file')) {
                $file = $request->file('signature_file');
                $filename = 'signature_' . time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
                $path = $file->storeAs('signatures', $filename, 'public');
                $signatureData['signature_path'] = $path;
                $signatureData['signature_url'] = Storage::url($path);
            }

            // If this is set as default, unset other defaults
            if ($request->is_default) {
                Signature::where('is_default', true)->update(['is_default' => false]);
            }

            $signatureData['created_by'] = auth()->id();
            $signatureData['status_id'] = 1; // Active status

            $signature = Signature::create($signatureData);

            return response()->json([
                'status' => true,
                'message' => 'Signature created successfully',
                'data' => $signature->load(['status', 'createdBy'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to create signature: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified signature
     */
    public function show(Signature $signature)
    {
        if (!authorize_any(['view_signature'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $signature->load(['status', 'createdBy']);

        return response()->json([
            'status' => true,
            'data' => $signature
        ]);
    }

    /**
     * Update the specified signature
     */
    public function update(Request $request, Signature $signature)
    {
        if (!authorize_any(['manage_signatures'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'department' => 'nullable|string|max:255',
            'type' => 'required|in:digital,manual,stamp',
            'signature_file' => 'nullable|file|mimes:png,jpg,jpeg,svg|max:2048',
            'is_default' => 'boolean',
            'is_active' => 'boolean',
            'metadata' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $signatureData = $request->only([
                'name', 'position', 'department', 'type', 'is_default', 'is_active', 'metadata'
            ]);

            // Handle file upload for digital signatures
            if ($request->hasFile('signature_file')) {
                // Delete old signature file if exists
                if ($signature->signature_path && Storage::disk('public')->exists($signature->signature_path)) {
                    Storage::disk('public')->delete($signature->signature_path);
                }

                $file = $request->file('signature_file');
                $filename = 'signature_' . time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
                $path = $file->storeAs('signatures', $filename, 'public');
                $signatureData['signature_path'] = $path;
                $signatureData['signature_url'] = Storage::url($path);
            }

            // If this is set as default, unset other defaults
            if ($request->is_default && !$signature->is_default) {
                Signature::where('is_default', true)->update(['is_default' => false]);
            }

            $signature->update($signatureData);

            return response()->json([
                'status' => true,
                'message' => 'Signature updated successfully',
                'data' => $signature->load(['status', 'createdBy'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to update signature: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified signature
     */
    public function destroy(Signature $signature)
    {
        if (!authorize_any(['manage_signatures'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        try {
            // Delete signature file if exists
            if ($signature->signature_path && Storage::disk('public')->exists($signature->signature_path)) {
                Storage::disk('public')->delete($signature->signature_path);
            }

            $signature->delete();

            return response()->json([
                'status' => true,
                'message' => 'Signature deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to delete signature: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle signature status
     */
    public function toggleStatus(Signature $signature)
    {
        if (!authorize_any(['manage_signatures'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        $signature->update([
            'is_active' => !$signature->is_active
        ]);

        return response()->json([
            'status' => true,
            'message' => 'Signature status updated successfully',
            'data' => $signature
        ]);
    }

    /**
     * Set signature as default
     */
    public function setDefault(Signature $signature)
    {
        if (!authorize_any(['manage_signatures'])) {
            throw new GeneralException(trans('default.action_not_allowed'));
        }

        // Unset all other defaults
        Signature::where('is_default', true)->update(['is_default' => false]);
        
        // Set this as default
        $signature->update(['is_default' => true]);

        return response()->json([
            'status' => true,
            'message' => 'Default signature updated successfully',
            'data' => $signature
        ]);
    }

    /**
     * Get available signature types
     */
    public function getTypes()
    {
        return response()->json([
            'status' => true,
            'data' => [
                ['value' => 'digital', 'label' => 'Digital Signature'],
                ['value' => 'manual', 'label' => 'Manual Signature'],
                ['value' => 'stamp', 'label' => 'Official Stamp']
            ]
        ]);
    }

    /**
     * Get default signature
     */
    public function getDefault()
    {
        $signature = Signature::where('is_default', true)
                             ->where('is_active', true)
                             ->first();

        return response()->json([
            'status' => true,
            'data' => $signature
        ]);
    }
}
