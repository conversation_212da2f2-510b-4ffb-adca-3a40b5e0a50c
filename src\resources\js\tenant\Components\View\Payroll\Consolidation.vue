<template>
    <div class="content-wrapper">
        <app-page-top-section :title="$t('consolidation')"/>
        
        <div class="card border-0">
            <div class="card-header d-flex align-items-center justify-content-between primary-card-color">
                <h5 class="card-title mb-0">{{ $t('consolidation_management') }}</h5>
                <div class="btn-group">
                    <button 
                        type="button" 
                        class="btn btn-primary"
                        @click="openCreateModal"
                        v-if="$can('create_consolidation')"
                    >
                        <i class="fas fa-plus"></i>
                        {{ $t('add_consolidation') }}
                    </button>
                    <button 
                        type="button" 
                        class="btn btn-success"
                        @click="exportConsolidation"
                        v-if="$can('export_consolidation')"
                    >
                        <i class="fas fa-download"></i>
                        {{ $t('export') }}
                    </button>
                </div>
            </div>
            
            <div class="card-body">
                <div class="row">
                    <div class="col-12">
                        <app-table
                            :id="tableId"
                            :options="options"
                            @action="triggerActions"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- Create/Edit Modal -->
        <app-modal
            modal-id="consolidation-modal"
            modal-size="large"
            :modal-title="isEdit ? $t('edit_consolidation') : $t('add_consolidation')"
            @close-modal="closeModal"
        >
            <consolidation-form
                :selected-url="selectedUrl"
                :is-edit="isEdit"
                @close-modal="closeModal"
                @reload-table="reloadTable"
            />
        </app-modal>
    </div>
</template>

<script>
import {TENANT_BASE_URL} from "../../../Config/ApiUrl";

export default {
    name: "Consolidation",
    data() {
        return {
            tableId: 'consolidation-table',
            selectedUrl: '',
            isEdit: false,
            options: {
                name: this.$t('consolidation'),
                url: `${TENANT_BASE_URL}app/consolidations`,
                showHeader: true,
                columns: [
                    {
                        title: this.$t('id'),
                        type: 'text',
                        key: 'id',
                        isVisible: true
                    },
                    {
                        title: this.$t('report_name'),
                        type: 'text',
                        key: 'report_name',
                        isVisible: true
                    },
                    {
                        title: this.$t('period'),
                        type: 'text',
                        key: 'period',
                        isVisible: true
                    },
                    {
                        title: this.$t('total_employees'),
                        type: 'text',
                        key: 'total_employees',
                        isVisible: true
                    },
                    {
                        title: this.$t('total_amount'),
                        type: 'currency',
                        key: 'total_amount',
                        isVisible: true
                    },
                    {
                        title: this.$t('created_at'),
                        type: 'text',
                        key: 'created_at',
                        isVisible: true
                    },
                    {
                        title: this.$t('actions'),
                        type: 'action',
                        key: 'actions',
                        isVisible: true
                    }
                ],
                filters: [],
                paginationType: "pagination",
                responsive: true,
                rowLimit: 10,
                showAction: true,
                orderBy: 'desc',
                actionType: "default",
                actions: [
                    {
                        title: this.$t('view'),
                        icon: 'eye',
                        type: 'modal',
                        component: 'app-consolidation-view-modal',
                        modalId: 'consolidation-view-modal',
                        url: `${TENANT_BASE_URL}app/consolidations`,
                        name: 'view',
                        modifier: 'btn-outline-primary'
                    },
                    {
                        title: this.$t('edit'),
                        icon: 'edit',
                        type: 'modal',
                        component: 'app-consolidation-modal',
                        modalId: 'consolidation-modal',
                        url: `${TENANT_BASE_URL}app/consolidations`,
                        name: 'edit',
                        modifier: 'btn-outline-dark'
                    },
                    {
                        title: this.$t('delete'),
                        icon: 'trash-2',
                        type: 'modal',
                        component: 'app-confirmation-modal',
                        modalId: 'app-confirmation-modal',
                        url: `${TENANT_BASE_URL}app/consolidations`,
                        name: 'delete',
                        modifier: 'btn-outline-danger'
                    }
                ]
            }
        }
    },
    methods: {
        openCreateModal() {
            this.isEdit = false;
            this.selectedUrl = '';
            $('#consolidation-modal').modal('show');
        },
        
        closeModal() {
            this.isEdit = false;
            this.selectedUrl = '';
            $('#consolidation-modal').modal('hide');
        },
        
        reloadTable() {
            this.$hub.$emit('reload-' + this.tableId);
        },
        
        triggerActions(row, action, active) {
            if (action.name === 'edit') {
                this.isEdit = true;
                this.selectedUrl = `${TENANT_BASE_URL}app/consolidations/${row.id}`;
                $('#consolidation-modal').modal('show');
            } else if (action.name === 'view') {
                this.selectedUrl = `${TENANT_BASE_URL}app/consolidations/${row.id}`;
                $('#consolidation-view-modal').modal('show');
            } else if (action.name === 'delete') {
                this.deleteConsolidation(row.id);
            }
        },
        
        deleteConsolidation(id) {
            this.$confirm({
                title: this.$t('are_you_sure'),
                message: this.$t('you_will_not_be_able_to_revert_this'),
                button: {
                    no: this.$t('cancel'),
                    yes: this.$t('delete')
                },
                callback: confirm => {
                    if (confirm) {
                        this.axiosDelete(`${TENANT_BASE_URL}app/consolidations/${id}`)
                            .then(response => {
                                this.$toastr.s(response.data.message);
                                this.reloadTable();
                            })
                            .catch(error => {
                                this.$toastr.e(error.response.data.message);
                            });
                    }
                }
            });
        },
        
        exportConsolidation() {
            window.open(`${TENANT_BASE_URL}app/consolidations/export`, '_blank');
        }
    }
}
</script>

<style scoped>
.primary-card-color {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}
</style>
