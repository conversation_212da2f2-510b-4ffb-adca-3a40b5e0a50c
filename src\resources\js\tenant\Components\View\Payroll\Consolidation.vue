<template>
    <div class="content-wrapper">
        <app-page-top-section :title="$t('consolidation')">
            <app-default-button
                v-if="$can('export_consolidation')"
                btn-class="btn btn-success mr-1"
                :title="$t('export_consolidation')"
                @click="exportConsolidation()"
            />
            <app-default-button
                v-if="$can('generate_consolidation')"
                btn-class="btn btn-primary"
                :title="$t('generate_consolidation')"
                @click="generateConsolidation()"
            />
        </app-page-top-section>

        <div class="card card-with-shadow border-0" style="min-height: 400px;">
            <div class="card-header d-flex align-items-center justify-content-between primary-card-color p-primary mb-primary">
                <app-month-calendar/>
                <app-period-calendar :filters="periodFilters" :initial-filter-value="'lastMonth'"/>
            </div>

            <!-- Summary Cards -->
            <div class="row px-3 mb-3">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <h5 class="card-title">{{ $t('total_employees') }}</h5>
                            <h3 class="mb-0">{{ summaryData.totalEmployees || 0 }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <h5 class="card-title">{{ $t('total_amount') }}</h5>
                            <h3 class="mb-0">{{ summaryData.totalAmount || '0.00' }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <h5 class="card-title">{{ $t('departments') }}</h5>
                            <h3 class="mb-0">{{ summaryData.totalDepartments || 0 }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <h5 class="card-title">{{ $t('pending_approvals') }}</h5>
                            <h3 class="mb-0">{{ summaryData.pendingApprovals || 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>

            <app-table
                id="consolidation-table"
                :options="tableOptions"
                @action="triggerActions"
            />
        </div>

        <app-confirmation-modal
            v-if="confirmationModalActive"
            :loading="loading"
            :message="promptMessage"
            :modal-class="promptClass"
            :icon="promptIcon"
            modal-id="app-confirmation-modal"
            @confirmed="confirmed"
            @cancelled="confirmationModalActive = false"
            :self-close="false"
        />
    </div>
</template>

<script>
import {axiosGet, axiosPost, urlGenerator} from "../../../../common/Helper/AxiosHelper";
import {
    CONSOLIDATIONS,
    CONSOLIDATIONS_GENERATE,
    CONSOLIDATIONS_SUMMARY,
    CONSOLIDATIONS_EXPORT_PDF,
    CONSOLIDATIONS_EXPORT_EXCEL
} from "../../../Config/ApiUrl";

export default {
    name: "Consolidation",
    data() {
        return {
            urlGenerator,
            confirmationModalActive: false,
            promptClass: '',
            promptIcon: '',
            promptMessage: '',
            loading: false,
            actionType: '',
            summaryData: {
                totalEmployees: 0,
                totalAmount: '0.00',
                totalDepartments: 0,
                pendingApprovals: 0
            },
            periodFilters: [
                {
                    name: this.$t('last_month'),
                    value: 'lastMonth'
                },
                {
                    name: this.$t('this_month'),
                    value: 'thisMonth'
                },
                {
                    name: this.$t('last_quarter'),
                    value: 'lastQuarter'
                },
                {
                    name: this.$t('this_quarter'),
                    value: 'thisQuarter'
                },
                {
                    name: this.$t('last_year'),
                    value: 'lastYear'
                },
                {
                    name: this.$t('this_year'),
                    value: 'thisYear'
                }
            ],
            tableOptions: {
                name: this.$t('consolidation_report'),
                url: CONSOLIDATIONS,
                showHeader: true,
                enableRowSelect: false,
                showCount: true,
                showClearFilter: true,
                showFilter: true,
                showSearch: true,
                tablePaddingClass: "px-0",
                tableShadow: false,
                columns: [
                    {
                        title: this.$t('department'),
                        type: 'text',
                        key: 'department',
                        isVisible: true
                    },
                    {
                        title: this.$t('employee_count'),
                        type: 'text',
                        key: 'employee_count',
                        isVisible: true
                    },
                    {
                        title: this.$t('total_gross'),
                        type: 'custom-html',
                        key: 'total_gross',
                        isVisible: true,
                        modifier: (val) => {
                            return `<span class="text-success font-weight-bold">${val}</span>`;
                        }
                    },
                    {
                        title: this.$t('total_deductions'),
                        type: 'custom-html',
                        key: 'total_deductions',
                        isVisible: true,
                        modifier: (val) => {
                            return `<span class="text-danger font-weight-bold">${val}</span>`;
                        }
                    },
                    {
                        title: this.$t('net_amount'),
                        type: 'custom-html',
                        key: 'net_amount',
                        isVisible: true,
                        modifier: (val) => {
                            return `<span class="text-primary font-weight-bold">${val}</span>`;
                        }
                    },
                    {
                        title: this.$t('status'),
                        type: 'custom-html',
                        key: 'status',
                        isVisible: true,
                        modifier: (val) => {
                            return `<span class="badge badge-pill badge-${val.class}">${val.translated_name}</span>`;
                        }
                    }
                ],
                filters: [],
                paginationType: "pagination",
                responsive: true,
                rowLimit: 10,
                showAction: true,
                orderBy: 'desc',
                actionType: "dropdown",
                actions: [
                    {
                        title: this.$t('view_details'),
                        actionName: 'view_details',
                        modifier: () => this.$can('view_consolidation')
                    },
                    {
                        title: this.$t('export_department'),
                        actionName: 'export_department',
                        modifier: () => this.$can('export_consolidation')
                    }
                ]
            }
        }
    },
    created() {
        this.loadSummaryData();
    },
    methods: {
        triggerActions(row, action) {
            if (action.actionName === 'view_details') {
                this.viewDepartmentDetails(row);
            } else if (action.actionName === 'export_department') {
                this.exportDepartment(row);
            }
        },
        viewDepartmentDetails(row) {
            // Show department details modal or navigate to details
            this.$toastr.s(`Viewing details for ${row.department}`);
        },
        exportDepartment(row) {
            this.loading = true;
            axiosPost(CONSOLIDATIONS_EXPORT_PDF, {
                department: row.department,
                period: this.getCurrentPeriod()
            }, {
                responseType: 'blob'
            }).then(response => {
                const url = window.URL.createObjectURL(new Blob([response.data]));
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', `${row.department}-consolidation.pdf`);
                document.body.appendChild(link);
                link.click();
                link.remove();
                this.$toastr.s('Department report exported successfully');
            }).catch(() => {
                this.$toastr.e('Failed to export department report');
            }).finally(() => {
                this.loading = false;
            });
        },
        generateConsolidation() {
            this.actionType = 'generate';
            this.confirmationModalActive = true;
            this.promptIcon = 'file-plus';
            this.promptClass = 'primary';
            this.promptMessage = this.$t('are_you_sure_to_generate_consolidation');
        },
        exportConsolidation() {
            this.actionType = 'export';
            this.confirmationModalActive = true;
            this.promptIcon = 'download';
            this.promptClass = 'success';
            this.promptMessage = this.$t('are_you_sure_to_export_consolidation');
        },
        loadSummaryData() {
            axiosGet(CONSOLIDATIONS_SUMMARY).then(response => {
                if (response.data.status) {
                    this.summaryData = response.data.data;
                }
            }).catch(() => {
                // Fallback data
                this.summaryData = {
                    totalEmployees: 0,
                    totalAmount: '₱0.00',
                    totalDepartments: 0,
                    pendingApprovals: 0
                };
            });
        },
        confirmed() {
            if (this.actionType === 'generate') {
                this.performGenerate();
            } else if (this.actionType === 'export') {
                this.performExport();
            }
        },
        performGenerate() {
            this.loading = true;
            const payload = {
                period_start: this.getPeriodStart(),
                period_end: this.getPeriodEnd(),
                report_type: 'departmental'
            };

            axiosPost(CONSOLIDATIONS_GENERATE, payload).then(response => {
                if (response.data.status) {
                    this.$toastr.s(response.data.message);
                    this.$hub.$emit('reload-consolidation-table');
                    this.loadSummaryData();
                } else {
                    this.$toastr.e(response.data.message);
                }
            }).catch(() => {
                this.$toastr.e('Failed to generate consolidation report');
            }).finally(() => {
                this.loading = false;
                this.confirmationModalActive = false;
            });
        },
        performExport() {
            this.loading = true;
            axiosPost(CONSOLIDATIONS_EXPORT_EXCEL, {
                period: this.getCurrentPeriod()
            }, {
                responseType: 'blob'
            }).then(response => {
                const url = window.URL.createObjectURL(new Blob([response.data]));
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', `consolidation-${new Date().toISOString().split('T')[0]}.xlsx`);
                document.body.appendChild(link);
                link.click();
                link.remove();
                this.$toastr.s('Consolidation exported successfully');
            }).catch(() => {
                this.$toastr.e('Failed to export consolidation');
            }).finally(() => {
                this.loading = false;
                this.confirmationModalActive = false;
            });
        },
        getCurrentPeriod() {
            return 'thisMonth'; // This should be dynamic based on filter selection
        },
        getPeriodStart() {
            const now = new Date();
            const year = now.getFullYear();
            const month = now.getMonth();
            return new Date(year, month, 1).toISOString().split('T')[0];
        },
        getPeriodEnd() {
            const now = new Date();
            const year = now.getFullYear();
            const month = now.getMonth();
            return new Date(year, month + 1, 0).toISOString().split('T')[0];
        }
    }
}
</script>

<style scoped>
</style>
