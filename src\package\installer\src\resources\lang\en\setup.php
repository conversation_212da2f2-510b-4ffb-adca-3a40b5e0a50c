<?php
return [
    //Set up
    'install' => 'Install',
    'password_requirements' => 'Password requirements.',
    'password_requirements_message' => 'The password should contain one upper case, one lower case, numbers, one special character ( +=#?!@$%^&*- ). It should be a minimum of 8 characters.',
    'database_configuration' => 'Database configuration',
    'db_connection' => 'Database connection',
    'database_hostname' => 'Database host',
    'database_port' => 'Database port',
    'database_name' => 'Database name',
    'database_username' => 'Database username',
    'database_password' => 'Database password',
    'admin_login_details' => 'Admin Login Details',
    'purchase_code' => 'Purchase code',
    'code' => 'Code',
    'mysql' => 'MySQL '.config('installer.core.min_mysql_version').'+',
    'pgsql' => 'PostgreSQL 9.4+',
    'sqlsrv' => 'SQL Server 2017+',
    'invalid_purchase_code' => 'Invalid purchase code',
    'app_installed_successfully' => 'App installed successfully',

    /*Installer addition lang*/
    'database_configuration_requirements' => 'Database configuration requirements',
    'should_not_contain' => 'Database name, Database username, Database password should not contain any',
    'make_sure_your_database_credentials_are_correct' => 'Make sure your database credentials are correct.',
    'avoid_the_uses_of_white_space' => 'Format: Database name, Database username, Database password should not contain white space (" ") and "#".',
    'server_could_not_process_request' => 'Server could not process request',
    'it_seems_due_to_some_network_issue' => 'Sorry for the inconvenience. It seems due to some network issue request could not be sent.',
    'it_seems_due_to_purchase_code' => 'Sorry for the inconvenience. It seems your purchase code is invalid.',
    'please_check_the' => 'Please check the',
    'error_log' => 'Error log',
    'request_failed' => 'Request failed',
    'please_contact_support' => '<NAME_EMAIL>.',
    'back' => 'Back',
    'error_log_copied_successfully' => 'Error log copied successfully',
    'please_copy_the_error_log_below_and_contact_support' => 'Please copy the error log and <NAME_EMAIL>',
    'full_name' => 'Full name',
    'purchase_code_verified' => 'Purchase code verified',
    'database_credentials_should_not' => ':name should not contain',
    'it_seems_due_to' => 'Sorry for the inconvenience. It seems',
    'admin_info_saved_successfully' => 'Admin info saved successfully',
    'email_setup_saved_successfully' => 'Email setup saved successfully',
    'skip' => 'Skip',
    'do_not_have_permission' => 'Do not have permission',
    'storage_sym_link_permission' => 'Storage symlink permission',
    'additional_requirements' => 'Additional requirements',
    'symlink_working' => 'Symlink permission enabled',
    'database_configured_successfully' => 'Database configured successfully',
    'save_&_install' => 'Save & install',
    'symlink_permission' => 'Symlink permission',
    'symlink_error_connection' => 'Unable to create symbolic link between target_link and storage_link',
    'requirement_checked_successfully' => 'Requirement checked successfully',

    // Broadcast
    'broadcasting_configuration' => 'Broadcasting Configuration',
    'broadcast_driver' => 'Broadcast Driver',
    'pusher_app_id' => 'Pusher App Id',
    'pusher_app_key' => 'Pusher App Key',
    'pusher_app_secret' => 'Pusher App Secret',
    'pusher_app_cluster' => 'Pusher App Cluster',
    'pusher' => 'Pusher',
    'ajax' => 'Ajax',
    'broadcast_setup' => 'Broadcast setup',
    'broadcast_settings' => 'Broadcast setting',
    'email' => 'Email',
    'password' => 'Password',
    'save_&_next' => 'Save & next',
    'database_credential_error' => 'Incorrect database credentials',
    'environment_setup_successfully' => 'Environment setup successfully',

    // Email setting
    'email_sent_from_name' => 'Email sent from name',
    'type_email_sent_from_name' => 'Type email sent from name',
    'email_sent_from_email' => 'Email sent from email',
    'type_email_sent_from_email' => 'Type email sent from email',
    'email_use_for' => 'Email use for',
    'type_email_use_for' => 'Type email use for',
    'supported_mail_services' => 'Supported mail services',
    'hostname' => 'Host name',
    'type_host_name' => 'Type host name',
    'access_key_id' => 'Access key id',
    'type_access_key_id' => 'Type access key id',
    'secret_access_key' => 'Secret access key',
    'type_secret_access_key' => 'Type secret access key',
    'domain_name' => 'Domain name',
    'type_domain_name' => 'Type domain name',
    'api_key' => 'Api key',
    'type_api_key' => 'Type api key',
    'smtp_host' => 'SMTP host',
    'type_smtp_host' => 'Type SMTP host',
    'port' => 'Port',
    'type_port_number' => 'Type port number',
    'password_to_access' => 'Password to access',
    'type_password_to_access' => 'Type password to access',
    'encryption_type' => 'Encryption type',
    'type_encryption_type' => 'Type encryption type',
    // mail driver
    'mandrill' => 'Mandrill',
    'amazon_ses' => 'Amazon SES',
    'smtp' => 'SMTP',
    'mailgun' => 'Mailgun',
    'sparkpost' => 'SparkPost',
    'sendmail' => 'Sendmail',
    'tls' => 'TLS',
    'ssh' => 'SSH',
    'mandrill_key' => 'Mandrill key',
    'sparkpost_key' => 'SparkPost key',
    'choose_one' => 'Choose one',
    'user_name' => 'User name',
    'nothing_store' => 'Nothing has been added.',
    'region' => 'Region',

    //Update
    'please_wait_it_might_take_time' => 'Please wait! It might take sometime.',
    'step_one' => 'Step - 1',
    'downloading' => 'Downloading...',
    'step_two' => 'Step - 2',
    'updating' => 'Updating...',
    'congratulations' => 'Congratulations',
    'your_app_has_been_updated_successfully' => 'Your app has been updated successfully.',
    'ok' => 'Ok',
    'symlink_error' => 'Symlink permission has been disabled from your Hosted server. Please contact your hosting provider to enable it.',
    'symlink_warning_1' => 'If symlink() is disabled then you will not be able to change any images on the application (Profile picture, icon etc..).',
    'symlink_warning_2' => 'Alternatively, if the service provider denied enabling symlink() then you can use cron job on your hosted server.',
    'symlink_warning_3' => 'Create a cron job using the command given below on your hosted server.',
    'no_new_update_found' => 'No new update found.',
    'no_roles_found' => 'No roles found',
    'job_application_setting' => 'Job application setting',
    'failed_to_create_job' => 'Sorry, could not create this job post.',
    'update_warning' => 'Update warning',
    'update_warning_details' => 'i. Before starting the installation of updates, please increase the max_execution_time (Recommendation 2000 sec) and upload_max_filesize (Recommendation 128MB) on your hosted server.
                    <br> ii. Backup all files and database before starting the installation of updates (including language files if you are using custom_lang.php file to overwrite translation text) and review the changelog.
                    <br> iii. You must install the previous versions to update the new version.
                    <br> iv. Download the update file zip from Step 1 and upload the zip in Step 2 (Don\'t rename the zip, otherwise this update will be failed.)',
    'change_logs' => 'Change logs',
    'this_will_update_entire_app' => 'This action will update the app to the selected version.',
    'action_text_with_home_link' => 'Your application updated successfully, Need to go home page!',
    'please_install_version_first' => 'Please install previous version first.',
    'upload_update_file' => 'Upload downloaded file',
    'download' => 'Download Update File',
    'version' => 'Version',
    'no_update_found' => 'No update found.',
    'no_updates_found' => 'No updates found.',
    'you_are_using_version' => 'You are using :version.',
];