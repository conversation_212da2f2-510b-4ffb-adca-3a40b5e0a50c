<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Core\Auth\Permission;
use App\Models\Core\Auth\Role;

class AssignPayrollPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = Permission::whereIn('name', [
            'view_transmittal',
            'generate_transmittal', 
            'export_transmittal',
            'delete_transmittal',
            'view_signature',
            'manage_signatures',
            'view_consolidation',
            'generate_consolidation',
            'export_consolidation'
        ])->pluck('id');

        if ($permissions->isEmpty()) {
            $this->command->error('No payroll permissions found. Please run PayrollPermissionsSeeder first.');
            return;
        }

        // Assign to admin roles
        $adminRoles = Role::where('is_admin', 1)->get();
        
        foreach ($adminRoles as $role) {
            $role->permissions()->syncWithoutDetaching($permissions);
            $this->command->info("Assigned payroll permissions to role: {$role->name}");
        }

        // Also assign to Manager role if it exists
        $managerRole = Role::where('name', 'Manager')->first();
        if ($managerRole) {
            $managerRole->permissions()->syncWithoutDetaching($permissions);
            $this->command->info("Assigned payroll permissions to Manager role");
        }

        $this->command->info('Payroll permissions assigned successfully!');
    }
}
