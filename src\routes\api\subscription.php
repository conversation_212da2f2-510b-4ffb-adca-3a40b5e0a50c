<?php
/**
 * API Routes for Subscription Management
 *
 * This file contains all the API routes related to subscription management.
 * These routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group.
 *
 * PHP version 8.1
 *
 * @category Routes
 * @package  App\Routes\Api
 * <AUTHOR> Team <<EMAIL>>
 * @license  MIT https://opensource.org/licenses/MIT
 * @version  GIT: <git_id>
 * @link     https://example.com
 */

use App\Http\Controllers\Tenant\SubscriptionController;
use Illuminate\Support\Facades\Route;

Route::group(['middleware' => 'auth:sanctum'], function () {

    Route::apiResource('subscriptions', SubscriptionController::class)->except(['create', 'edit']);
    
    // Get active subscription for current user
    Route::get('active-subscription', [SubscriptionController::class, 'activeSubscription']);
    Route::get('plans', [SubscriptionController::class, 'plan']);
    Route::post('upgrade-plan', [SubscriptionController::class, 'upgradePlan']);
    Route::post('renew-plan', [SubscriptionController::class, 'renew']);
    
    

    Route::post('select-plan', [SubscriptionController::class, 'selectPlan'])
    ->name('api.subscription.select-plan');

    // Check employee limit
    Route::get('check-employee-limit', [SubscriptionController::class, 'checkEmployeeLimit']);
            
    // Cancel a subscription
    Route::post('subscriptions/{subscription}/cancel', [SubscriptionController::class, 'cancel']);
    
    // Renew a subscription
    Route::post('subscriptions/{subscription}/renew', [SubscriptionController::class, 'renew']);
});

