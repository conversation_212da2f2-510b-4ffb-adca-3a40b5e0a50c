<template>
  <div>
    <!-- Expired Alert -->
    <div v-if="daysLeft !== null && daysLeft < 0" class="alert alert-danger d-flex justify-content-between">
      <div class="d-flex align-items-center">
        <i class="fas fa-times-circle text-danger mr-2"></i>
        <div>
          <strong>Urgent:</strong> Your subscription expired
          <strong>{{ Math.abs(daysLeft) }} day<span v-if="Math.abs(daysLeft) > 1">s</span></strong> ago.
          Renew now to restore access.
        </div>
      </div>

      <div>
        <button v-if="inSubscription" class="btn btn-danger" @click="openRenewPlanModal">
          Renew Plan
        </button>
        <button v-else class="btn btn-text-danger" @click="navigateToSubscription">
          <p class="mb-0 text-danger">
            Renew now <i class="fas fa-arrow-right ml-2"></i>
          </p>
        </button>
      </div>
    </div>

    <!-- Warning Alert 0–7 days -->
    <div v-else-if="daysLeft !== null && daysLeft >= 0 && daysLeft <= 7">
      <div v-if="daysLeft === 0" class="alert alert-danger d-flex justify-content-between">
        <div class="d-flex align-items-center">
          <i class="fas fa-exclamation-triangle text-danger mr-2"></i>
          <div>
            <strong>Urgent:</strong> Your subscription expires <strong>today</strong>.
            Please renew immediately.
          </div>
        </div>

        <div>
          <button v-if="inSubscription" class="btn btn-danger" @click="openRenewPlanModal">
            Renew Plan
          </button>
          <button v-else class="btn btn-text-danger" @click="navigateToSubscription">
            <p class="mb-0 text-danger">
              Renew now <i class="fas fa-arrow-right ml-2"></i>
            </p>
          </button>
        </div>
      </div>

      <div v-else class="alert alert-info d-flex justify-content-between">
        <div class="d-flex align-items-center">
          <i class="fas fa-info-circle text-info mr-2"></i>
          <div>
            <strong>Reminder:</strong> Your subscription will end in
            <strong>{{ daysLeft }} day<span v-if="daysLeft > 1">s</span></strong>.
            Please consider renewing your plan.
          </div>
        </div>

        <div>
          <button v-if="inSubscription" class="btn btn-primary" @click="openRenewPlanModal">
            Renew Plan
          </button>
          <button v-else class="btn btn-text-primary" @click="navigateToSubscription">
            <p class="mb-0 text-primary">
              Renew my subscription <i class="fas fa-arrow-right ml-2"></i>
            </p>
          </button>
        </div>
      </div>
    </div>

    <!-- Always mounted modal -->
    <RenewPlanModal ref="renewPlanModal" :current-plan-id="currentPlanId" @plan-renewed="handleRenewed" />
  </div>
</template>

<script>
import RenewPlanModal from '../../../tenant/Components/View/Setting/Component/RenewPlanModal.vue';

export default {
  name: "RenewAlert",
  components: { RenewPlanModal },
  props: {
    daysLeft: { type: Number, required: true },
    inSubscription: { type: Boolean, default: false },
    currentPlanId: { type: [Number, String], default: null }
  },
  methods: {
    navigateToSubscription() {
      window.location.href = '/hris/app/settings?tab=subscription';
    },
    openRenewPlanModal() {
      if (this.$refs.renewPlanModal) {
        this.$refs.renewPlanModal.show();
      } else {
        console.warn('RenewPlanModal is not loaded yet.');
      }
    },
    handleRenewed(plan) {
      this.$toastr.s(`Successfully renewed: ${plan.plan_name}`);
    }
  }
};
</script>
