<?php

namespace App\Exports;

use App\Helpers\Traits\DateTimeHelper;
use App\Models\Tenant\Export\ModuleExport;
use App\Models\Tenant\Leave\Leave;
use App\Notifications\Tenant\ExportFailedNotification;
use App\Repositories\Core\Status\StatusRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;

class LeaveModuleExport implements
    FromQuery,
    WithMapping,
    ShouldQueue,
//    WithCustomChunkSize,
    WithEvents,
    WithHeadings,
    WithTitle,
    ShouldAutoSize
{
    use Exportable, RegistersEventListeners, DateTimeHelper;

    protected int|null $module_export_id;
    public function __construct($module_export_id = null)
    {
        $this->module_export_id = $module_export_id;
    }

//    public function chunkSize(): int {
//        return 1000;
//    }

    public static function afterSheet(AfterSheet $event)
    {
        // Example event listener, you can define additional events if needed
        // update completed_modules leave
        if($event->getConcernable()?->module_export_id) {
            $module_export = ModuleExport::query()->find($event->getConcernable()->module_export_id);
            if($module_export) {
                $completed_modules = array_values(array_unique(array_merge($module_export->completed_modules, ['leave'])));
                $module_export->update([
                    'completed_modules' => $completed_modules,
                ]);
            }
        }
         Log::info('Leave: After sheet');
    }
    public function failed(\Throwable $exception): void
    {
        // handle failed export
        // update status rejected
        if($this->module_export_id) {
            $module_export = ModuleExport::query()->find($this->module_export_id);
            if($module_export) {
                $module_export->update([
                    'status_id' => resolve(StatusRepository::class)->exportRejected(),
                    'data' => [
                        'message' => 'Leave export failed.'
                    ],
                ]);
                // notify user failed
                notify()
                    ->on('export_failed')
                    ->mergeAudiences($module_export->created_by)
                    ->send(ExportFailedNotification::class);
            }
        }
        // notify user failed
        Log::info('leave: failed');
    }

    public function query()
    {
        return Leave::query()
            ->with([
                'status:id,name,class',
                'type:id,name',
                'user:id,email',
                'comments' => fn(MorphMany $many) => $many->orderBy('parent_id', 'DESC')
                    ->select('id', 'commentable_type', 'commentable_id', 'user_id', 'type', 'comment', 'parent_id'),
            ])
            ->latest('date');
    }
    public function map($leave): array
    {
        return [
            $this->getStartAt($leave),
            $this->getEndAt($leave),
            $this->getStatus($leave),
            $this->getNote($leave),
            $this->getUserEmail($leave),
        ];
    }

    public function headings(): array
    {
        return [
            'Start_date',
            'End_date',
            'Status',
            'description',
            'User',
        ];
    }

    public function title(): string
    {
        return __t('leaves');
    }

    // mapping functions
    private function getStartAt($leave) {
        if(isset($leave->start_at)) {
            return $this->carbon($leave->start_at)->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
    private function getEndAt($leave) {
        if(isset($leave->end_at)) {
            return $this->carbon($leave->end_at)->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
    private function getStatus($leave): string {
        if(isset($leave->status) && isset($leave->status->translated_name)) {
            return $leave->status->translated_name;
        }
        return '';
    }
    private function getNote($leave, $type = 'reason-note')
    {
        if(!isset($leave->comments)) return '';
        if (!$leave->comments->count()) return '';

        return optional($leave->comments->where('type', $type)->sortByDesc('parent_id')->first())->comment;
    }
    private function getUserEmail($leave): string {
        if(isset($leave->user) && isset($leave->user->email)) {
            return $leave->user->email;
        }
        return '';
    }
}
