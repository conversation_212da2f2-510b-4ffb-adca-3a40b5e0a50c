<?php

namespace App\Jobs\Tenant\FastExportTraits;

use App\Helpers\Traits\DateTimeHelper;
use App\Models\Tenant\Employee\DepartmentUser;

trait DepartmentHistoryTrait
{
    use DateTimeHelper;
    // Define DepartmentHistory generator function
    public function DepartmentHistoryDataGenerator(): \Generator
    {
        foreach (
            DepartmentUser::query()
                ->select(['start_date', 'end_date'])
                ->selectSub(function ($query) {
                    $query->select('name')
                        ->from('departments')
                        ->whereColumn('departments.id', 'department_user.department_id')
                        ->limit(1);
                }, 'department')
                ->selectSub(function ($query) {
                    $query->select('email')
                        ->from('users')
                        ->whereColumn('users.id', 'department_user.user_id')
                        ->limit(1);
                }, 'user')
                ->cursor() as $row) {
            $row->sheet_name = 'department_history';
            yield $row;
        }
    }

    // Define DepartmentHistory map function
    public function departmentHistoryMap($row): array {
        return [
            'User' => $row->user,
            'Department' => $row->department,
            'Start_date' => $this->getDepartmentHistoryStartDate($row),
            'End_date' => $this->getDepartmentHistoryEndDate($row),
        ];
    }

    // mapping helper functions
    private function getDepartmentHistoryStartDate($row) {
        if(isset($row->start_date)) {
            return $this->carbon($row->start_date)->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
    private function getDepartmentHistoryEndDate($row) {
        if(isset($row->end_date)) {
            return $this->carbon($row->end_date)->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
}