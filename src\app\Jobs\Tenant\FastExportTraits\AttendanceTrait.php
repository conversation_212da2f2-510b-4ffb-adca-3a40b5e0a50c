<?php

namespace App\Jobs\Tenant\FastExportTraits;

use App\Helpers\Traits\DateTimeHelper;
use App\Models\Tenant\Attendance\AttendanceDetails;
use Illuminate\Support\Facades\DB;

trait AttendanceTrait
{
    use DateTimeHelper;
    // Define Attendance generator function
    public function AttendanceDataGenerator(): \Generator
    {
        foreach (AttendanceDetails::query()
                     ->select([
                         'in_time',
                         'out_time',
                         'attendance_details.status_id',
                         'review_by',
                         'added_by'
                     ])
                     ->selectSub(function ($query) {
                         $query->select('name')
                             ->from('statuses')
                             ->whereColumn('statuses.id', 'attendance_details.status_id')
                             ->limit(1);
                     }, 'status')
                     ->selectSub(function ($query) {
                         $query->select('comment')
                             ->from('comments')
                             ->whereColumn('comments.commentable_id', 'attendance_details.id')
                             ->where('comments.commentable_type', AttendanceDetails::class)
                             ->where('comments.type', 'manual')
//                        ->orderBy('parent_id', 'DESC')
                             ->limit(1);
                     }, 'manual_note')
                     ->selectSub(function ($query) {
                         $query->select('comment')
                             ->from('comments')
                             ->whereColumn('comments.commentable_id', 'attendance_details.id')
                             ->where('comments.commentable_type', AttendanceDetails::class)
                             ->where('comments.type', 'in-note')
//                        ->orderBy('parent_id', 'DESC')
                             ->limit(1);
                     }, 'in_note')
                     ->selectSub(function ($query) {
                         $query->select('comment')
                             ->from('comments')
                             ->whereColumn('comments.commentable_id', 'attendance_details.id')
                             ->where('comments.commentable_type', AttendanceDetails::class)
                             ->where('comments.type', 'out-note')
//                        ->orderBy('parent_id', 'DESC')
                             ->limit(1);
                     }, 'out_note')
                     ->selectSub(function ($query) {
                         $query->select('comment')
                             ->from('comments')
                             ->whereColumn('comments.commentable_id', 'attendance_details.id')
                             ->where('comments.commentable_type', AttendanceDetails::class)
                             ->where('comments.type', 'request')
//                        ->orderBy('parent_id', 'DESC')
                             ->limit(1);
                     }, 'request_note')
                     ->selectSub(function ($query) {
                         $query->select('email')
                             ->from(DB::raw('(SELECT id, email FROM users) as users'))
                             ->whereColumn('users.id', 'attendances.user_id')
//                             ->whereNull('users.deleted_at')
                             ->limit(1);
                     }, 'user')
                     ->join('attendances', 'attendance_details.attendance_id', '=', 'attendances.id')
                     ->cursor() as $attendance) {
            $attendance->sheet_name = 'attendance';
            yield $attendance;
        }
    }

    // Define Working shift map function
    public function attendanceMap($row): array {
        return [
            'Start_date' => $this->getAttendanceIntime($row),
            'End_date' => $this->getAttendanceOuttime($row),
            'Status' => __t($row->status),
            'description' => $this->getAttendanceComment($row),
            'User' => $row->user,
        ];
    }

    // mapping helper functions
    private function getAttendanceIntime($attendanceDetail) {
        if(isset($attendanceDetail->in_time)) {
            return $this->carbon($attendanceDetail->in_time)->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
    private function getAttendanceOuttime($attendanceDetail) {
        if(isset($attendanceDetail->out_time)) {
            return $this->carbon($attendanceDetail->out_time)->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
    private function getAttendanceComment(AttendanceDetails $attendanceDetail)
    {
        if ($attendanceDetail->review_by || $attendanceDetail->added_by) {
            return $attendanceDetail->manual_note ? "Reason Note:'$attendanceDetail->manual_note'" : '';
        }

        $in_note = $attendanceDetail->in_note;
        $out_note = $attendanceDetail->out_note;

        if ($in_note && $out_note) {
            return "PUNCH-IN:'$in_note' || PUNCH-OUT:'$out_note'";
        }

        if ($in_note) {
            return "PUNCH-IN:'$in_note'";
        }
        if ($out_note) {
            return "PUNCH-OUT:'$out_note'";
        }

        $note = $attendanceDetail->request_note;
        return $note ? "Reason Note:'$note'" : '';
    }
}