<?php

namespace App\Jobs\Tenant\FastExportTraits;

use App\Helpers\Traits\DateTimeHelper;
use App\Models\Tenant\Salary\Salary;

trait SalaryHistoryTrait
{
    use DateTimeHelper;

    // Define Leave generator function
    public function SalaryDataGenerator(): \Generator
    {
        foreach (
            Salary::query()
                ->select(['start_at', 'end_at', 'amount'])
                ->selectSub(function ($query) {
                    $query->select('email')
                        ->from('users')
                        ->whereColumn('users.id', 'salaries.user_id')
                        ->limit(1);
                }, 'user')
                ->selectSub(function ($query) {
                    $query->select('joining_date')
                        ->from('profiles')
                        ->whereColumn('profiles.user_id', 'salaries.user_id')
                        ->limit(1);
                }, 'joining_date')
                ->cursor() as $salary) {
            $salary->sheet_name = 'salary_history';
            yield $salary;
        }
    }


    // Define Leave map function
    public function salaryHistoryMap($row): array
    {
        return [
            'Start_date' => $this->getSalaryStartAt($row),
            'End_date' => $this->getSalaryEndAt($row),
            'Salary_Amount' => $row->amount,
            'User' => $row->user,
        ];
    }

    // mapping helper functions
    private function getSalaryStartAt($salary)
    {
        if (isset($salary->start_at)) {
            return $this->carbon($salary->start_at . ' 00:00:00')->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        if (isset($salary->joining_date)) {
            return $this->carbon($salary->joining_date . ' 00:00:00')->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }

    private function getSalaryEndAt($salary)
    {
        if (isset($salary->end_at)) {
            return $this->carbon($salary->end_at . ' 00:00:00')->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
}