<?php

namespace App\Models\Tenant\Payroll;

use App\Models\Core\BaseModel;
use App\Models\Core\Auth\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ConsolidationReport extends BaseModel
{
    protected $fillable = [
        'report_name',
        'period',
        'total_employees',
        'total_amount',
        'data',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'total_employees' => 'integer',
        'total_amount' => 'decimal:2',
        'data' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Get the user who created this consolidation report.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this consolidation report.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope to filter by report name or period.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('report_name', 'like', "%{$search}%")
              ->orWhere('period', 'like', "%{$search}%");
        });
    }

    /**
     * Scope to filter by period.
     */
    public function scopeByPeriod($query, $period)
    {
        return $query->where('period', $period);
    }

    /**
     * Get formatted total amount.
     */
    public function getFormattedTotalAmountAttribute()
    {
        return number_format($this->total_amount, 2);
    }

    /**
     * Get formatted created date.
     */
    public function getFormattedCreatedAtAttribute()
    {
        return $this->created_at->format('M d, Y h:i A');
    }

    /**
     * Get formatted updated date.
     */
    public function getFormattedUpdatedAtAttribute()
    {
        return $this->updated_at->format('M d, Y h:i A');
    }

    /**
     * Get average amount per employee.
     */
    public function getAverageAmountPerEmployeeAttribute()
    {
        if ($this->total_employees > 0) {
            return $this->total_amount / $this->total_employees;
        }
        return 0;
    }

    /**
     * Get formatted average amount per employee.
     */
    public function getFormattedAverageAmountPerEmployeeAttribute()
    {
        return number_format($this->average_amount_per_employee, 2);
    }
}
