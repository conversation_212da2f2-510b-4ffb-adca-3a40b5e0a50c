<?php

namespace App\Models\Tenant\Payroll;

use App\Models\Core\Auth\User;
use App\Models\Core\Status;
use App\Models\Core\Traits\StatusRelationship;
use App\Models\Tenant\TenantModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class ConsolidationReport extends TenantModel
{
    use HasFactory, StatusRelationship;

    protected $fillable = [
        'report_type',
        'period_start',
        'period_end',
        'department_ids',
        'total_employees',
        'total_amount',
        'total_departments',
        'report_data',
        'generated_by',
        'status_id',
        'file_path',
        'file_url',
        'notes',
        'approved_at',
        'approved_by'
    ];

    protected $casts = [
        'period_start' => 'date',
        'period_end' => 'date',
        'department_ids' => 'array',
        'total_amount' => 'decimal:2',
        'report_data' => 'array',
        'approved_at' => 'datetime'
    ];

    protected $appends = [
        'report_type_label',
        'formatted_amount',
        'period_display',
        'status_badge',
        'full_file_url'
    ];

    /**
     * Get the user who generated the report
     */
    public function generatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'generated_by');
    }

    /**
     * Get the user who approved the report
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the status of the report
     */
    public function status(): BelongsTo
    {
        return $this->belongsTo(Status::class);
    }

    /**
     * Get report type label attribute
     */
    public function getReportTypeLabelAttribute(): string
    {
        return match($this->report_type) {
            'summary' => 'Summary Report',
            'detailed' => 'Detailed Report',
            'departmental' => 'Departmental Report',
            default => 'Unknown Report'
        };
    }

    /**
     * Get formatted amount attribute
     */
    public function getFormattedAmountAttribute(): string
    {
        return '₱' . number_format($this->total_amount, 2);
    }

    /**
     * Get period display attribute
     */
    public function getPeriodDisplayAttribute(): string
    {
        return $this->period_start->format('M d, Y') . ' - ' . $this->period_end->format('M d, Y');
    }

    /**
     * Get status badge attribute
     */
    public function getStatusBadgeAttribute(): array
    {
        $status = $this->status;
        return [
            'name' => $status->name ?? 'unknown',
            'class' => $status->class ?? 'secondary',
            'translated_name' => $status->translated_name ?? 'Unknown'
        ];
    }

    /**
     * Get full file URL attribute
     */
    public function getFullFileUrlAttribute(): ?string
    {
        if (!$this->file_path) {
            return null;
        }

        if (filter_var($this->file_url, FILTER_VALIDATE_URL)) {
            return $this->file_url;
        }

        return Storage::url($this->file_path);
    }

    /**
     * Scope for filtering by report type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('report_type', $type);
    }

    /**
     * Scope for filtering by period
     */
    public function scopeForPeriod($query, $startDate, $endDate)
    {
        return $query->whereBetween('period_start', [$startDate, $endDate])
                    ->orWhereBetween('period_end', [$startDate, $endDate]);
    }

    /**
     * Scope for approved reports
     */
    public function scopeApproved($query)
    {
        return $query->whereNotNull('approved_at');
    }

    /**
     * Scope for pending reports
     */
    public function scopePending($query)
    {
        return $query->whereNull('approved_at');
    }

    /**
     * Scope for reports by generator
     */
    public function scopeGeneratedBy($query, $userId)
    {
        return $query->where('generated_by', $userId);
    }

    /**
     * Check if report has file
     */
    public function hasFile(): bool
    {
        return !empty($this->file_path) && Storage::disk('public')->exists($this->file_path);
    }

    /**
     * Delete report file
     */
    public function deleteFile(): bool
    {
        if ($this->hasFile()) {
            return Storage::disk('public')->delete($this->file_path);
        }
        return true;
    }

    /**
     * Generate report reference number
     */
    public function generateReferenceNumber(): string
    {
        $prefix = match($this->report_type) {
            'summary' => 'CSR',
            'detailed' => 'CDR',
            'departmental' => 'CDT',
            default => 'CRP'
        };
        
        $year = $this->created_at->format('Y');
        $month = $this->created_at->format('m');
        $sequence = str_pad($this->id, 4, '0', STR_PAD_LEFT);
        
        return "{$prefix}-{$year}{$month}-{$sequence}";
    }

    /**
     * Get summary statistics from report data
     */
    public function getSummaryStats(): array
    {
        $data = $this->report_data;
        
        return [
            'total_employees' => $this->total_employees,
            'total_amount' => $this->total_amount,
            'total_departments' => $this->total_departments,
            'average_per_employee' => $this->total_employees > 0 ? $this->total_amount / $this->total_employees : 0,
            'period' => $this->period_display
        ];
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($report) {
            $report->deleteFile();
        });
    }
}
