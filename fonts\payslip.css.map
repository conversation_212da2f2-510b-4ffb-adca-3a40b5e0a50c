{"version": 3, "file": "css/payslip.css", "mappings": "AAyHE,gBC6VF;ACtdA;;;;;EAAA,CCAA,MAGI,8MAIA,yIAIA,2GAKF,oMACA,sGFmBF,CGlBA,iBAGE,qBHqBF,CGlBA,KAGE,8BACA,0CAHA,uBACA,gBHuBF,CGfA,sEACE,aHkBF,CGRA,KAQE,qBC9CS,CD4CT,aCnCS,CD+BT,wKCiO4B,CCjJxB,cAtCa,CFxCjB,eC0O4B,CDzO5B,eC8O4B,CDlP5B,SAMA,eHYF,CGHA,sBACE,mBHMF,CGGA,GACE,mBACA,SACA,gBHAF,CGaA,kBAEE,mBCgN4B,CDjN5B,YHTF,CGiBA,EAEE,kBCoF0B,CDrF1B,YHbF,CGyBA,sCAKE,gBADA,YAFA,0BACA,0EAGA,mEHtBF,CGyBA,QAEE,kBACA,mBHtBF,CGyBA,iBALE,kBHbF,CGkBA,SAGE,YHrBF,CGyBA,wBAIE,eHtBF,CGyBA,GACE,eHtBF,CGyBA,GACE,oBACA,aHtBF,CGyBA,WACE,eHtBF,CGyBA,SAEE,kBHtBF,CGyBA,MEpFI,aL+DJ,CG8BA,QE7FI,cFiGF,cAFA,kBAGA,sBH3BF,CG8BA,IAAM,aH1BN,CG2BA,IAAM,SHvBN,CG8BA,EAGE,yBAFA,aCXwC,CDYxC,oBH1BF,CMjJE,QH+KE,aCdsC,CDetC,yBH3BJ,CMjJE,sGH2LE,cACA,oBHlCJ,CGqCE,oCACE,SHnCJ,CG4CA,kBAIE,sFCoD4B,CCzM1B,aL6GJ,CG4CA,IAIE,mBAFA,aAIA,aH5CF,CGoDA,OAEE,eHlDF,CG0DA,IAEE,iBHvDF,CG0DA,QAJE,qBHjDF,CGqDA,IAGE,eHxDF,CGiEA,MACE,wBH9DF,CGiEA,QAKE,oBAFA,aCpQS,CDmQT,qBC0E4B,CD3E5B,kBC2E4B,CDxE5B,eH7DF,CGiEA,GAGE,kBHhEF,CGwEA,MAEE,qBACA,mBHtEF,CG4EA,OAEE,eH1EF,CGiFA,aACE,mBACA,yCH9EF,CGiFA,sCAME,oBEtPE,kBFwPF,oBAHA,QH3EF,CGiFA,aAEE,gBH9EF,CGiFA,cAEE,mBH9EF,CGoFA,OACE,gBHjFF,CGwFA,gDAIE,yBHrFF,CG8FI,4GACE,cHxFN,CG8FA,wHAKE,kBADA,SH1FF,CG8FA,uCAEE,sBACA,SH3FF,CG+FA,+EASE,0BHjGF,CGoGA,SACE,cAEA,eHlGF,CGqGA,SAUE,SADA,SAHA,YAEA,SHtGF,CG6GA,OAQE,cAPA,cE9RI,gBAtCa,CF0UjB,oBAFA,oBAFA,eACA,UAKA,mBAPA,UHnGF,CG6GA,SACE,sBH1GF,CG8GA,kFAEE,WH3GF,CG8GA,cAME,wBADA,mBH9GF,CGsHA,yCACE,uBHnHF,CG2HA,6BAEE,0BADA,YHvHF,CG+HA,OACE,oBH5HF,CG+HA,QAEE,eADA,iBH3HF,CG+HA,SACE,YH5HF,CGiIA,SACE,sBH9HF,CD7VA,0CAIE,eKiS4B,CLhS5B,eKiS4B,CLpS5B,mBCkWF,CD3VA,OMgHM,gBL+ON,CD9VA,OM+GM,cLmPN,CDjWA,OM8GM,iBLuPN,CDpWA,OM6GM,gBL2PN,CDvWA,OM4GM,iBL+PN,CD1WA,OM2GM,cLmQN,CD5WA,MMyGM,iBAtCa,CNjEjB,eC+WF,CD3WA,WMmGM,cL8QN,CD5WA,sBAHE,eKsR4B,CLrR5B,eCoXF,CDlXA,WM8FM,gBLoRN,CD7WA,WMyFM,gBL0RN,CD9WA,sBAHE,eK8Q4B,CL7Q5B,eCsXF,CDpXA,WMoFM,gBLgSN,CDzWA,GAGE,SACA,+BAFA,kBKyEO,CL1EP,eC+WF,CDpWA,aMMI,cNHF,eCuWF,CDpWA,WAGE,wBKsQ4B,CLvQ5B,YCwWF,CD1VA,4BQnFE,gBADA,cPwbF,CDjWA,kBACE,oBCoWF,CDlWE,mCACE,kBCoWJ,CD1VA,YMjCI,cNmCF,wBC6VF,CDzVA,YMgBM,iBAtCa,CNuBjB,kBC6VF,CDzVA,mBAGE,aK1GS,CLwGT,cM7CE,aL2YJ,CD1VE,0BACE,YC4VJ,CQzcA,0BCCE,YAHA,cTydF,CQvdA,eAEE,qBJRS,CIST,yBEXE,oBNqO0B,CI5N5B,cRsdF,CQxcA,QAEE,oBR0cF,CQvcA,YAEE,cADA,mBR2cF,CQvcA,gBAEE,aJ3BS,CC2DP,aL0aJ,CWjfA,KAEE,aPoCQ,CCiCN,gBMpEF,qBXofF,CWjfE,OACE,aXmfJ,CW9eA,IAIE,wBPDS,CMXP,mBNuO0B,CO5N5B,UPTS,CCiEP,gBM1DF,mBXqfF,CW9eE,QNmDE,eMhDA,ePoQ0B,COtQ1B,SXkfJ,CW1eA,IAGE,aPjBS,COeT,cNyCE,eLscJ,CW1eE,SAEE,cNkCA,kBMjCA,iBX4eJ,CWveA,gBACE,gBPwiCkC,COviClC,iBX0eF,CYnhBE,WCIA,iBADA,kBADA,kBADA,mBADA,Ub2hBF,CcpeI,yBFvDF,WCYI,ebmhBJ,CACF,CczeI,yBFvDF,WCYI,ebwhBJ,CACF,Cc9eI,yBFvDF,WCYI,eb6hBJ,CACF,CcnfI,0BFvDF,WCYI,gBbkiBJ,CACF,CYniBE,iBCRA,iBADA,kBADA,kBADA,mBADA,UbujBF,CYjiBE,KCJA,aACA,eAEA,kBADA,kBb0iBF,CYliBE,YAEE,cADA,cZsiBJ,CYniBI,2CAGE,eADA,eZsiBN,CetkBE,sqBAIE,kBADA,mBAFA,kBACA,UfglBJ,Ce3jBM,KACE,aACA,YACA,cf8jBR,Ce5jBM,UACE,cAEA,eADA,UfgkBR,Ce3jBQ,OFFN,uBAIA,uBb8jBF,CehkBQ,OFFN,wBAIA,wBbmkBF,CerkBQ,OFFN,aAIA,abwkBF,Ce1kBQ,OFFN,wBAIA,wBb6kBF,Ce/kBQ,OFFN,wBAIA,wBbklBF,CeplBQ,OFFN,aAIA,abulBF,CezlBQ,OFFN,wBAIA,wBb4lBF,Ce9lBQ,OFFN,wBAIA,wBbimBF,CenmBQ,OFFN,aAIA,absmBF,CexmBQ,QFFN,wBAIA,wBb2mBF,Ce7mBQ,QFFN,wBAIA,wBbgnBF,CelnBQ,QFFN,cAIA,cbqnBF,CelnBM,aAAwB,QfsnB9B,CepnBM,YAAuB,QfwnB7B,CernBQ,SAAwB,OfynBhC,CeznBQ,SAAwB,Of6nBhC,Ce7nBQ,SAAwB,OfioBhC,CejoBQ,SAAwB,OfqoBhC,CeroBQ,SAAwB,OfyoBhC,CezoBQ,SAAwB,Of6oBhC,Ce7oBQ,SAAwB,OfipBhC,CejpBQ,SAAwB,OfqpBhC,CerpBQ,SAAwB,OfypBhC,CezpBQ,SAAwB,Of6pBhC,Ce7pBQ,UAAwB,QfiqBhC,CejqBQ,UAAwB,QfqqBhC,CerqBQ,UAAwB,QfyqBhC,CenqBU,UFTR,yBbgrBF,CevqBU,UFTR,0BborBF,Ce3qBU,UFTR,ebwrBF,Ce/qBU,UFTR,0Bb4rBF,CenrBU,UFTR,0BbgsBF,CevrBU,UFTR,ebosBF,Ce3rBU,UFTR,0BbwsBF,Ce/rBU,UFTR,0Bb4sBF,CensBU,UFTR,ebgtBF,CevsBU,WFTR,0BbotBF,Ce3sBU,WFTR,0BbwtBF,Cc7sBI,yBC9BE,QACE,aACA,YACA,cf+uBN,Ce7uBI,aACE,cAEA,eADA,UfgvBN,Ce3uBM,UFFN,uBAIA,uBb6uBA,Ce/uBM,UFFN,wBAIA,wBbivBA,CenvBM,UFFN,aAIA,abqvBA,CevvBM,UFFN,wBAIA,wBbyvBA,Ce3vBM,UFFN,wBAIA,wBb6vBA,Ce/vBM,UFFN,aAIA,abiwBA,CenwBM,UFFN,wBAIA,wBbqwBA,CevwBM,UFFN,wBAIA,wBbywBA,Ce3wBM,UFFN,aAIA,ab6wBA,Ce/wBM,WFFN,wBAIA,wBbixBA,CenxBM,WFFN,wBAIA,wBbqxBA,CevxBM,WFFN,cAIA,cbyxBA,CetxBI,gBAAwB,QfyxB5B,CevxBI,eAAuB,Qf0xB3B,CevxBM,YAAwB,Of0xB9B,Ce1xBM,YAAwB,Of6xB9B,Ce7xBM,YAAwB,OfgyB9B,CehyBM,YAAwB,OfmyB9B,CenyBM,YAAwB,OfsyB9B,CetyBM,YAAwB,OfyyB9B,CezyBM,YAAwB,Of4yB9B,Ce5yBM,YAAwB,Of+yB9B,Ce/yBM,YAAwB,OfkzB9B,CelzBM,YAAwB,OfqzB9B,CerzBM,aAAwB,QfwzB9B,CexzBM,aAAwB,Qf2zB9B,Ce3zBM,aAAwB,Qf8zB9B,CexzBQ,aFTR,abo0BA,Ce3zBQ,aFTR,yBbu0BA,Ce9zBQ,aFTR,0Bb00BA,Cej0BQ,aFTR,eb60BA,Cep0BQ,aFTR,0Bbg1BA,Cev0BQ,aFTR,0Bbm1BA,Ce10BQ,aFTR,ebs1BA,Ce70BQ,aFTR,0Bby1BA,Ceh1BQ,aFTR,0Bb41BA,Cen1BQ,aFTR,eb+1BA,Cet1BQ,cFTR,0Bbk2BA,Cez1BQ,cFTR,0Bbq2BA,CACF,Cc31BI,yBC9BE,QACE,aACA,YACA,cf43BN,Ce13BI,aACE,cAEA,eADA,Uf63BN,Cex3BM,UFFN,uBAIA,uBb03BA,Ce53BM,UFFN,wBAIA,wBb83BA,Ceh4BM,UFFN,aAIA,abk4BA,Cep4BM,UFFN,wBAIA,wBbs4BA,Cex4BM,UFFN,wBAIA,wBb04BA,Ce54BM,UFFN,aAIA,ab84BA,Ceh5BM,UFFN,wBAIA,wBbk5BA,Cep5BM,UFFN,wBAIA,wBbs5BA,Cex5BM,UFFN,aAIA,ab05BA,Ce55BM,WFFN,wBAIA,wBb85BA,Ceh6BM,WFFN,wBAIA,wBbk6BA,Cep6BM,WFFN,cAIA,cbs6BA,Cen6BI,gBAAwB,Qfs6B5B,Cep6BI,eAAuB,Qfu6B3B,Cep6BM,YAAwB,Ofu6B9B,Cev6BM,YAAwB,Of06B9B,Ce16BM,YAAwB,Of66B9B,Ce76BM,YAAwB,Ofg7B9B,Ceh7BM,YAAwB,Ofm7B9B,Cen7BM,YAAwB,Ofs7B9B,Cet7BM,YAAwB,Ofy7B9B,Cez7BM,YAAwB,Of47B9B,Ce57BM,YAAwB,Of+7B9B,Ce/7BM,YAAwB,Ofk8B9B,Cel8BM,aAAwB,Qfq8B9B,Cer8BM,aAAwB,Qfw8B9B,Cex8BM,aAAwB,Qf28B9B,Cer8BQ,aFTR,abi9BA,Cex8BQ,aFTR,yBbo9BA,Ce38BQ,aFTR,0Bbu9BA,Ce98BQ,aFTR,eb09BA,Cej9BQ,aFTR,0Bb69BA,Cep9BQ,aFTR,0Bbg+BA,Cev9BQ,aFTR,ebm+BA,Ce19BQ,aFTR,0Bbs+BA,Ce79BQ,aFTR,0Bby+BA,Ceh+BQ,aFTR,eb4+BA,Cen+BQ,cFTR,0Bb++BA,Cet+BQ,cFTR,0Bbk/BA,CACF,Ccx+BI,yBC9BE,QACE,aACA,YACA,cfygCN,CevgCI,aACE,cAEA,eADA,Uf0gCN,CergCM,UFFN,uBAIA,uBbugCA,CezgCM,UFFN,wBAIA,wBb2gCA,Ce7gCM,UFFN,aAIA,ab+gCA,CejhCM,UFFN,wBAIA,wBbmhCA,CerhCM,UFFN,wBAIA,wBbuhCA,CezhCM,UFFN,aAIA,ab2hCA,Ce7hCM,UFFN,wBAIA,wBb+hCA,CejiCM,UFFN,wBAIA,wBbmiCA,CeriCM,UFFN,aAIA,abuiCA,CeziCM,WFFN,wBAIA,wBb2iCA,Ce7iCM,WFFN,wBAIA,wBb+iCA,CejjCM,WFFN,cAIA,cbmjCA,CehjCI,gBAAwB,QfmjC5B,CejjCI,eAAuB,QfojC3B,CejjCM,YAAwB,OfojC9B,CepjCM,YAAwB,OfujC9B,CevjCM,YAAwB,Of0jC9B,Ce1jCM,YAAwB,Of6jC9B,Ce7jCM,YAAwB,OfgkC9B,CehkCM,YAAwB,OfmkC9B,CenkCM,YAAwB,OfskC9B,CetkCM,YAAwB,OfykC9B,CezkCM,YAAwB,Of4kC9B,Ce5kCM,YAAwB,Of+kC9B,Ce/kCM,aAAwB,QfklC9B,CellCM,aAAwB,QfqlC9B,CerlCM,aAAwB,QfwlC9B,CellCQ,aFTR,ab8lCA,CerlCQ,aFTR,yBbimCA,CexlCQ,aFTR,0BbomCA,Ce3lCQ,aFTR,ebumCA,Ce9lCQ,aFTR,0Bb0mCA,CejmCQ,aFTR,0Bb6mCA,CepmCQ,aFTR,ebgnCA,CevmCQ,aFTR,0BbmnCA,Ce1mCQ,aFTR,0BbsnCA,Ce7mCQ,aFTR,ebynCA,CehnCQ,cFTR,0Bb4nCA,CennCQ,cFTR,0Bb+nCA,CACF,CcrnCI,0BC9BE,QACE,aACA,YACA,cfspCN,CeppCI,aACE,cAEA,eADA,UfupCN,CelpCM,UFFN,uBAIA,uBbopCA,CetpCM,UFFN,wBAIA,wBbwpCA,Ce1pCM,UFFN,aAIA,ab4pCA,Ce9pCM,UFFN,wBAIA,wBbgqCA,CelqCM,UFFN,wBAIA,wBboqCA,CetqCM,UFFN,aAIA,abwqCA,Ce1qCM,UFFN,wBAIA,wBb4qCA,Ce9qCM,UFFN,wBAIA,wBbgrCA,CelrCM,UFFN,aAIA,aborCA,CetrCM,WFFN,wBAIA,wBbwrCA,Ce1rCM,WFFN,wBAIA,wBb4rCA,Ce9rCM,WFFN,cAIA,cbgsCA,Ce7rCI,gBAAwB,QfgsC5B,Ce9rCI,eAAuB,QfisC3B,Ce9rCM,YAAwB,OfisC9B,CejsCM,YAAwB,OfosC9B,CepsCM,YAAwB,OfusC9B,CevsCM,YAAwB,Of0sC9B,Ce1sCM,YAAwB,Of6sC9B,Ce7sCM,YAAwB,OfgtC9B,CehtCM,YAAwB,OfmtC9B,CentCM,YAAwB,OfstC9B,CettCM,YAAwB,OfytC9B,CeztCM,YAAwB,Of4tC9B,Ce5tCM,aAAwB,Qf+tC9B,Ce/tCM,aAAwB,QfkuC9B,CeluCM,aAAwB,QfquC9B,Ce/tCQ,aFTR,ab2uCA,CeluCQ,aFTR,yBb8uCA,CeruCQ,aFTR,0BbivCA,CexuCQ,aFTR,ebovCA,Ce3uCQ,aFTR,0BbuvCA,Ce9uCQ,aFTR,0Bb0vCA,CejvCQ,aFTR,eb6vCA,CepvCQ,aFTR,0BbgwCA,CevvCQ,aFTR,0BbmwCA,Ce1vCQ,aFTR,ebswCA,Ce7vCQ,cFTR,0BbywCA,CehwCQ,cFTR,0Bb4wCA,CACF,CgB1zCA,OAGE,aZSS,CYVT,kBZ2HO,CY5HP,UhB8zCF,CgBzzCE,oBAIE,6BAFA,cZ8U0B,CY7U1B,kBhB4zCJ,CgBxzCE,gBAEE,gCADA,qBhB2zCJ,CgBvzCE,mBACE,4BhByzCJ,CgB/yCE,0BAEE,ahBkzCJ,CgBtyCE,sDAEE,wBhB4yCJ,CgBxyCI,kDAEE,uBhB0yCN,CgBpyCE,mGAIE,QhBuyCJ,CgB9xCE,yCACE,0BhBiyCJ,CMh2CE,4BU4EI,iCZyQwB,CY1QxB,ahB0xCN,CiB32CI,mDAGE,wBjB82CN,CiB12CM,uFAIE,oBjB42CR,CiB91CQ,4GAEE,wBjBo2CV,CiB/3CI,yDAGE,wBjBk4CN,CiB93CM,+FAIE,oBjBg4CR,CiBl3CQ,kHAEE,wBjBw3CV,CiBn5CI,mDAGE,wBjBs5CN,CiBl5CM,uFAIE,oBjBo5CR,CiBt4CQ,4GAEE,wBjB44CV,CiBv6CI,0CAGE,wBjB06CN,CiBt6CM,2EAIE,oBjBw6CR,CiB15CQ,mGAEE,wBjBg6CV,CiB37CI,mDAGE,wBjB87CN,CiB17CM,uFAIE,oBjB47CR,CiB96CQ,4GAEE,wBjBo7CV,CiB/8CI,gDAGE,wBjBk9CN,CiB98CM,mFAIE,oBjBg9CR,CiBl8CQ,yGAEE,wBjBw8CV,CiBn+CI,6CAGE,wBjBs+CN,CiBl+CM,+EAIE,oBjBo+CR,CiBt9CQ,sGAEE,wBjB49CV,CiBv/CI,0CAGE,wBjB0/CN,CiBt/CM,2EAIE,oBjBw/CR,CiB1+CQ,mGAEE,wBjBg/CV,CiBl/CQ,yJAEE,iCjB8/CV,CgB96CI,sBAEE,wBZpGK,CYqGL,oBZ2PwB,CY7PxB,UhBm7CN,CgB56CI,uBAEE,wBZlHK,CYmHL,oBZlHK,CYgHL,ahBg7CN,CgBz6CA,YAEE,wBZpHS,CYmHT,UhB66CF,CgB16CE,mDAGE,oBhB46CJ,CgBz6CE,2BACE,QhB26CJ,CgBv6CI,oDACE,0BhBy6CN,CM9iDE,uCU6IM,qCZqNsB,CYtNtB,UhBs6CR,Ccr/CI,4BEiGA,qBAKI,iCAHA,cAEA,gBADA,UhBy5CN,CgBp5CM,qCACE,QhBs5CR,CACF,CcjgDI,4BEiGA,qBAKI,iCAHA,cAEA,gBADA,UhBo6CN,CgB/5CM,qCACE,QhBi6CR,CACF,Cc5gDI,4BEiGA,qBAKI,iCAHA,cAEA,gBADA,UhB+6CN,CgB16CM,qCACE,QhB46CR,CACF,CcvhDI,6BEiGA,qBAKI,iCAHA,cAEA,gBADA,UhB07CN,CgBr7CM,qCACE,QhBu7CR,CACF,CgBj8CI,kBAKI,iCAHA,cAEA,gBADA,UhBo8CR,CgB/7CQ,kCACE,QhBi8CV,CkB9mDA,cAWE,4BADA,qBdTS,CcWT,yBRbE,oBNqO0B,Cc3N5B,adDS,CcPT,cbwHI,cAtCa,Ca5EjB,ed8Q4B,CclR5B,iCdqesC,CchetC,edkR4B,CctR5B,uBCJI,oEDkBJ,CAhBA,UlB4nDF,CmBznDI,uCDLJ,cCMM,enB4nDJ,CACF,CkB9mDE,0BACE,yBACA,QlBgnDJ,CoBhoDE,oBAEE,qBhBRO,CgBSP,oBhBgdoC,CgB1clC,gChBgXwB,CgBxX1B,cAGA,SpBmoDJ,CkBhnDE,2BACE,adxBO,Cc0BP,SlBinDJ,CkBzmDE,+CAEE,wBdxCO,Cc0CP,SlBymDJ,CkBpmDE,qCAOE,qBdxDO,CcuDP,alBmmDJ,CkB7lDA,uCAEE,cACA,UlBgmDF,CkBtlDA,gBbTI,kBacF,ed0M4B,Cc5M5B,gBADA,mCADA,+BlB6lDF,CkBtlDA,mBbsCM,iBAtCa,CaIjB,eduI4B,CczI5B,iCADA,6BlB4lDF,CkBtlDA,mBb+BM,iBAtCa,CaWjB,ediI4B,CcnI5B,kCADA,8BlB4lDF,CkBhlDA,wBAQE,yBAEA,sCAHA,adpGS,Cc8FT,cAKA,ed6K4B,Cc9K5B,gBADA,sBd6Q4B,Cc9Q5B,mBd8Q4B,Cc/Q5B,UlB2lDF,CkBjlDE,gFAGE,eADA,elBmlDJ,CkBtkDA,iBRnII,mBNuO0B,CC7GxB,iBAtCa,CagDjB,gCdsWsC,CcnWtC,edyF4B,Cc3F5B,oBlB4kDF,CkBtkDA,iBR3II,mBNsO0B,CC5GxB,iBAtCa,CawDjB,+Bd+VsC,Cc5VtC,edgF4B,CclF5B,kBlB4kDF,CkB9jDA,8EACE,WlBqkDF,CkB7jDA,YACE,kBlBgkDF,CkB7jDA,WACE,cACA,iBlBgkDF,CkBxjDA,UACE,aACA,eAEA,iBADA,iBlB4jDF,CkBzjDE,uCAGE,iBADA,iBlB4jDJ,CkBljDA,YAEE,cACA,oBd0SsC,Cc5StC,iBlBujDF,CkBljDA,kBAGE,qBADA,gBdsSsC,CcvStC,iBlBujDF,CkBnjDE,6CACE,alBqjDJ,CkBjjDA,kBACE,elBojDF,CkBjjDA,mBAEE,mBADA,oBAGA,mBdyRsC,Cc1RtC,clBqjDF,CkBjjDE,qCAIE,cADA,qBdoRoC,CcrRpC,aADA,elBsjDJ,CoB9vDE,gBAKE,aFmNqC,CEvNrC,af0CA,cexCA,iBhBodoC,CgBrdpC,UpBowDJ,CoB9vDE,eAWE,2BV3CA,oBNqO0B,CgB3L1B,WANA,afsFE,iBAtCa,Ce3Cf,ehBkP0B,CgBpP1B,iBAFA,eACA,qBALA,kBACA,SACA,SpB0wDJ,CoB7vDI,0DAMI,wQF0LwD,CExLxD,yDADA,4BAEA,4DAPF,oBF8LmC,CE3LjC,kCpBiwDR,CoB1vDM,sEACE,oBFmLiC,CElLjC,gCpB4vDR,CoBzvDM,kLAEE,apB4vDR,CoBrvDI,0EAII,8EADA,kCpBuvDR,CoBhvDI,4DAMI,kiBAJF,oBF0JmC,CEvJjC,qCpBivDR,CoB7uDM,wEACE,oBFkJiC,CEjJjC,gCpB+uDR,CoBjuDM,4XAEE,apB0uDR,CoBluDM,sGACE,apBquDR,CoBluDM,kMAEE,apBquDR,CoB7tDM,sHACE,apBguDR,CoB9tDQ,oIACE,oBpBguDV,CoB5tDM,kNAEE,apB+tDR,CoB3tDQ,oJClJJ,wBDoJ2B,CADrB,oBpB8tDV,CoBxtDQ,gJACE,gCpB0tDV,CoB5sDM,sRACE,oBpBktDR,CoB/sDM,sMAEE,apBktDR,CoB9sDQ,sHACE,oBF4D+B,CE3D/B,gCpBgtDV,CoB72DE,kBAKE,aFmNqC,CEvNrC,af0CA,cexCA,iBhBodoC,CgBrdpC,UpBm3DJ,CoB72DE,iBAWE,2BV3CA,oBNqO0B,CgB3L1B,WANA,afsFE,iBAtCa,Ce3Cf,ehBkP0B,CgBpP1B,iBAFA,eACA,qBALA,kBACA,SACA,SpBy3DJ,CoB52DI,8DAMI,mTF0LwD,CExLxD,yDADA,4BAEA,4DAPF,oBF8LmC,CE3LjC,kCpBg3DR,CoBz2DM,0EACE,oBFmLiC,CElLjC,gCpB22DR,CoBx2DM,kMAEE,apB22DR,CoBp2DI,8EAII,8EADA,kCpBs2DR,CoB/1DI,gEAMI,6kBAJF,oBF0JmC,CEvJjC,qCpBg2DR,CoB51DM,4EACE,oBFkJiC,CEjJjC,gCpB81DR,CoBh1DM,4ZAEE,apBy1DR,CoBj1DM,0GACE,apBo1DR,CoBj1DM,kNAEE,apBo1DR,CoB50DM,0HACE,apB+0DR,CoB70DQ,wIACE,oBpB+0DV,CoB30DM,kOAEE,apB80DR,CoB10DQ,wJClJJ,wBDoJ2B,CADrB,oBpB60DV,CoBv0DQ,oJACE,gCpBy0DV,CoB3zDM,8RACE,oBpBi0DR,CoB9zDM,sNAEE,apBi0DR,CoB7zDQ,0HACE,oBF4D+B,CE3D/B,gCpB+zDV,CkBxvDA,aAGE,mBAFA,aACA,kBlB4vDF,CkBtvDE,yBACE,UlBwvDJ,Cct8DI,yBImNA,mBAGE,sBlBuvDJ,CkBlvDE,4CANE,mBADA,aAGA,elB6vDJ,CkBzvDE,yBAEE,cACA,kBlBsvDJ,CkBhvDE,2BACE,qBAEA,sBADA,UlBmvDJ,CkB9uDE,qCACE,oBlBgvDJ,CkB7uDE,sDAEE,UlB+uDJ,CkB1uDE,yBAEE,mBADA,aAEA,uBAEA,eADA,UlB6uDJ,CkB1uDE,+BAEE,cAGA,cADA,mBd2LkC,Cc5LlC,aAFA,iBlBgvDJ,CkBzuDE,6BACE,mBACA,sBlB2uDJ,CkBzuDE,mCACE,elB2uDJ,CACF,CsB5iEA,KAQE,yBACA,uBZVE,oBNqO0B,CkBhO5B,alBMS,CkBTT,qBjBwHI,cAtCa,CiBhFjB,elBkR4B,CmBpL5B,enByL4B,CmB3L5B,uBD1FA,kBHLI,6HGWJ,CAJA,0CADA,qBtBsjEF,CmBvjEI,uCGLJ,KHMM,enB0jEJ,CACF,CM3jEE,WgBQE,alBJO,CkBKP,oBtBsjEJ,CsBnjEE,sBAGE,gClB2W0B,CkB5W1B,StBqjEJ,CsBhjEE,4BAEE,WtBijEJ,CsBliEA,uCAEE,mBtBoiEF,CsB3hEE,aDrDE,wBjByEW,CmBvEb,oBnBuEa,CmBzEb,UvBslEF,CMhlEE,mBeNE,wBED2D,CAS3D,oBATqG,CAOrG,UvBqlEJ,CuBhlEE,sCAMI,gCvB6kEN,CuBxkEE,4CAGE,wBnBgDW,CmB/CX,oBnB+CW,CmBjDX,UvB2kEJ,CuBlkEE,uIAIE,wBAtC+I,CA0C/I,oBA1CyL,CAqCzL,UvBokEJ,CuB7jEI,yJAKI,gCvB2jER,CsBtjEE,eDrDE,wBjByEW,CmBvEb,oBnBuEa,CmBzEb,UvBinEF,CM3mEE,qBeNE,wBED2D,CAS3D,oBATqG,CAOrG,UvBgnEJ,CuB3mEE,0CAMI,gCvBwmEN,CuBnmEE,gDAGE,wBnBgDW,CmB/CX,oBnB+CW,CmBjDX,UvBsmEJ,CuB7lEE,6IAIE,wBAtC+I,CA0C/I,oBA1CyL,CAqCzL,UvB+lEJ,CuBxlEI,+JAKI,gCvBslER,CsBjlEE,aDrDE,wBjByEW,CmBvEb,oBnBuEa,CmBzEb,UvB4oEF,CMtoEE,mBeNE,wBED2D,CAS3D,oBATqG,CAOrG,UvB2oEJ,CuBtoEE,sCAMI,gCvBmoEN,CuB9nEE,4CAGE,wBnBgDW,CmB/CX,oBnB+CW,CmBjDX,UvBioEJ,CuBxnEE,uIAIE,wBAtC+I,CA0C/I,oBA1CyL,CAqCzL,UvB0nEJ,CuBnnEI,yJAKI,gCvBinER,CsB5mEE,UDrDE,wBjByEW,CmBvEb,oBnBuEa,CmBzEb,UvBuqEF,CMjqEE,gBeNE,wBED2D,CAS3D,oBATqG,CAOrG,UvBsqEJ,CuBjqEE,gCAMI,gCvB8pEN,CuBzpEE,sCAGE,wBnBgDW,CmB/CX,oBnB+CW,CmBjDX,UvB4pEJ,CuBnpEE,8HAIE,wBAtC+I,CA0C/I,oBA1CyL,CAqCzL,UvBqpEJ,CuB9oEI,gJAKI,gCvB4oER,CsBvoEE,aDrDE,wBjByEW,CmBvEb,oBnBuEa,CmBzEb,avBksEF,CM5rEE,mBeNE,wBED2D,CAS3D,oBATqG,CAOrG,avBisEJ,CuB5rEE,sCAMI,gCvByrEN,CuBprEE,4CAGE,wBnBgDW,CmB/CX,oBnB+CW,CmBjDX,avBurEJ,CuB9qEE,uIAIE,wBAtC+I,CA0C/I,oBA1CyL,CAqCzL,avBgrEJ,CuBzqEI,yJAKI,gCvBuqER,CsBlqEE,YDrDE,wBjByEW,CmBvEb,oBnBuEa,CmBzEb,UvB6tEF,CMvtEE,kBeNE,wBED2D,CAS3D,oBATqG,CAOrG,UvB4tEJ,CuBvtEE,oCAMI,gCvBotEN,CuB/sEE,0CAGE,wBnBgDW,CmB/CX,oBnB+CW,CmBjDX,UvBktEJ,CuBzsEE,oIAIE,wBAtC+I,CA0C/I,oBA1CyL,CAqCzL,UvB2sEJ,CuBpsEI,sJAKI,gCvBksER,CsB7rEE,WDrDE,wBjByEW,CmBvEb,oBnBuEa,CmBzEb,avBwvEF,CMlvEE,iBeNE,wBED2D,CAS3D,oBATqG,CAOrG,avBuvEJ,CuBlvEE,kCAMI,gCvB+uEN,CuB1uEE,wCAGE,wBnBgDW,CmB/CX,oBnB+CW,CmBjDX,avB6uEJ,CuBpuEE,iIAIE,wBAtC+I,CA0C/I,oBA1CyL,CAqCzL,avBsuEJ,CuB/tEI,mJAKI,gCvB6tER,CsBxtEE,UDrDE,wBjByEW,CmBvEb,oBnBuEa,CmBzEb,UvBmxEF,CM7wEE,gBeNE,wBED2D,CAS3D,oBATqG,CAOrG,UvBkxEJ,CuB7wEE,gCAMI,gCvB0wEN,CuBrwEE,sCAGE,wBnBgDW,CmB/CX,oBnB+CW,CmBjDX,UvBwwEJ,CuB/vEE,8HAIE,wBAtC+I,CA0C/I,oBA1CyL,CAqCzL,UvBiwEJ,CuB1vEI,gJAKI,gCvBwvER,CsB7uEE,qBCHA,oBnBiBa,CmBlBb,avBsvEF,CMvyEE,2BiBsDE,wBnBaW,CmBZX,oBnBYW,CmBdX,UvBuvEJ,CuBlvEE,sDAEE,gCvBmvEJ,CuBhvEE,4DAGE,yBADA,avBkvEJ,CuB9uEE,+JAIE,wBnBNW,CmBOX,oBnBPW,CmBKX,UvBgvEJ,CuB5uEI,iLAKI,gCvB0uER,CsBtwEE,uBCHA,oBnBiBa,CmBlBb,avB+wEF,CMh0EE,6BiBsDE,wBnBaW,CmBZX,oBnBYW,CmBdX,UvBgxEJ,CuB3wEE,0DAEE,gCvB4wEJ,CuBzwEE,gEAGE,yBADA,avB2wEJ,CuBvwEE,qKAIE,wBnBNW,CmBOX,oBnBPW,CmBKX,UvBywEJ,CuBrwEI,uLAKI,gCvBmwER,CsB/xEE,qBCHA,oBnBiBa,CmBlBb,avBwyEF,CMz1EE,2BiBsDE,wBnBaW,CmBZX,oBnBYW,CmBdX,UvByyEJ,CuBpyEE,sDAEE,gCvBqyEJ,CuBlyEE,4DAGE,yBADA,avBoyEJ,CuBhyEE,+JAIE,wBnBNW,CmBOX,oBnBPW,CmBKX,UvBkyEJ,CuB9xEI,iLAKI,gCvB4xER,CsBxzEE,kBCHA,oBnBiBa,CmBlBb,avBi0EF,CMl3EE,wBiBsDE,wBnBaW,CmBZX,oBnBYW,CmBdX,UvBk0EJ,CuB7zEE,gDAEE,gCvB8zEJ,CuB3zEE,sDAGE,yBADA,avB6zEJ,CuBzzEE,sJAIE,wBnBNW,CmBOX,oBnBPW,CmBKX,UvB2zEJ,CuBvzEI,wKAKI,gCvBqzER,CsBj1EE,qBCHA,oBnBiBa,CmBlBb,avB01EF,CM34EE,2BiBsDE,wBnBaW,CmBZX,oBnBYW,CmBdX,avB21EJ,CuBt1EE,sDAEE,gCvBu1EJ,CuBp1EE,4DAGE,yBADA,avBs1EJ,CuBl1EE,+JAIE,wBnBNW,CmBOX,oBnBPW,CmBKX,avBo1EJ,CuBh1EI,iLAKI,gCvB80ER,CsB12EE,oBCHA,oBnBiBa,CmBlBb,avBm3EF,CMp6EE,0BiBsDE,wBnBaW,CmBZX,oBnBYW,CmBdX,UvBo3EJ,CuB/2EE,oDAEE,gCvBg3EJ,CuB72EE,0DAGE,yBADA,avB+2EJ,CuB32EE,4JAIE,wBnBNW,CmBOX,oBnBPW,CmBKX,UvB62EJ,CuBz2EI,8KAKI,gCvBu2ER,CsBn4EE,mBCHA,oBnBiBa,CmBlBb,avB44EF,CM77EE,yBiBsDE,wBnBaW,CmBZX,oBnBYW,CmBdX,avB64EJ,CuBx4EE,kDAEE,gCvBy4EJ,CuBt4EE,wDAGE,yBADA,avBw4EJ,CuBp4EE,yJAIE,wBnBNW,CmBOX,oBnBPW,CmBKX,avBs4EJ,CuBl4EI,2KAKI,gCvBg4ER,CsB55EE,kBCHA,oBnBiBa,CmBlBb,avBq6EF,CMt9EE,wBiBsDE,wBnBaW,CmBZX,oBnBYW,CmBdX,UvBs6EJ,CuBj6EE,gDAEE,gCvBk6EJ,CuB/5EE,sDAGE,yBADA,avBi6EJ,CuB75EE,sJAIE,wBnBNW,CmBOX,oBnBPW,CmBKX,UvB+5EJ,CuB35EI,wKAKI,gCvBy5ER,CsB16EA,UAEE,alB6FwC,CkB9FxC,elB8M4B,CkB5M5B,oBtB66EF,CMh/EE,gBgBsEE,alB2FsC,CkB1FtC,yBtB66EJ,CsB16EE,gCAGE,gBADA,yBtB46EJ,CsBx6EE,sCAEE,alBjFO,CkBkFP,mBtBy6EJ,CsB95EA,2BZrGI,mBNsO0B,CC5GxB,iBAtCa,CkBcjB,enB6H4B,CmB/H5B,kBvB06EF,CsBj6EA,2BZzGI,mBNuO0B,CC7GxB,iBAtCa,CkBcjB,enB8H4B,CmBhI5B,oBvBi7EF,CsB/5EA,WACE,cACA,UtBk6EF,CsB/5EE,sBACE,gBtBi6EJ,CsBz5EE,sFACE,UtB85EJ,CwBpiFA,MLMM,8BnBkiFN,CmB7hFI,uCKXJ,MLYM,enBgiFJ,CACF,CwB1iFE,iBACE,SxB4iFJ,CwBviFE,qBACE,YxB0iFJ,CwBtiFA,YAEE,SACA,gBAFA,kBLTI,2BnBqjFN,CmBhjFI,uCKGJ,YLFM,enBmjFJ,CACF,CyB/jFA,uCAIE,iBzBkkFF,CyB/jFA,iBACE,kBzBkkFF,C0B9iFI,uBA1BF,gBACA,6BAFA,8BADA,sBAgCI,WAHA,qBACA,kBtB0NwB,CsBzNxB,qB1BqjFN,C0B5hFI,6BACE,a1B8hFN,CyBxkFA,eAeE,4BADA,qBrBvBS,CqByBT,2Bf3BE,oBNqO0B,CqB/M5B,arBXS,CqBKT,aACA,WpByGI,cAtCa,CoBtEjB,OAUA,gBAJA,mBAFA,erButBkC,CqBttBlC,gBAPA,kBAWA,gBAVA,SAEA,YzBwlFF,CyBnkFI,oBAEE,OADA,UzBukFN,CyBnkFI,qBAEE,UADA,OzBukFN,Cc1jFI,yBWnBA,uBAEE,OADA,UzBklFJ,CyB9kFE,wBAEE,UADA,OzBilFJ,CACF,CcrkFI,yBWnBA,uBAEE,OADA,UzB4lFJ,CyBxlFE,wBAEE,UADA,OzB2lFJ,CACF,Cc/kFI,yBWnBA,uBAEE,OADA,UzBsmFJ,CyBlmFE,wBAEE,UADA,OzBqmFJ,CACF,CczlFI,0BWnBA,uBAEE,OADA,UzBgnFJ,CyB5mFE,wBAEE,UADA,OzB+mFJ,CACF,CyBvmFE,uBAEE,YAEA,qBrBorBgC,CqBrrBhC,aAFA,QzB4mFJ,C0BxoFI,+BAnBF,yBACA,6BAFA,8BADA,aAyBI,WAHA,qBACA,kBtB0NwB,CsBzNxB,qB1B+oFN,C0BtnFI,qCACE,a1BwnFN,CyB7mFE,0BAGE,UAEA,mBrBsqBgC,CqBvqBhC,aAFA,WADA,KzBonFJ,C0B7pFI,kCAZF,+BACA,uBAFA,eADA,4BAkBI,WAHA,qBACA,kBtB0NwB,CsBzNxB,qB1BoqFN,C0B3oFI,wCACE,a1B6oFN,CyBxnFI,kCACE,gBzB0nFN,CyBpnFE,yBAGE,UAEA,oBrBqpBgC,CqBtpBhC,aAFA,WADA,KzB2nFJ,C0BrrFI,iCAIE,WAHA,qBAeE,aAdF,kBtB0NwB,CsBzNxB,qB1BwrFN,C0BxqFM,kCAxBJ,+BADA,wBADA,4BA8BM,WAHA,qBACA,mBtBuMsB,CsBtMtB,qB1BirFR,C0B3qFI,uCACE,a1B6qFN,CyBvoFI,kCACE,gBzByoFN,CyBjoFE,0IAKE,YADA,UzBkoFJ,CyB5nFA,kBE3GE,6BAHA,SACA,eACA,e3B+uFF,CyB5nFA,eASE,yBACA,SANA,WAEA,arBhHS,CqB2GT,cAIA,erB4J4B,CqB9J5B,sBAIA,mBACA,mBANA,UzBuoFF,CMnvFE,0CeVE,wBjBEO,CqB2IP,arB0mBgC,CqBzmBhC,oBzBonFJ,CyBhnFE,4CJlJE,wBjB+O0B,CqB3F1B,UrBnJO,CqBoJP,oBzBknFJ,CyB9mFE,gDAIE,yBAFA,arBpJO,CqBqJP,mBzBgnFJ,CyBvmFA,oBACE,azB0mFF,CyBtmFA,iBAKE,arBxKS,CqBoKT,cpBlDI,iBAtCa,CoB0FjB,gBADA,qBAIA,kBzBymFF,CyBrmFA,oBAGE,arB7KS,CqB2KT,cACA,qBzBymFF,C4BlyFA,+BAGE,oBADA,kBAEA,qB5BqyFF,C4BnyFE,yCAEE,cADA,iB5BuyFJ,C4B/xFI,wNAGE,S5BsyFN,C4BhyFA,aACE,aACA,eACA,0B5BmyFF,C4BjyFE,0BACE,U5BmyFJ,C4B7xFE,0EAEE,gB5BgyFJ,C4B5xFE,mGlBvBE,4BkByB6B,ClB1B7B,yBVyzFJ,C4B5xFE,+ElBdE,2BkBgB4B,ClBjB5B,wBVgzFJ,C4B/wFA,uBAEE,sBADA,sB5BmxFF,C4BhxFE,0GAGE,a5BgxFJ,C4B7wFE,wCACE,c5B+wFJ,C4B3wFA,yEAEE,qBADA,qB5B+wFF,C4B3wFA,yEAEE,oBADA,oB5B+wFF,C4B1vFA,oBAEE,uBADA,sBAEA,sB5B6vFF,C4B3vFE,wDAEE,U5B6vFJ,C4B1vFE,4FAEE,e5B4vFJ,C4BxvFE,qHlBjGE,2BkBmG8B,ClBpG9B,4BV+1FJ,C4BxvFE,iGlBrHE,wBkBuH2B,ClBtH3B,yBVi3FJ,C4BzuFE,yDAEE,e5B4uFJ,C4B1uFI,gMAGE,mBACA,oBAFA,iB5BgvFN,C6Bv4FA,aAIE,oBAFA,aACA,eAFA,kBAIA,U7B04FF,C6Bx4FE,sHAKE,cAIA,gBALA,kBAIA,Q7By4FJ,C6Bt4FI,0gBAGE,gB7Bi5FN,C6B54FE,yIAGE,S7B84FJ,C6B14FE,mDACE,S7B44FJ,C6Bv4FI,yFnBvBA,4BmBuBkD,CnBxBlD,yBVo6FJ,C6B34FI,2FnBVA,2BmBUkD,CnBXlD,wBV25FJ,C6B34FE,0BAEE,mBADA,Y7B84FJ,C6B34FI,kInBjCA,4BmBkC4E,CnBnC5E,yBVi7FJ,C6B74FI,+DnBrBA,2BmBqBqE,CnBtBrE,wBVu6FJ,C6Bt4FA,yCAEE,Y7By4FF,C6Bp4FE,mDACE,kBACA,S7Bu4FJ,C6Br4FI,+DACE,S7Bw4FN,C6Bp4FE,4VAIE,gB7B04FJ,C6Bt4FA,qBAAuB,iB7B04FvB,C6Bz4FA,oBAAsB,gB7B64FtB,C6Br4FA,kBAEE,mBASA,wBzBvGS,CyBwGT,yBnB5GE,oBNqO0B,CyB7H5B,azB/FS,CyBwFT,axByBI,cAtCa,CwBkBjB,ezBgL4B,CyB/K5B,ezBoL4B,CyBvL5B,gBADA,uBAMA,kBACA,kB7B24FF,C6Br4FE,2EAEE,Y7Bu4FJ,C6B73FA,2EAEE,+B7Bg4FF,C6B73FA,6PnBjII,mBNsO0B,CC5GxB,iBAtCa,CwBqDjB,ezBsF4B,CyBxF5B,kB7Bm4FF,C6B73FA,2EAEE,gC7Bg4FF,C6B73FA,6PnBlJI,mBNuO0B,CC7GxB,iBAtCa,CwBsEjB,ezBsE4B,CyBxE5B,oB7Bm4FF,C6B73FA,8DAEE,qB7Bg4FF,C6Br3FA,6XnB1JI,4BmBgK2B,CnBjK3B,yBV0hGJ,C6Bt3FA,+WnBrJI,2BmB2J0B,CnB5J1B,wBVshGJ,C8BhjGA,gBAEE,cACA,kBACA,oBAHA,iB9BsjGF,C8BhjGA,uBACE,oBACA,iB9BmjGF,C8BhjGA,sBAGE,UAFA,kBACA,U9BojGF,C8BjjGE,2DTpBE,wBjB+O0B,C0BzN1B,oB1ByN0B,C0B1N1B,U9BqjGJ,C8B/iGE,yDAKI,gC9B6iGN,C8BziGE,uEACE,oB9B2iGJ,C8BxiGE,yEAEE,wB1B8e4C,C0B7e5C,oB1B6e4C,C0B/e5C,U9B4iGJ,C8BriGI,qDACE,a9BuiGN,C8BriGM,4DACE,wB9BuiGR,C8B7hGA,sBAEE,gBADA,kBAEA,kB9BgiGF,C8B7hGE,6BASE,qB1B5EO,C0B6EP,yBAHA,mB9BkiGJ,C8B1hGE,yDAPE,WAJA,cAEA,W1ByboC,C0B5bpC,aAFA,kBACA,WAGA,U9B8iGJ,C8BpiGE,4BAQE,gC9B4hGJ,C8BlhGE,8CpBtGE,oBV4nGJ,C8BjhGI,2EACE,yN9BmhGN,C8B9gGI,kFThHA,wBjB+O0B,C0B9HxB,oB9BihGN,C8B7gGI,iFACE,uK9B+gGN,C8BvgGI,gLACE,0B9B4gGN,C8BlgGE,2CAEE,iB9BogGJ,C8BhgGI,wEACE,oK9BkgGN,C8B7/FI,kFACE,0B9B+/FN,C8Br/FA,eACE,oB9Bw/FF,C8Br/FI,4CAKE,mB1BgY0C,C0BpY1C,cAEA,mBADA,a9By/FN,C8Bn/FI,2CAKE,wB1B3KK,C0B6KL,mB1BsX0C,C0BzX1C,uB1B0X0C,C0B5X1C,0BADA,uBX7KA,iIWoLA,CALA,sB9By/FN,CmBnqGI,uCWuKA,2CXtKE,enBsqGJ,CACF,C8Bp/FI,yEACE,qB1BzLK,C0B0LL,4B9Bs/FN,C8Bj/FI,mFACE,0B9Bm/FN,C8Bv+FA,eAgBE,gBALA,yN1BmWkC,C0BlWlC,qB1BxNS,C0ByNT,yBpB3NE,oBNqO0B,C0Bd5B,a1B9MS,C0BsMT,qBzBrFI,cAtCa,CyBiIjB,e1BiE4B,C0BrE5B,iC1BwRsC,C0BnRtC,e1BqE4B,C0BzE5B,uCAMA,sBARA,U9Bs/FF,C8Bt+FE,qBACE,oB1B0PoC,C0BrPlC,gC1BmW8B,C0BvWhC,S9By+FJ,C8Bl+FI,gCAOE,qB1B9OK,C0B6OL,a9Bg+FN,C8B39FE,8DAIE,sBAFA,YACA,oB9B69FJ,C8Bz9FE,wBAEE,wB1BzPO,C0BwPP,a9B49FJ,C8Bv9FE,2BACE,Y9By9FJ,C8Br9FA,kBzB5IM,iBAtCa,CyBmLjB,gC1BmOsC,C0BjOtC,qB1B0H4B,C0BzH5B,kB1B0H4B,C0B5H5B,kB9B29FF,C8Br9FA,kBzBpJM,iBAtCa,CyB2LjB,+B1B4NsC,C0B1NtC,oB1BuH4B,C0BtH5B,iB1BuH4B,C0BzH5B,iB9B29FF,C8Bh9FA,aAEE,qBAGA,e9Bm9FF,C8Bh9FA,gCAJE,iC1B0MsC,C0B7MtC,kBAEA,U9B89FF,C8Bz9FA,mBAKE,SACA,UAJA,S9Bu9FF,C8Bj9FE,4CACE,oB1B+KoC,C0B9KpC,gC9Bm9FJ,C8Bh9FE,+CACE,wB9Bk9FJ,C8B98FI,qDACE,gB9Bg9FN,C8B58FE,yDACE,yB9B88FJ,C8B18FA,mBAYE,qB1BzUS,C0B0UT,yBpB5UE,oBNqO0B,C0BmG5B,e1BlD4B,C0B+C5B,iC1BoKsC,C0BtKtC,OACA,S9Bq9FF,C8Bz8FE,4CANA,a1BjUS,C0BgUT,e1B9C4B,C0B2C5B,uBANA,kBAEA,QADA,K9Bw+FF,C8Bz9FE,yBT/UE,wBjBGO,C0ByVP,oBpB7VA,+BoB8VuB,CAVvB,SAOA,iBALA,cACA,2B1B8IoC,C0BhJpC,S9Bo9FJ,C8Bj8FA,cAKE,gBADA,yBAFA,cACA,UAFA,U9Bw8FF,C8Bl8FE,oBACE,Y9Bo8FJ,C8Bh8FI,0CAA0B,+C9Bm8F9B,C8Bl8FI,sCAA0B,+C9Bq8F9B,C8Bp8FI,+BAA0B,+C9Bu8F9B,C8Bp8FE,gCACE,Q9Bs8FJ,C8Bn8FE,oCASE,gBTrYA,wBjB+O0B,C0BkJ1B,Q1B6NyC,CM/lBzC,kBNgmByC,C0BjOzC,W1B6NyC,C0B5NzC,mBX/XE,8GWoYF,CXpYE,sGWoYF,CAPA,U9B48FJ,CmBp0GI,uCWuXF,oCXtXI,uCnBu0GJ,CACF,C8Bv8FI,2CTvYA,wBrBi1GJ,C8Br8FE,6CAKE,wB1B7YO,C0B8YP,mBpBnZA,kBNylBgC,C0BzMhC,YACA,c1BsMgC,C0BxMhC,Y1BuMgC,C0BxMhC,U9B68FJ,C8Bn8FE,gCAQE,gBT/ZA,wBjB+O0B,C0B4K1B,Q1BmMyC,CM/lBzC,kBNgmByC,C0BtMzC,W1BkMyC,Ce3lBvC,2GW8ZF,CX9ZE,sGW8ZF,CANA,U9B28FJ,CmB91GI,uCWkZF,gCXjZI,oCnBi2GJ,CACF,C8Bv8FI,uCTjaA,wBrB22GJ,C8Br8FE,gCAKE,wB1BvaO,C0BwaP,mBpB7aA,kBNylBgC,C0B/KhC,YACA,c1B4KgC,C0B9KhC,Y1B6KgC,C0B9KhC,U9B68FJ,C8Bn8FE,yBAWE,gBT5bA,wBjB+O0B,C0ByM1B,Q1BsKyC,CM/lBzC,kBNgmByC,C0B5KzC,W1BwKyC,C0BrKzC,iB1BxD0B,C0BuD1B,kB1BvD0B,C0BsD1B,aXpbE,0GW2bF,CX3bE,sGW2bF,CATA,U9B88FJ,CmB33GI,uCW4aF,yBX3aI,mCnB83GJ,CACF,C8Bv8FI,gCT9bA,wBrBw4GJ,C8Br8FE,yBAKE,yBACA,mBACA,mBAJA,YACA,c1B+IgC,C0BjJhC,Y1BgJgC,C0BjJhC,U9B68FJ,C8B97FE,4DAJE,wB1B3cO,CMLP,kBV25GJ,C8Bv8FE,8BACE,iB9Bs8FJ,C8Bh8FI,6CACE,wB9Bk8FN,C8B/7FI,sDACE,c9Bi8FN,C8B97FI,yCACE,wB9Bg8FN,C8B77FI,yCACE,c9B+7FN,C8B57FI,kCACE,wB9B87FN,C8Bz7FA,+DXhfM,sGnB+6GN,CmB16GI,uCW2eJ,+DX1eM,enB+6GJ,CACF,C+Bv7GA,KACE,aACA,eAGA,gBADA,gBADA,c/B47GF,C+Bv7GA,UACE,cACA,kB/B07GF,CMz7GE,gCyBEE,oB/B07GJ,C+Bt7GE,mBACE,a3BVO,C2BYP,eADA,mB/By7GJ,C+Bh7GA,UACE,+B/Bm7GF,C+Bj7GE,oBACE,kB/Bm7GJ,C+Bh7GE,oBACE,uBrB3BA,6BN4N0B,CM3N1B,8BV88GJ,CM78GE,oDyB6BI,oC/Bm7GN,C+Bh7GI,6BAEE,yBACA,mBAFA,a/Bo7GN,C+B96GE,8DAGE,qB3BnDO,C2BoDP,iC3B+nBgC,C2BjoBhC,a/Bk7GJ,C+B76GE,yBrBhDE,wBqBoD2B,CrBnD3B,yBqBmD2B,CAF3B,e/Bg7GJ,C+Bp6GE,qBrBvEE,oBV++GJ,C+Bp6GE,uDAGE,wB3BkK0B,C2BnK1B,U/Bu6GJ,C+B55GE,oBACE,cACA,iB/B+5GJ,C+B15GE,yBACE,aACA,YACA,iB/B65GJ,C+Bn5GE,uBACE,Y/Bs5GJ,C+Bp5GE,qBACE,a/Bs5GJ,CgC1/GA,QAME,mBALA,iBhCkgHF,CgCz/GE,oDANA,mBAFA,aACA,eAEA,6BhCqgHF,CgCl/GA,cACE,qB3BqFI,iBAtCa,C2B1CjB,oBAFA,iB5BoFO,C4BrFP,uB5BmqBkC,C4BpqBlC,oB5BoqBkC,C4B/pBlC,kBhCq/GF,CMrhHE,wC0BmCE,oBhCq/GJ,CgC5+GA,YACE,aACA,sBAGA,gBADA,gBADA,chCi/GF,CgC7+GE,sBAEE,eADA,ehCg/GJ,CgC5+GE,2BAEE,WADA,ehC++GJ,CgCr+GA,aACE,qBAEA,oB5B0lBkC,C4B3lBlC,iBhCy+GF,CgC59GA,iBAKE,mBAJA,gBACA,WhCg+GF,CgCz9GA,gBAIE,yBACA,uBtB3GE,oBNqO0B,CC3GxB,iBAtCa,C2BqBjB,cAFA,qBhCi+GF,CM7jHE,4C0BoGE,oBhC49GJ,CgCt9GA,qBAME,yBACA,0BAFA,WAJA,qBAEA,aACA,sBAFA,WhC89GF,CcjhHI,4BkBoEI,gEAGE,eADA,ehCk9GR,CACF,CctiHI,yBkB+EA,kBAUI,qBACA,0BhCi9GN,CgC/8GM,8BACE,kBhCi9GR,CgC/8GQ,6CACE,iBhCi9GV,CgC98GQ,wCAEE,kB5BwiBwB,C4BziBxB,mBhCi9GV,CgC38GM,gEAEE,gBhC68GR,CgC18GM,mCACE,uBAGA,ehC08GR,CgCv8GM,kCACE,YhCy8GR,CACF,CcpjHI,4BkBoEI,gEAGE,eADA,ehCo/GR,CACF,CcxkHI,yBkB+EA,kBAUI,qBACA,0BhCm/GN,CgCj/GM,8BACE,kBhCm/GR,CgCj/GQ,6CACE,iBhCm/GV,CgCh/GQ,wCAEE,kB5BwiBwB,C4BziBxB,mBhCm/GV,CgC7+GM,gEAEE,gBhC++GR,CgC5+GM,mCACE,uBAGA,ehC4+GR,CgCz+GM,kCACE,YhC2+GR,CACF,CctlHI,4BkBoEI,gEAGE,eADA,ehCshHR,CACF,Cc1mHI,yBkB+EA,kBAUI,qBACA,0BhCqhHN,CgCnhHM,8BACE,kBhCqhHR,CgCnhHQ,6CACE,iBhCqhHV,CgClhHQ,wCAEE,kB5BwiBwB,C4BziBxB,mBhCqhHV,CgC/gHM,gEAEE,gBhCihHR,CgC9gHM,mCACE,uBAGA,ehC8gHR,CgC3gHM,kCACE,YhC6gHR,CACF,CcxnHI,6BkBoEI,gEAGE,eADA,ehCwjHR,CACF,Cc5oHI,0BkB+EA,kBAUI,qBACA,0BhCujHN,CgCrjHM,8BACE,kBhCujHR,CgCrjHQ,6CACE,iBhCujHV,CgCpjHQ,wCAEE,kB5BwiBwB,C4BziBxB,mBhCujHV,CgCjjHM,gEAEE,gBhCmjHR,CgChjHM,mCACE,uBAGA,ehCgjHR,CgC7iHM,kCACE,YhC+iHR,CACF,CgCxlHI,eAUI,qBACA,0BhCilHR,CgC1lHQ,0DAGE,eADA,ehC6lHV,CgCplHQ,2BACE,kBhCslHV,CgCplHU,0CACE,iBhCslHZ,CgCnlHU,qCAEE,kB5BwiBwB,C4BziBxB,mBhCslHZ,CgChlHQ,0DAEE,gBhCklHV,CgC/kHQ,gCACE,uBAGA,ehC+kHV,CgC5kHQ,+BACE,YhC8kHV,CMjvHE,gG0BqLI,ehCmkHN,CgC9jHI,oCACE,ehCgkHN,CM3vHE,oF0B8LM,ehCgkHR,CgC7jHM,6CACE,ehC+jHR,CgC3jHI,0KAIE,ehC6jHN,CgCzjHE,8BAEE,sB5BkgBgC,C4BngBhC,ehC4jHJ,CgCxjHE,mCACE,qQhC0jHJ,CgCvjHE,2BACE,ehCyjHJ,CMjxHE,mG0B6NM,ehC0jHR,CMvxHE,6F0ByOI,UhCqjHN,CgChjHI,mCACE,ehCkjHN,CMjyHE,kF0BkPM,ehCkjHR,CgC/iHM,4CACE,ehCijHR,CgC7iHI,sKAIE,UhC+iHN,CgC3iHE,6BAEE,sB5BucgC,C4BxchC,ehC8iHJ,CgC1iHE,kCACE,2QhC4iHJ,CgCziHE,0BACE,ehC2iHJ,CMvzHE,gG0BiRM,UhC4iHR,CiCz0HA,MAKE,qBAEA,wBADA,qB7BHS,C6BKT,kCvBPE,oBNqO0B,C6BpO5B,aACA,sBACA,YAHA,iBjCo1HF,CiC10HE,SAEE,cADA,cjC60HJ,CiCx0HI,2DvBPA,6BN4N0B,CM3N1B,8BVk1HJ,CiCt0HI,yDvBEA,gCN6M0B,CM9M1B,iCVy0HJ,CiCp0HA,WAGE,cACA,ejCq0HF,CiCj0HA,YACE,oBjCo0HF,CiCj0HA,eACE,mBjCq0HF,CiCj0HA,qCAHE,ejCw0HF,CM32HE,iB2B4CE,oBjCm0HJ,CiCh0HE,sBACE,mBjCk0HJ,CiC1zHA,aAIE,0B7BivBkC,C6BhvBlC,yCAHA,gBADA,sBjCg0HF,CiC1zHE,yBvBtEE,uDVm4HJ,CiCxzHI,sDACE,YjC0zHN,CiCrzHA,aAEE,0B7BiuBkC,C6BhuBlC,sCAFA,sBjC0zHF,CiCtzHE,wBvBtFE,uDV+4HJ,CiC/yHA,kBAIE,gBAFA,qBjCozHF,CiC/yHA,qCAJE,qBAFA,qBjC0zHF,CiC9yHA,kBAIE,SACA,OACA,e7BssBkC,C6B3sBlC,kBAEA,QADA,KjCqzHF,CiC9yHA,UvBtHI,gCNmzBgC,C6B5rBlC,UjCkzHF,CiC7yHA,cvBnHI,yCN0yBgC,CMzyBhC,0CNyyBgC,C6BtrBlC,UjCkzHF,CiC9yHA,iBvBzGI,4CN2xBgC,CM5xBhC,6CN4xBgC,C6BjrBlC,UjCmzHF,CiC5yHA,WACE,aACA,qBjC+yHF,CiC7yHE,iBACE,kBjC+yHJ,Cct4HI,yBmBkFJ,WASI,mBAEA,kBADA,kBjCgzHF,CiC7yHE,iBACE,aAEA,YACA,sBAEA,gBACA,gB7B8pB8B,C6BhqB9B,iBjCgzHJ,CACF,CiCryHA,YACE,aACA,qBjCwyHF,CiCpyHE,kBACE,kBjCsyHJ,Cc75HI,yBmBgHJ,YAWI,kBjCsyHF,CiCnyHE,kBAEE,YACA,ejCoyHJ,CiClyHI,wBAEE,cADA,ajCqyHN,CiC/xHM,mCvBhLJ,4BuBiLmC,CvBlLnC,yBVo9HF,CiChyHQ,iGAGE,yBjCiyHV,CiC/xHQ,oGAGE,4BjCgyHV,CiC5xHM,oCvBjLJ,2BuBkLkC,CvBnLlC,wBVk9HF,CiC7xHQ,mGAGE,wBjC8xHV,CiC5xHQ,sGAGE,2BjC6xHV,CACF,CiChxHE,oBACE,oBjCmxHJ,Cct8HI,yBmBiLJ,cAMI,c7BglBgC,C6B/kBhC,kB7BglBgC,C6B/kBhC,UACA,QjCmxHF,CiCjxHE,oBACE,qBACA,UjCmxHJ,CACF,CiCzwHE,iBACE,ejC4wHJ,CiCzwHM,8DvBpQF,eVghIJ,CiCxwHM,wDACE,gBvBzQJ,eVohIJ,CiCtwHI,+BACE,gBvBvPF,2BuBwPgC,CvBzPhC,4BVkgIJ,CiCtwHI,8BvB1QA,wBuB2Q6B,CvB1Q7B,yBVmhIJ,CiCtwHI,8BACE,kBjCwwHN,CkCriIA,YAME,wB9BGS,CMJP,oBNqO0B,C8BzO5B,aACA,eAGA,gBADA,kB9B6gCkC,C8B9gClC,mBlC4iIF,CkCniIE,kCACE,kBlCsiIJ,CkCpiII,yCAGE,a9BLK,C8BML,W9BmgC8B,C8BtgC9B,qBACA,mBlCwiIN,CkC5hIE,+CACE,0BAIA,oBlC0hIJ,CkCvhIE,wBACE,alC4hIJ,CmClkIA,YzBKI,oBNqO0B,C+BzO5B,a5BIA,gBADA,cPqkIF,CmCnkIA,WAOE,qB/BNS,C+BOT,yBAFA,a/B+JwC,C+BnKxC,cAGA,gB/BwwBkC,C+BzwBlC,iBADA,qBAFA,iBnC6kIF,CmCpkIE,iBAIE,wB/BXO,C+BYP,oB/BXO,C+BQP,a/B2JsC,C+B1JtC,qBAFA,SnC0kIJ,CmCnkIE,iBAGE,gC/B2W0B,C+B5W1B,S/BiwBgC,C+BlwBhC,SnCukIJ,CmC/jII,kCzBGA,gCNsM0B,CMvM1B,6BNuM0B,C+BxMxB,anCokIN,CmC/jII,iCzBjBA,iCNoN0B,CMrN1B,8BVqlIJ,CmC9jIE,6BAGE,wB/BsM0B,C+BrM1B,oB/BqM0B,C+BvM1B,U/BvCO,C+BsCP,SnCmkIJ,CmC7jIE,+BAKE,qB/BjDO,C+BkDP,oB/B/CO,C+B0CP,a/BvCO,C+B0CP,YAFA,mBnCkkIJ,CoCpnIE,0B/B4HI,iBAtCa,C+BnFf,ehC8N0B,CgChO1B,qBpCynIJ,CoClnIM,iD1ByBF,+BNuM0B,CMxM1B,4BV8lIJ,CoCjnIM,gD1BMF,gCNqN0B,CMtN1B,6BVgnIJ,CoCloIE,0B/B4HI,iBAtCa,C+BnFf,ehC+N0B,CgCjO1B,oBpCuoIJ,CoChoIM,iD1ByBF,+BNwM0B,CMzM1B,4BV4mIJ,CoC/nIM,gD1BMF,gCNsN0B,CMvN1B,6BV8nIJ,CqC9oIA,O3BAI,oBNqO0B,CiCpO5B,qBhCkEE,cgC/DF,ejCmR4B,CiClR5B,cAHA,mBAIA,kBlBLI,6HkBSJ,CAFA,uBADA,kBrCopIF,CmBrpII,uCkBNJ,OlBOM,enBwpIJ,CACF,CMrpIE,4B+BGI,oBrCqpIN,CqChpIE,aACE,YrCmpIJ,CqC9oIA,YACE,kBACA,QrCipIF,CqC1oIA,Y3BlCI,mBNs5BgC,CiCl3BlC,iBjC+2BkC,CiCh3BlC,kBrC+oIF,CqCroIE,eChDA,wBlC6Ea,CkC9Eb,UtC2rIF,CM5qIE,4CgCTI,yBADA,UtC0rIN,CsCtrII,4CAGE,iCADA,StCwrIN,CqClpIE,iBChDA,wBlC6Ea,CkC9Eb,UtCwsIF,CMzrIE,gDgCTI,yBADA,UtCusIN,CsCnsII,gDAGE,iCADA,StCqsIN,CqC/pIE,eChDA,wBlC6Ea,CkC9Eb,UtCqtIF,CMtsIE,4CgCTI,yBADA,UtCotIN,CsChtII,4CAGE,iCADA,StCktIN,CqC5qIE,YChDA,wBlC6Ea,CkC9Eb,UtCkuIF,CMntIE,sCgCTI,yBADA,UtCiuIN,CsC7tII,sCAGE,iCADA,StC+tIN,CqCzrIE,eChDA,wBlC6Ea,CkC9Eb,atC+uIF,CMhuIE,4CgCTI,yBADA,atC8uIN,CsC1uII,4CAGE,iCADA,StC4uIN,CqCtsIE,cChDA,wBlC6Ea,CkC9Eb,UtC4vIF,CM7uIE,0CgCTI,yBADA,UtC2vIN,CsCvvII,0CAGE,iCADA,StCyvIN,CqCntIE,aChDA,wBlC6Ea,CkC9Eb,atCywIF,CM1vIE,wCgCTI,yBADA,atCwwIN,CsCpwII,wCAGE,iCADA,StCswIN,CqChuIE,YChDA,wBlC6Ea,CkC9Eb,UtCsxIF,CMvwIE,sCgCTI,yBADA,UtCqxIN,CsCjxII,sCAGE,iCADA,StCmxIN,CuC/xIA,WAIE,wBnCKS,CMJP,mBNsO0B,CmCzO5B,kBnC0yBkC,CmC3yBlC,iBvCqyIF,Cc1uII,yByB5DJ,WAQI,iBvCkyIF,CACF,CuC/xIA,iB7BPI,e6BUqB,CADvB,eADA,evCoyIF,CwC7yIA,OAIE,uB9BHE,oBNqO0B,CoCnO5B,kBpCm8BkC,CoCp8BlC,uBADA,iBxCozIF,CwC5yIA,eAEE,axC8yIF,CwC1yIA,YACE,exC6yIF,CwCryIA,mBACE,kBxCwyIF,CwCryIE,0BAKE,cADA,uBAHA,kBAEA,QADA,KxC0yIJ,CwC7xIE,enBzCE,wBmB0CuB,CC7CzB,oBD6CqE,CC/CrE,azCi1IF,CyC70IE,kBACE,wBzC+0IJ,CyC50IE,2BACE,azC80IJ,CwCzyIE,iBnBzCE,wBmB0CuB,CC7CzB,oBD6CqE,CC/CrE,azC61IF,CyCz1IE,oBACE,wBzC21IJ,CyCx1IE,6BACE,azC01IJ,CwCrzIE,enBzCE,wBmB0CuB,CC7CzB,oBD6CqE,CC/CrE,azCy2IF,CyCr2IE,kBACE,wBzCu2IJ,CyCp2IE,2BACE,azCs2IJ,CwCj0IE,YnBzCE,wBmB0CuB,CC7CzB,oBD6CqE,CC/CrE,azCq3IF,CyCj3IE,eACE,wBzCm3IJ,CyCh3IE,wBACE,azCk3IJ,CwC70IE,enBzCE,wBmB0CuB,CC7CzB,oBD6CqE,CC/CrE,azCi4IF,CyC73IE,kBACE,wBzC+3IJ,CyC53IE,2BACE,azC83IJ,CwCz1IE,cnBzCE,wBmB0CuB,CC7CzB,oBD6CqE,CC/CrE,azC64IF,CyCz4IE,iBACE,wBzC24IJ,CyCx4IE,0BACE,azC04IJ,CwCr2IE,anBzCE,wBmB0CuB,CC7CzB,oBD6CqE,CC/CrE,azCy5IF,CyCr5IE,gBACE,wBzCu5IJ,CyCp5IE,yBACE,azCs5IJ,CwCj3IE,YnBzCE,wBmB0CuB,CC7CzB,oBD6CqE,CC/CrE,azCq6IF,CyCj6IE,eACE,wBzCm6IJ,CyCh6IE,wBACE,azCk6IJ,C0C16IE,gCACE,GAAO,0B1C86IT,C0C76IE,GAAK,uB1Cg7IP,CACF,C0C76IA,UAKE,wBtCJS,CMJP,oBNqO0B,CsCjO5B,arCsHI,gBAtCa,CqC/EjB,WtC48BkC,CsC38BlC,e1Ck7IF,C0C36IA,cAOE,wBtCm8BkC,CsCt8BlC,UtCfS,CsCYT,aACA,sBACA,uBAEA,kBvBjBI,yBuBoBJ,CAFA,kB1Cg7IF,CmB77II,uCuBOJ,cvBNM,enBg8IJ,CACF,C0Ch7IA,sBrBcE,gHqBZA,yB1Cm7IF,C0C/6IE,uBACE,iD1Ck7IJ,C0Ch7II,uCAHF,uBAII,c1Cm7IJ,CACF,C2C39IA,OAEE,uBADA,Y3C+9IF,C2C39IA,YACE,M3C89IF,C4Ch+IA,YACE,aACA,sBAIA,gBADA,c5Ck+IF,C4Cx9IA,wBAEE,axCPS,CwCQT,mBAFA,U5C69IF,CMj+IE,4DsCaE,wBxCrBO,CwCmBP,axCbO,CwCcP,qBAFA,S5C69IJ,C4Cv9IE,+BAEE,wBxCzBO,CwCwBP,a5C09IJ,C4Ch9IA,iBAOE,qBxC3CS,CwC4CT,kCANA,cAGA,mBAFA,uBAFA,iB5Cw9IF,C4C/8IE,6BlCvCE,6BN4N0B,CM3N1B,8BVy/IJ,C4C/8IE,4BlC5BE,gCN6M0B,CM9M1B,iCN8M0B,CwChL1B,e5Cm9IJ,C4C/8IE,oDAIE,qBxC3DO,CwCyDP,axCnDO,CwCoDP,mB5Ci9IJ,C4C58IE,wBAGE,wBxC4K0B,CwC3K1B,oBxC2K0B,CwC7K1B,UxCjEO,CwCgEP,S5Ci9IJ,C4Cj8II,uBACE,kB5Co8IN,C4Cl8IM,wCAEE,gBADA,iB5Cq8IR,C4Cl8IQ,oDlC1DJ,gCNsM0B,CMvM1B,6BNuM0B,CM1L1B,yBVq/IJ,C4Cl8IQ,mDlCvCJ,2BkC0CyC,ClChFzC,iCNoN0B,CMrN1B,8BNqN0B,CwCtIpB,c5Cu8IV,Cc/+II,yB8B2BA,0BACE,kB5Cw9IJ,C4Ct9II,2CAEE,gBADA,iB5Cy9IN,C4Ct9IM,uDlC1DJ,gCNsM0B,CMvM1B,6BNuM0B,CM1L1B,yBVygJF,C4Ct9IM,sDlCvCJ,2BkC0CyC,ClChFzC,iCNoN0B,CMrN1B,8BNqN0B,CwCtIpB,c5C29IR,CACF,CcpgJI,yB8B2BA,0BACE,kB5C4+IJ,C4C1+II,2CAEE,gBADA,iB5C6+IN,C4C1+IM,uDlC1DJ,gCNsM0B,CMvM1B,6BNuM0B,CM1L1B,yBV6hJF,C4C1+IM,sDlCvCJ,2BkC0CyC,ClChFzC,iCNoN0B,CMrN1B,8BNqN0B,CwCtIpB,c5C++IR,CACF,CcxhJI,yB8B2BA,0BACE,kB5CggJJ,C4C9/II,2CAEE,gBADA,iB5CigJN,C4C9/IM,uDlC1DJ,gCNsM0B,CMvM1B,6BNuM0B,CM1L1B,yBVijJF,C4C9/IM,sDlCvCJ,2BkC0CyC,ClChFzC,iCNoN0B,CMrN1B,8BNqN0B,CwCtIpB,c5CmgJR,CACF,Cc5iJI,0B8B2BA,0BACE,kB5CohJJ,C4ClhJI,2CAEE,gBADA,iB5CqhJN,C4ClhJM,uDlC1DJ,gCNsM0B,CMvM1B,6BNuM0B,CM1L1B,yBVqkJF,C4ClhJM,sDlCvCJ,2BkC0CyC,ClChFzC,iCNoN0B,CMrN1B,8BNqN0B,CwCtIpB,c5CuhJR,CACF,C4CxgJE,mCAEE,clCjHA,ekCkHuB,CAFvB,c5C4gJJ,C4CxgJI,8CACE,kB5C0gJN,C4CrgJI,2DACE,Y5CugJN,C4ClgJI,yDAEE,gBADA,e5CqgJN,C6CxoJE,yBAEE,wBD8IuC,CC/IvC,a7C4oJJ,CMhoJE,4GuCNM,yBADA,a7C2oJR,C6CvoJM,uDAEE,wBDoIkE,CCnIlE,oBDmIkE,CCrIlE,U7C2oJR,C6CtpJE,2BAEE,wBD8IuC,CC/IvC,a7C0pJJ,CM9oJE,gHuCNM,yBADA,a7CypJR,C6CrpJM,yDAEE,wBDoIkE,CCnIlE,oBDmIkE,CCrIlE,U7CypJR,C6CpqJE,yBAEE,wBD8IuC,CC/IvC,a7CwqJJ,CM5pJE,4GuCNM,yBADA,a7CuqJR,C6CnqJM,uDAEE,wBDoIkE,CCnIlE,oBDmIkE,CCrIlE,U7CuqJR,C6ClrJE,sBAEE,wBD8IuC,CC/IvC,a7CsrJJ,CM1qJE,sGuCNM,yBADA,a7CqrJR,C6CjrJM,oDAEE,wBDoIkE,CCnIlE,oBDmIkE,CCrIlE,U7CqrJR,C6ChsJE,yBAEE,wBD8IuC,CC/IvC,a7CosJJ,CMxrJE,4GuCNM,yBADA,a7CmsJR,C6C/rJM,uDAEE,wBDoIkE,CCnIlE,oBDmIkE,CCrIlE,U7CmsJR,C6C9sJE,wBAEE,wBD8IuC,CC/IvC,a7CktJJ,CMtsJE,0GuCNM,yBADA,a7CitJR,C6C7sJM,sDAEE,wBDoIkE,CCnIlE,oBDmIkE,CCrIlE,U7CitJR,C6C5tJE,uBAEE,wBD8IuC,CC/IvC,a7CguJJ,CMptJE,wGuCNM,yBADA,a7C+tJR,C6C3tJM,qDAEE,wBDoIkE,CCnIlE,oBDmIkE,CCrIlE,U7C+tJR,C6C1uJE,sBAEE,wBD8IuC,CC/IvC,a7C8uJJ,CMluJE,sGuCNM,yBADA,a7C6uJR,C6CzuJM,oDAEE,wBDoIkE,CCnIlE,oBDmIkE,CCrIlE,U7C6uJR,C8C3vJA,OAKE,U1CYS,C0ChBT,YzC8HI,gBAtCa,CyCtFjB,e1CyR4B,C0CxR5B,cAGA,WADA,wB9C+vJF,CMzvJE,awCDE,U1CMO,C0CLP,oB9C6vJJ,CMzvJE,sFwCCI,W9C2vJN,C8ChvJA,aAIE,gBAFA,yBACA,SAFA,S9CsvJF,C8C7uJA,iBACE,mB9CgvJF,C+CvxJA,OASE,2BAHA,4BADA,0B3C63BkC,C2C33BlC,2BrCFE,oBNg4BgC,C2C73BlC,oC3C83BkC,CCvwB9B,iBAtCa,C0CxFjB,e3C43BkC,C2Cn3BlC,UARA,e/CkyJF,C+CvxJE,wBACE,oB/CyxJJ,C+CtxJE,eACE,S/CwxJJ,C+CrxJE,YACE,cACA,S/CuxJJ,C+CpxJE,YACE,Y/CsxJJ,C+ClxJA,cAEE,mBAIA,4BADA,0B3Cq2BkC,C2Cn2BlC,kCAHA,a3CtBS,C2CmBT,aAEA,qB/CyxJF,C+ClxJA,YACE,c/CqxJF,CgDzzJA,YAEE,ehD2zJF,CgDzzJE,mBACE,kBACA,ehD2zJJ,CgDtzJA,OAKE,aAEA,YAJA,OAQA,UAHA,gBAPA,eACA,MAIA,WAFA,YhD8zJF,CgDhzJA,cAGE,Y5C63BkC,C4C33BlC,oBAJA,kBACA,UhDqzJF,CgD/yJE,0BAEE,2B5Ck5BgC,Cez7B9B,iCnBw1JN,CmBn1JI,uC6BgCF,0B7B/BI,enBs1JJ,CACF,CgDpzJE,0BACE,chDszJJ,CgDlzJA,yBACE,aACA,4BhDqzJF,CgDnzJE,wCACE,8BACA,ehDqzJJ,CgDlzJE,8EAEE,ahDozJJ,CgDjzJE,qCACE,ehDmzJJ,CgD/yJA,uBAEE,mBADA,aAEA,4BhDkzJF,CgD/yJE,8BAGE,WAFA,cACA,yBhDkzJJ,CgD7yJE,+CACE,sBAEA,YADA,sBhDgzJJ,CgD7yJI,8DACE,ehD+yJN,CgD5yJI,sDACE,YhD8yJN,CgDxyJA,eASE,4BADA,qB5CrGS,C4CuGT,uBtCzGE,mBNsO0B,C4CrI5B,aACA,sBAWA,UAPA,oBANA,kBAGA,UhDizJF,CgDnyJA,gBAOE,qB5C5GS,C4C2GT,aAHA,OAFA,eACA,MAGA,YADA,YhDyyJF,CgDnyJE,qBAAS,ShDsyJX,CgDryJE,qBAAS,UhDwyJX,CgDnyJA,cAEE,uBAGA,gCtC7HE,4BN6N0B,CM5N1B,6BN4N0B,C4CpG5B,aAEA,8BACA,YhDyyJF,CgDryJE,qBAGE,8BAFA,YhDwyJJ,CgDjyJA,aAEE,e5CwI4B,C4CzI5B,ehDqyJF,CgD/xJA,YAIE,cACA,Y5C+vBkC,C4CnwBlC,iBhDoyJF,CgD5xJA,cAEE,mBtC3IE,+BN8M0B,CM/M1B,gCN+M0B,C4ChE5B,6BAJA,aAEA,yBACA,YhDkyJF,CgD7xJE,iCAAuB,kBhDgyJzB,CgD/xJE,gCAAsB,mBhDkyJxB,CgD9xJA,yBAIE,YACA,gBAJA,kBACA,YACA,UhDmyJF,Cc95JI,yBkCmIF,cAEE,oBADA,ehDgyJF,CgD5xJA,yBACE,8BhD8xJF,CgD5xJE,wCACE,+BhD8xJJ,CgD1xJA,uBACE,8BhD4xJF,CgD1xJE,8BACE,2BhD4xJJ,CgDpxJA,UAAY,ehDuxJZ,CACF,Ccp7JI,yBkCgKF,oBAEE,ehDuxJF,CACF,Cc17JI,0BkCuKF,UAAY,gBhDuxJZ,CACF,CiD1/JA,SAUE,qBAPA,cCHA,wK9C+Q4B,CCjJxB,iBAtCa,C6CtFjB,kBACA,e9CuR4B,C8ChR5B,sBAIA,gBAVA,e9C2R4B,C6C3R5B,Q7C60BkC,C6Ct0BlC,UAVA,kBCIA,gBACA,iBACA,qBACA,iBACA,oBAIA,mBAFA,kBACA,oBDVA,YjD+gKF,CiDpgKE,cAAS,UjDugKX,CiDrgKE,gBAEE,cAEA,Y7Ci0BgC,C6Cp0BhC,kBAEA,WjDwgKJ,CiDrgKI,uBAGE,mBACA,mBAFA,WADA,iBjD0gKN,CiDlgKA,mDACE,ejDqgKF,CiDngKE,iEACE,QjDqgKJ,CiDngKI,+EAGE,qB7CvBK,C6CsBL,2BADA,KjDugKN,CiDhgKA,uDACE,ejDmgKF,CiDjgKE,qEAGE,Y7CiyBgC,C6CnyBhC,OACA,WjDogKJ,CiDjgKI,mFAGE,uB7CvCK,C6CsCL,iCADA,OjDqgKN,CiD9/JA,yDACE,ejDigKF,CiD//JE,uEACE,KjDigKJ,CiD//JI,qFAGE,wB7CrDK,C6CoDL,2BADA,QjDmgKN,CiD5/JA,qDACE,ejD+/JF,CiD7/JE,mEAGE,Y7CmwBgC,C6CrwBhC,QACA,WjDggKJ,CiD7/JI,iFAGE,sB7CrEK,C6CoEL,iCADA,MjDigKN,CiD1+JA,eAKE,qB7C/FS,CMZP,oBNqO0B,C6C5H5B,U7CvGS,C6CqGT,e7C+tBkC,C6C9tBlC,qBAEA,iBjD++JF,CmD9lKA,SAYE,qBAEA,4BADA,qB/CNS,C+CQT,uBzCVE,mBNsO0B,C8C1O5B,wK9C+Q4B,CCjJxB,iBAtCa,C6CtFjB,kBACA,e9CuR4B,C+CxR5B,ODQA,sBAIA,gBAVA,e9C2R4B,C+C1R5B,e/C+1BkC,C8C/1BlC,gBACA,iBACA,qBACA,iBACA,oBCRA,MDYA,mBAFA,kBACA,oBCTA,YnDunKF,CmDxmKE,yBAdA,cAJA,iBnDioKF,CmD/mKE,gBAIE,Y/C81BgC,C+C71BhC,eAFA,UnD4mKJ,CmDxmKI,6CAKE,mBACA,mBAFA,WADA,cADA,iBnD6mKN,CmDpmKA,mDACE,mBnDumKF,CmDrmKE,iEACE,0BnDumKJ,CmDrmKI,+EAGE,0B/C00B8B,C+C30B9B,2BADA,QnDymKN,CmDpmKI,6EAGE,qB/C7CK,C+C4CL,2BADA,UnDwmKN,CmDjmKA,uDACE,iBnDomKF,CmDlmKE,qEAGE,W/CqzBgC,C+CvzBhC,yBAGA,eAFA,WnDsmKJ,CmDlmKI,mFAGE,4B/CmzB8B,C+CpzB9B,iCADA,MnDsmKN,CmDjmKI,iFAGE,uB/CpEK,C+CmEL,iCADA,QnDqmKN,CmD9lKA,yDACE,gBnDimKF,CmD/lKE,uEACE,uBnDimKJ,CmD/lKI,qFAGE,6B/C+xB8B,C+ChyB9B,2BADA,KnDmmKN,CmD9lKI,mFAGE,wB/CxFK,C+CuFL,2BADA,OnDkmKN,CmD3lKE,uGAQE,gCADA,WAHA,cADA,SAGA,mBALA,kBACA,MAGA,UnDgmKJ,CmDzlKA,qDACE,kBnD4lKF,CmD1lKE,mEAGE,W/C8vBgC,C+C7vBhC,eAHA,0BACA,WnD8lKJ,CmD1lKI,iFAGE,2B/C4vB8B,C+C7vB9B,iCADA,OnD8lKN,CmDzlKI,+EAGE,sB/C3HK,C+C0HL,iCADA,SnD6lKN,CmDrkKA,gBAKE,wB/C8sBkC,C+C7sBlC,gCzChJE,wCyCiJoB,CzChJpB,yCyCgJoB,C9ChClB,cAtCa,C8CiEjB,gBADA,oBnD8kKF,CmDrkKE,sBACE,YnDukKJ,CmDnkKA,cAEE,a/CzJS,C+CwJT,oBnDukKF,CoDluKA,UACE,iBpDquKF,CoDluKA,wBACE,kBpDquKF,CoDluKA,gBAGE,gBAFA,kBACA,UpDsuKF,CqD5vKE,sBAEE,WACA,WAFA,arDgwKJ,CoDtuKA,eAME,2BAJA,aACA,WAEA,mBAJA,kBjCvBI,oCiC6BJ,CAHA,UpD4uKF,CmBjwKI,uCiCiBJ,ejChBM,enBowKJ,CACF,CoD3uKA,8DAGE,apD8uKF,CoD3uKA,yEAEE,0BpD8uKF,CoD3uKA,yEAEE,2BpD8uKF,CoDruKE,8BACE,UAEA,eADA,2BpDyuKJ,CoDruKE,kJAIE,UADA,SpDwuKJ,CoDpuKE,qFAGE,UjCtEE,yBiCuEF,CAFA,SpDwuKJ,CmBxyKI,uCiC8DF,qFjC7DI,enB4yKJ,CACF,CoDnuKA,8CAQE,mBAJA,SAOA,UhD1FS,CgDsFT,aAEA,uBAIA,UhD47BmC,CgDv8BnC,kBAUA,kBATA,MjCnFI,4BiC8FJ,CAJA,ShD87BmC,CgDn8BnC,SpD8uKF,CmB9zKI,uCiC2EJ,8CjC1EM,enBk0KJ,CACF,CM/zKE,oH8CwFE,UhDjGO,CgDoGP,UhDq7BiC,CgDt7BjC,UADA,oBpD8uKJ,CoDzuKA,uBACE,MpD4uKF,CoDvuKA,uBACE,OpD0uKF,CoDnuKA,wDAKE,mCAHA,qBAEA,WhD66BmC,CgD96BnC,UpDwuKF,CoDpuKA,4BACE,6MpDuuKF,CoDruKA,4BACE,8MpDwuKF,CoD/tKA,qBAGE,SAGA,aACA,uBAHA,OAQA,gBADA,ehDm4BmC,CgDp4BnC,gBhDo4BmC,CgDt4BnC,eAPA,kBACA,QAGA,UpDwuKF,CoD/tKE,wBAUE,4BADA,qBhDhKO,CgDoKP,+BADA,4BAXA,mBAOA,eANA,cAEA,UhDk4BiC,CgDh4BjC,ehDk4BiC,CgDn4BjC,gBhDm4BiC,CgD13BjC,WAPA,mBjC/JE,2BiCuKF,CAZA,UpD4uKJ,CmBl4KI,uCiCmJF,wBjClJI,enBq4KJ,CACF,CoDluKE,6BACE,SpDouKJ,CoD3tKA,kBAGE,YAKA,UhD3LS,CgDuLT,SAGA,oBADA,iBALA,kBACA,UAOA,kBAJA,UpDkuKF,CsD75KA,0BACE,GAAK,uBtDi6KL,CACF,CsD/5KA,gBASE,8CAHA,mBAEA,kBAFA,+BALA,qBAEA,WlD6iCsB,CkD5iCtB,2BAFA,UtDu6KF,CsD75KA,mBAGE,iBlDuiCwB,CkDxiCxB,WlDsiCwB,CkDviCxB,UtDk6KF,CsDz5KA,wBACE,GACE,kBtD45KF,CsD15KA,IACE,StD45KF,CACF,CsDz5KA,cASE,4CAJA,8BAEA,kBANA,qBAEA,WlD8gCsB,CkDzgCtB,UAJA,2BAFA,UtDi6KF,CsDv5KA,iBAEE,WlDugCwB,CkDxgCxB,UtD25KF,CuD78KA,gBAAqB,gCvDi9KrB,CuDh9KA,WAAqB,4BvDo9KrB,CuDn9KA,cAAqB,+BvDu9KrB,CuDt9KA,cAAqB,+BvD09KrB,CuDz9KA,mBAAqB,oCvD69KrB,CuD59KA,gBAAqB,iCvDg+KrB,CwDl+KE,YACE,kCxDq+KJ,CM39KE,sFkDLI,kCxDs+KN,CwD5+KE,cACE,kCxD++KJ,CMr+KE,8FkDLI,kCxDg/KN,CwDt/KE,YACE,kCxDy/KJ,CM/+KE,sFkDLI,kCxD0/KN,CwDhgLE,SACE,kCxDmgLJ,CMz/KE,0EkDLI,kCxDogLN,CwD1gLE,YACE,kCxD6gLJ,CMngLE,sFkDLI,kCxD8gLN,CwDphLE,WACE,kCxDuhLJ,CM7gLE,kFkDLI,kCxDwhLN,CwD9hLE,UACE,kCxDiiLJ,CMvhLE,8EkDLI,kCxDkiLN,CwDxiLE,SACE,kCxD2iLJ,CMjiLE,0EkDLI,kCxD4iLN,CyD3iLA,UACE,+BzD8iLF,CyD3iLA,gBACE,kCzD8iLF,C0DzjLA,QAAkB,kC1D6jLlB,C0D5jLA,YAAkB,sC1DgkLlB,C0D/jLA,cAAkB,wC1DmkLlB,C0DlkLA,eAAkB,yC1DskLlB,C0DrkLA,aAAkB,uC1DykLlB,C0DvkLA,UAAmB,kB1D2kLnB,C0D1kLA,cAAmB,sB1D8kLnB,C0D7kLA,gBAAmB,wB1DilLnB,C0DhlLA,iBAAmB,yB1DolLnB,C0DnlLA,eAAmB,uB1DulLnB,C0DplLE,gBACE,8B1DulLJ,C0DxlLE,kBACE,8B1D2lLJ,C0D5lLE,gBACE,8B1D+lLJ,C0DhmLE,aACE,8B1DmmLJ,C0DpmLE,gBACE,8B1DumLJ,C0DxmLE,eACE,8B1D2mLJ,C0D5mLE,cACE,8B1D+mLJ,C0DhnLE,aACE,8B1DmnLJ,C0D/mLA,cACE,2B1DknLF,C0D3mLA,YACE,6B1D8mLF,C0D3mLA,SACE,8B1D8mLF,C0D3mLA,aACE,uC1D+mLF,C0D3mLA,4BAHE,wC1DmnLF,C0D3mLA,+BAHE,2C1DmnLF,C0D3mLA,8BAHE,0C1DmnLF,C0DhnLA,cACE,uC1D+mLF,C0D3mLA,YACE,6B1D8mLF,C0D3mLA,gBACE,2B1D8mLF,C0D3mLA,cACE,6B1D8mLF,C0D3mLA,WACE,yB1D8mLF,CqDtrLE,gBAEE,WACA,WAFA,arD2rLJ,C2DlrLM,QAAwB,sB3DsrL9B,C2DtrLM,UAAwB,wB3D0rL9B,C2D1rLM,gBAAwB,8B3D8rL9B,C2D9rLM,SAAwB,uB3DksL9B,C2DlsLM,SAAwB,uB3DssL9B,C2DtsLM,aAAwB,2B3D0sL9B,C2D1sLM,cAAwB,4B3D8sL9B,C2D9sLM,QAAwB,sB3DktL9B,C2DltLM,eAAwB,6B3DstL9B,CcrqLI,yB6CjDE,WAAwB,sB3D2tL5B,C2D3tLI,aAAwB,wB3D8tL5B,C2D9tLI,mBAAwB,8B3DiuL5B,C2DjuLI,YAAwB,uB3DouL5B,C2DpuLI,YAAwB,uB3DuuL5B,C2DvuLI,gBAAwB,2B3D0uL5B,C2D1uLI,iBAAwB,4B3D6uL5B,C2D7uLI,WAAwB,sB3DgvL5B,C2DhvLI,kBAAwB,6B3DmvL5B,CACF,CcnsLI,yB6CjDE,WAAwB,sB3DwvL5B,C2DxvLI,aAAwB,wB3D2vL5B,C2D3vLI,mBAAwB,8B3D8vL5B,C2D9vLI,YAAwB,uB3DiwL5B,C2DjwLI,YAAwB,uB3DowL5B,C2DpwLI,gBAAwB,2B3DuwL5B,C2DvwLI,iBAAwB,4B3D0wL5B,C2D1wLI,WAAwB,sB3D6wL5B,C2D7wLI,kBAAwB,6B3DgxL5B,CACF,CchuLI,yB6CjDE,WAAwB,sB3DqxL5B,C2DrxLI,aAAwB,wB3DwxL5B,C2DxxLI,mBAAwB,8B3D2xL5B,C2D3xLI,YAAwB,uB3D8xL5B,C2D9xLI,YAAwB,uB3DiyL5B,C2DjyLI,gBAAwB,2B3DoyL5B,C2DpyLI,iBAAwB,4B3DuyL5B,C2DvyLI,WAAwB,sB3D0yL5B,C2D1yLI,kBAAwB,6B3D6yL5B,CACF,Cc7vLI,0B6CjDE,WAAwB,sB3DkzL5B,C2DlzLI,aAAwB,wB3DqzL5B,C2DrzLI,mBAAwB,8B3DwzL5B,C2DxzLI,YAAwB,uB3D2zL5B,C2D3zLI,YAAwB,uB3D8zL5B,C2D9zLI,gBAAwB,2B3Di0L5B,C2Dj0LI,iBAAwB,4B3Do0L5B,C2Dp0LI,WAAwB,sB3Du0L5B,C2Dv0LI,kBAAwB,6B3D00L5B,CACF,C2Dj0LA,aAEI,cAAqB,sB3Dm0LvB,C2Dn0LE,gBAAqB,wB3Ds0LvB,C2Dt0LE,sBAAqB,8B3Dy0LvB,C2Dz0LE,eAAqB,uB3D40LvB,C2D50LE,eAAqB,uB3D+0LvB,C2D/0LE,mBAAqB,2B3Dk1LvB,C2Dl1LE,oBAAqB,4B3Dq1LvB,C2Dr1LE,cAAqB,sB3Dw1LvB,C2Dx1LE,qBAAqB,6B3D21LvB,CACF,C4Dj3LA,kBAEE,cAGA,gBADA,UAHA,kBAEA,U5Dq3LF,C4Dj3LE,yBAEE,WADA,a5Do3LJ,C4Dh3LE,2IAWE,SAJA,SAGA,YAFA,OAHA,kBACA,MAGA,U5Do3LJ,C4Dz2LI,+BACE,0B5D42LN,C4D72LI,+BACE,kB5Dg3LN,C4Dj3LI,8BACE,e5Do3LN,C4Dr3LI,8BACE,gB5Dw3LN,C6Dj5LI,UAAgC,4B7Dq5LpC,C6Dp5LI,aAAgC,+B7Dw5LpC,C6Dv5LI,kBAAgC,oC7D25LpC,C6D15LI,qBAAgC,uC7D85LpC,C6D55LI,WAA8B,wB7Dg6LlC,C6D/5LI,aAA8B,0B7Dm6LlC,C6Dl6LI,mBAA8B,gC7Ds6LlC,C6Dr6LI,WAA8B,uB7Dy6LlC,C6Dx6LI,aAA8B,qB7D46LlC,C6D36LI,aAA8B,qB7D+6LlC,C6D96LI,eAA8B,uB7Dk7LlC,C6Dj7LI,eAA8B,uB7Dq7LlC,C6Dn7LI,uBAAoC,oC7Du7LxC,C6Dt7LI,qBAAoC,kC7D07LxC,C6Dz7LI,wBAAoC,gC7D67LxC,C6D57LI,yBAAoC,uC7Dg8LxC,C6D/7LI,wBAAoC,sC7Dm8LxC,C6Dj8LI,mBAAiC,gC7Dq8LrC,C6Dp8LI,iBAAiC,8B7Dw8LrC,C6Dv8LI,oBAAiC,4B7D28LrC,C6D18LI,sBAAiC,8B7D88LrC,C6D78LI,qBAAiC,6B7Di9LrC,C6D/8LI,qBAAkC,kC7Dm9LtC,C6Dl9LI,mBAAkC,gC7Ds9LtC,C6Dr9LI,sBAAkC,8B7Dy9LtC,C6Dx9LI,uBAAkC,qC7D49LtC,C6D39LI,sBAAkC,oC7D+9LtC,C6D99LI,uBAAkC,+B7Dk+LtC,C6Dh+LI,iBAAgC,yB7Do+LpC,C6Dn+LI,kBAAgC,+B7Du+LpC,C6Dt+LI,gBAAgC,6B7D0+LpC,C6Dz+LI,mBAAgC,2B7D6+LpC,C6D5+LI,qBAAgC,6B7Dg/LpC,C6D/+LI,oBAAgC,4B7Dm/LpC,Ccv+LI,yB+ClDA,aAAgC,4B7D8hMlC,C6D7hME,gBAAgC,+B7DgiMlC,C6D/hME,qBAAgC,oC7DkiMlC,C6DjiME,wBAAgC,uC7DoiMlC,C6DliME,cAA8B,wB7DqiMhC,C6DpiME,gBAA8B,0B7DuiMhC,C6DtiME,sBAA8B,gC7DyiMhC,C6DxiME,cAA8B,uB7D2iMhC,C6D1iME,gBAA8B,qB7D6iMhC,C6D5iME,gBAA8B,qB7D+iMhC,C6D9iME,kBAA8B,uB7DijMhC,C6DhjME,kBAA8B,uB7DmjMhC,C6DjjME,0BAAoC,oC7DojMtC,C6DnjME,wBAAoC,kC7DsjMtC,C6DrjME,2BAAoC,gC7DwjMtC,C6DvjME,4BAAoC,uC7D0jMtC,C6DzjME,2BAAoC,sC7D4jMtC,C6D1jME,sBAAiC,gC7D6jMnC,C6D5jME,oBAAiC,8B7D+jMnC,C6D9jME,uBAAiC,4B7DikMnC,C6DhkME,yBAAiC,8B7DmkMnC,C6DlkME,wBAAiC,6B7DqkMnC,C6DnkME,wBAAkC,kC7DskMpC,C6DrkME,sBAAkC,gC7DwkMpC,C6DvkME,yBAAkC,8B7D0kMpC,C6DzkME,0BAAkC,qC7D4kMpC,C6D3kME,yBAAkC,oC7D8kMpC,C6D7kME,0BAAkC,+B7DglMpC,C6D9kME,oBAAgC,yB7DilMlC,C6DhlME,qBAAgC,+B7DmlMlC,C6DllME,mBAAgC,6B7DqlMlC,C6DplME,sBAAgC,2B7DulMlC,C6DtlME,wBAAgC,6B7DylMlC,C6DxlME,uBAAgC,4B7D2lMlC,CACF,CchlMI,yB+ClDA,aAAgC,4B7DsoMlC,C6DroME,gBAAgC,+B7DwoMlC,C6DvoME,qBAAgC,oC7D0oMlC,C6DzoME,wBAAgC,uC7D4oMlC,C6D1oME,cAA8B,wB7D6oMhC,C6D5oME,gBAA8B,0B7D+oMhC,C6D9oME,sBAA8B,gC7DipMhC,C6DhpME,cAA8B,uB7DmpMhC,C6DlpME,gBAA8B,qB7DqpMhC,C6DppME,gBAA8B,qB7DupMhC,C6DtpME,kBAA8B,uB7DypMhC,C6DxpME,kBAA8B,uB7D2pMhC,C6DzpME,0BAAoC,oC7D4pMtC,C6D3pME,wBAAoC,kC7D8pMtC,C6D7pME,2BAAoC,gC7DgqMtC,C6D/pME,4BAAoC,uC7DkqMtC,C6DjqME,2BAAoC,sC7DoqMtC,C6DlqME,sBAAiC,gC7DqqMnC,C6DpqME,oBAAiC,8B7DuqMnC,C6DtqME,uBAAiC,4B7DyqMnC,C6DxqME,yBAAiC,8B7D2qMnC,C6D1qME,wBAAiC,6B7D6qMnC,C6D3qME,wBAAkC,kC7D8qMpC,C6D7qME,sBAAkC,gC7DgrMpC,C6D/qME,yBAAkC,8B7DkrMpC,C6DjrME,0BAAkC,qC7DorMpC,C6DnrME,yBAAkC,oC7DsrMpC,C6DrrME,0BAAkC,+B7DwrMpC,C6DtrME,oBAAgC,yB7DyrMlC,C6DxrME,qBAAgC,+B7D2rMlC,C6D1rME,mBAAgC,6B7D6rMlC,C6D5rME,sBAAgC,2B7D+rMlC,C6D9rME,wBAAgC,6B7DisMlC,C6DhsME,uBAAgC,4B7DmsMlC,CACF,CcxrMI,yB+ClDA,aAAgC,4B7D8uMlC,C6D7uME,gBAAgC,+B7DgvMlC,C6D/uME,qBAAgC,oC7DkvMlC,C6DjvME,wBAAgC,uC7DovMlC,C6DlvME,cAA8B,wB7DqvMhC,C6DpvME,gBAA8B,0B7DuvMhC,C6DtvME,sBAA8B,gC7DyvMhC,C6DxvME,cAA8B,uB7D2vMhC,C6D1vME,gBAA8B,qB7D6vMhC,C6D5vME,gBAA8B,qB7D+vMhC,C6D9vME,kBAA8B,uB7DiwMhC,C6DhwME,kBAA8B,uB7DmwMhC,C6DjwME,0BAAoC,oC7DowMtC,C6DnwME,wBAAoC,kC7DswMtC,C6DrwME,2BAAoC,gC7DwwMtC,C6DvwME,4BAAoC,uC7D0wMtC,C6DzwME,2BAAoC,sC7D4wMtC,C6D1wME,sBAAiC,gC7D6wMnC,C6D5wME,oBAAiC,8B7D+wMnC,C6D9wME,uBAAiC,4B7DixMnC,C6DhxME,yBAAiC,8B7DmxMnC,C6DlxME,wBAAiC,6B7DqxMnC,C6DnxME,wBAAkC,kC7DsxMpC,C6DrxME,sBAAkC,gC7DwxMpC,C6DvxME,yBAAkC,8B7D0xMpC,C6DzxME,0BAAkC,qC7D4xMpC,C6D3xME,yBAAkC,oC7D8xMpC,C6D7xME,0BAAkC,+B7DgyMpC,C6D9xME,oBAAgC,yB7DiyMlC,C6DhyME,qBAAgC,+B7DmyMlC,C6DlyME,mBAAgC,6B7DqyMlC,C6DpyME,sBAAgC,2B7DuyMlC,C6DtyME,wBAAgC,6B7DyyMlC,C6DxyME,uBAAgC,4B7D2yMlC,CACF,CchyMI,0B+ClDA,aAAgC,4B7Ds1MlC,C6Dr1ME,gBAAgC,+B7Dw1MlC,C6Dv1ME,qBAAgC,oC7D01MlC,C6Dz1ME,wBAAgC,uC7D41MlC,C6D11ME,cAA8B,wB7D61MhC,C6D51ME,gBAA8B,0B7D+1MhC,C6D91ME,sBAA8B,gC7Di2MhC,C6Dh2ME,cAA8B,uB7Dm2MhC,C6Dl2ME,gBAA8B,qB7Dq2MhC,C6Dp2ME,gBAA8B,qB7Du2MhC,C6Dt2ME,kBAA8B,uB7Dy2MhC,C6Dx2ME,kBAA8B,uB7D22MhC,C6Dz2ME,0BAAoC,oC7D42MtC,C6D32ME,wBAAoC,kC7D82MtC,C6D72ME,2BAAoC,gC7Dg3MtC,C6D/2ME,4BAAoC,uC7Dk3MtC,C6Dj3ME,2BAAoC,sC7Do3MtC,C6Dl3ME,sBAAiC,gC7Dq3MnC,C6Dp3ME,oBAAiC,8B7Du3MnC,C6Dt3ME,uBAAiC,4B7Dy3MnC,C6Dx3ME,yBAAiC,8B7D23MnC,C6D13ME,wBAAiC,6B7D63MnC,C6D33ME,wBAAkC,kC7D83MpC,C6D73ME,sBAAkC,gC7Dg4MpC,C6D/3ME,yBAAkC,8B7Dk4MpC,C6Dj4ME,0BAAkC,qC7Do4MpC,C6Dn4ME,yBAAkC,oC7Ds4MpC,C6Dr4ME,0BAAkC,+B7Dw4MpC,C6Dt4ME,oBAAgC,yB7Dy4MlC,C6Dx4ME,qBAAgC,+B7D24MlC,C6D14ME,mBAAgC,6B7D64MlC,C6D54ME,sBAAgC,2B7D+4MlC,C6D94ME,wBAAgC,6B7Di5MlC,C6Dh5ME,uBAAgC,4B7Dm5MlC,CACF,C8D97MI,YAAwB,oB9Di8M5B,C8Dh8MI,aAAwB,qB9Do8M5B,C8Dn8MI,YAAwB,oB9Du8M5B,Ccn5MI,yBgDtDA,eAAwB,oB9D88M1B,C8D78ME,gBAAwB,qB9Dg9M1B,C8D/8ME,eAAwB,oB9Dk9M1B,CACF,Cc/5MI,yBgDtDA,eAAwB,oB9Dy9M1B,C8Dx9ME,gBAAwB,qB9D29M1B,C8D19ME,eAAwB,oB9D69M1B,CACF,Cc16MI,yBgDtDA,eAAwB,oB9Do+M1B,C8Dn+ME,gBAAwB,qB9Ds+M1B,C8Dr+ME,eAAwB,oB9Dw+M1B,CACF,Ccr7MI,0BgDtDA,eAAwB,oB9D++M1B,C8D9+ME,gBAAwB,qB9Di/M1B,C8Dh/ME,eAAwB,oB9Dm/M1B,CACF,C+Dz/ME,eAAsB,uB/D4/MxB,C+D5/ME,iBAAsB,yB/DggNxB,CgE//ME,iBAAyB,yBhEmgN3B,CgEngNE,mBAAyB,2BhEugN3B,CgEvgNE,mBAAyB,2BhE2gN3B,CgE3gNE,gBAAyB,wBhE+gN3B,CgE/gNE,iBAAyB,yBhEmhN3B,CgE9gNA,WAEE,KhEohNF,CgE9gNA,yBAJE,OAHA,eAEA,QAEA,YhEyhNF,CgEthNA,cAGE,QhEmhNF,CgE7gNE,4BADF,YAEI,gBACA,MACA,YhEihNF,CACF,CiE3iNA,SCOE,mBAEA,SALA,WAEA,gBADA,UAHA,kBAMA,mBALA,SlEmjNF,CkEniNE,mDAME,UAFA,YACA,iBAHA,gBAKA,mBAJA,UlEyiNJ,CmEjkNA,WAAa,sDnEqkNb,CmEpkNA,QAAU,2CnEwkNV,CmEvkNA,WAAa,iDnE2kNb,CmE1kNA,aAAe,yBnE8kNf,CoE7kNI,MAAuB,mBpEilN3B,CoEjlNI,MAAuB,mBpEqlN3B,CoErlNI,MAAuB,mBpEylN3B,CoEzlNI,OAAuB,oBpE6lN3B,CoE7lNI,QAAuB,oBpEimN3B,CoEjmNI,MAAuB,oBpEqmN3B,CoErmNI,MAAuB,oBpEymN3B,CoEzmNI,MAAuB,oBpE6mN3B,CoE7mNI,OAAuB,qBpEinN3B,CoEjnNI,QAAuB,qBpEqnN3B,CoEjnNA,QAAU,wBpEqnNV,CoEpnNA,QAAU,yBpEwnNV,CoEpnNA,YAAc,yBpEwnNd,CoEvnNA,YAAc,0BpE2nNd,CoEznNA,QAAU,qBpE6nNV,CoE5nNA,QAAU,sBpEgoNV,CqE9oNE,sBAWE,uBAPA,SAKA,WAJA,OAGA,oBAPA,kBAEA,QADA,MAIA,SrEopNJ,CsErpNQ,KAAgC,kBtEypNxC,CsExpNQ,YAEE,sBtE2pNV,CsEzpNQ,YAEE,wBtE4pNV,CsE1pNQ,YAEE,yBtE6pNV,CsE3pNQ,YAEE,uBtE8pNV,CsE7qNQ,KAAgC,uBtEirNxC,CsEhrNQ,YAEE,2BtEmrNV,CsEjrNQ,YAEE,6BtEorNV,CsElrNQ,YAEE,8BtEqrNV,CsEnrNQ,YAEE,4BtEsrNV,CsErsNQ,KAAgC,sBtEysNxC,CsExsNQ,YAEE,0BtE2sNV,CsEzsNQ,YAEE,4BtE4sNV,CsE1sNQ,YAEE,6BtE6sNV,CsE3sNQ,YAEE,2BtE8sNV,CsE7tNQ,KAAgC,qBtEiuNxC,CsEhuNQ,YAEE,yBtEmuNV,CsEjuNQ,YAEE,2BtEouNV,CsEluNQ,YAEE,4BtEquNV,CsEnuNQ,YAEE,0BtEsuNV,CsErvNQ,KAAgC,uBtEyvNxC,CsExvNQ,YAEE,2BtE2vNV,CsEzvNQ,YAEE,6BtE4vNV,CsE1vNQ,YAEE,8BtE6vNV,CsE3vNQ,YAEE,4BtE8vNV,CsE7wNQ,KAAgC,qBtEixNxC,CsEhxNQ,YAEE,yBtEmxNV,CsEjxNQ,YAEE,2BtEoxNV,CsElxNQ,YAEE,4BtEqxNV,CsEnxNQ,YAEE,0BtEsxNV,CsEryNQ,KAAgC,mBtEyyNxC,CsExyNQ,YAEE,uBtE2yNV,CsEzyNQ,YAEE,yBtE4yNV,CsE1yNQ,YAEE,0BtE6yNV,CsE3yNQ,YAEE,wBtE8yNV,CsE7zNQ,KAAgC,wBtEi0NxC,CsEh0NQ,YAEE,4BtEm0NV,CsEj0NQ,YAEE,8BtEo0NV,CsEl0NQ,YAEE,+BtEq0NV,CsEn0NQ,YAEE,6BtEs0NV,CsEr1NQ,KAAgC,uBtEy1NxC,CsEx1NQ,YAEE,2BtE21NV,CsEz1NQ,YAEE,6BtE41NV,CsE11NQ,YAEE,8BtE61NV,CsE31NQ,YAEE,4BtE81NV,CsE72NQ,KAAgC,sBtEi3NxC,CsEh3NQ,YAEE,0BtEm3NV,CsEj3NQ,YAEE,4BtEo3NV,CsEl3NQ,YAEE,6BtEq3NV,CsEn3NQ,YAEE,2BtEs3NV,CsEr4NQ,KAAgC,wBtEy4NxC,CsEx4NQ,YAEE,4BtE24NV,CsEz4NQ,YAEE,8BtE44NV,CsE14NQ,YAEE,+BtE64NV,CsE34NQ,YAEE,6BtE84NV,CsE75NQ,KAAgC,sBtEi6NxC,CsEh6NQ,YAEE,0BtEm6NV,CsEj6NQ,YAEE,4BtEo6NV,CsEl6NQ,YAEE,6BtEq6NV,CsEn6NQ,YAEE,2BtEs6NV,CsE95NQ,MAAwB,wBtEk6NhC,CsEj6NQ,cAEE,4BtEo6NV,CsEl6NQ,cAEE,8BtEq6NV,CsEn6NQ,cAEE,+BtEs6NV,CsEp6NQ,cAEE,6BtEu6NV,CsEt7NQ,MAAwB,uBtE07NhC,CsEz7NQ,cAEE,2BtE47NV,CsE17NQ,cAEE,6BtE67NV,CsE37NQ,cAEE,8BtE87NV,CsE57NQ,cAEE,4BtE+7NV,CsE98NQ,MAAwB,sBtEk9NhC,CsEj9NQ,cAEE,0BtEo9NV,CsEl9NQ,cAEE,4BtEq9NV,CsEn9NQ,cAEE,6BtEs9NV,CsEp9NQ,cAEE,2BtEu9NV,CsEt+NQ,MAAwB,wBtE0+NhC,CsEz+NQ,cAEE,4BtE4+NV,CsE1+NQ,cAEE,8BtE6+NV,CsE3+NQ,cAEE,+BtE8+NV,CsE5+NQ,cAEE,6BtE++NV,CsE9/NQ,MAAwB,sBtEkgOhC,CsEjgOQ,cAEE,0BtEogOV,CsElgOQ,cAEE,4BtEqgOV,CsEngOQ,cAEE,6BtEsgOV,CsEpgOQ,cAEE,2BtEugOV,CsEjgOI,QAAmB,qBtEqgOvB,CsEpgOI,kBAEE,yBtEugON,CsErgOI,kBAEE,2BtEwgON,CsEtgOI,kBAEE,4BtEygON,CsEvgOI,kBAEE,0BtE0gON,CcnhOI,yBwDlDI,QAAgC,kBtE0kOtC,CsEzkOM,kBAEE,sBtE2kOR,CsEzkOM,kBAEE,wBtE2kOR,CsEzkOM,kBAEE,yBtE2kOR,CsEzkOM,kBAEE,uBtE2kOR,CsE1lOM,QAAgC,uBtE6lOtC,CsE5lOM,kBAEE,2BtE8lOR,CsE5lOM,kBAEE,6BtE8lOR,CsE5lOM,kBAEE,8BtE8lOR,CsE5lOM,kBAEE,4BtE8lOR,CsE7mOM,QAAgC,sBtEgnOtC,CsE/mOM,kBAEE,0BtEinOR,CsE/mOM,kBAEE,4BtEinOR,CsE/mOM,kBAEE,6BtEinOR,CsE/mOM,kBAEE,2BtEinOR,CsEhoOM,QAAgC,qBtEmoOtC,CsEloOM,kBAEE,yBtEooOR,CsEloOM,kBAEE,2BtEooOR,CsEloOM,kBAEE,4BtEooOR,CsEloOM,kBAEE,0BtEooOR,CsEnpOM,QAAgC,uBtEspOtC,CsErpOM,kBAEE,2BtEupOR,CsErpOM,kBAEE,6BtEupOR,CsErpOM,kBAEE,8BtEupOR,CsErpOM,kBAEE,4BtEupOR,CsEtqOM,QAAgC,qBtEyqOtC,CsExqOM,kBAEE,yBtE0qOR,CsExqOM,kBAEE,2BtE0qOR,CsExqOM,kBAEE,4BtE0qOR,CsExqOM,kBAEE,0BtE0qOR,CsEzrOM,QAAgC,mBtE4rOtC,CsE3rOM,kBAEE,uBtE6rOR,CsE3rOM,kBAEE,yBtE6rOR,CsE3rOM,kBAEE,0BtE6rOR,CsE3rOM,kBAEE,wBtE6rOR,CsE5sOM,QAAgC,wBtE+sOtC,CsE9sOM,kBAEE,4BtEgtOR,CsE9sOM,kBAEE,8BtEgtOR,CsE9sOM,kBAEE,+BtEgtOR,CsE9sOM,kBAEE,6BtEgtOR,CsE/tOM,QAAgC,uBtEkuOtC,CsEjuOM,kBAEE,2BtEmuOR,CsEjuOM,kBAEE,6BtEmuOR,CsEjuOM,kBAEE,8BtEmuOR,CsEjuOM,kBAEE,4BtEmuOR,CsElvOM,QAAgC,sBtEqvOtC,CsEpvOM,kBAEE,0BtEsvOR,CsEpvOM,kBAEE,4BtEsvOR,CsEpvOM,kBAEE,6BtEsvOR,CsEpvOM,kBAEE,2BtEsvOR,CsErwOM,QAAgC,wBtEwwOtC,CsEvwOM,kBAEE,4BtEywOR,CsEvwOM,kBAEE,8BtEywOR,CsEvwOM,kBAEE,+BtEywOR,CsEvwOM,kBAEE,6BtEywOR,CsExxOM,QAAgC,sBtE2xOtC,CsE1xOM,kBAEE,0BtE4xOR,CsE1xOM,kBAEE,4BtE4xOR,CsE1xOM,kBAEE,6BtE4xOR,CsE1xOM,kBAEE,2BtE4xOR,CsEpxOM,SAAwB,wBtEuxO9B,CsEtxOM,oBAEE,4BtEwxOR,CsEtxOM,oBAEE,8BtEwxOR,CsEtxOM,oBAEE,+BtEwxOR,CsEtxOM,oBAEE,6BtEwxOR,CsEvyOM,SAAwB,uBtE0yO9B,CsEzyOM,oBAEE,2BtE2yOR,CsEzyOM,oBAEE,6BtE2yOR,CsEzyOM,oBAEE,8BtE2yOR,CsEzyOM,oBAEE,4BtE2yOR,CsE1zOM,SAAwB,sBtE6zO9B,CsE5zOM,oBAEE,0BtE8zOR,CsE5zOM,oBAEE,4BtE8zOR,CsE5zOM,oBAEE,6BtE8zOR,CsE5zOM,oBAEE,2BtE8zOR,CsE70OM,SAAwB,wBtEg1O9B,CsE/0OM,oBAEE,4BtEi1OR,CsE/0OM,oBAEE,8BtEi1OR,CsE/0OM,oBAEE,+BtEi1OR,CsE/0OM,oBAEE,6BtEi1OR,CsEh2OM,SAAwB,sBtEm2O9B,CsEl2OM,oBAEE,0BtEo2OR,CsEl2OM,oBAEE,4BtEo2OR,CsEl2OM,oBAEE,6BtEo2OR,CsEl2OM,oBAEE,2BtEo2OR,CsE91OE,WAAmB,qBtEi2OrB,CsEh2OE,wBAEE,yBtEk2OJ,CsEh2OE,wBAEE,2BtEk2OJ,CsEh2OE,wBAEE,4BtEk2OJ,CsEh2OE,wBAEE,0BtEk2OJ,CACF,Cc52OI,yBwDlDI,QAAgC,kBtEk6OtC,CsEj6OM,kBAEE,sBtEm6OR,CsEj6OM,kBAEE,wBtEm6OR,CsEj6OM,kBAEE,yBtEm6OR,CsEj6OM,kBAEE,uBtEm6OR,CsEl7OM,QAAgC,uBtEq7OtC,CsEp7OM,kBAEE,2BtEs7OR,CsEp7OM,kBAEE,6BtEs7OR,CsEp7OM,kBAEE,8BtEs7OR,CsEp7OM,kBAEE,4BtEs7OR,CsEr8OM,QAAgC,sBtEw8OtC,CsEv8OM,kBAEE,0BtEy8OR,CsEv8OM,kBAEE,4BtEy8OR,CsEv8OM,kBAEE,6BtEy8OR,CsEv8OM,kBAEE,2BtEy8OR,CsEx9OM,QAAgC,qBtE29OtC,CsE19OM,kBAEE,yBtE49OR,CsE19OM,kBAEE,2BtE49OR,CsE19OM,kBAEE,4BtE49OR,CsE19OM,kBAEE,0BtE49OR,CsE3+OM,QAAgC,uBtE8+OtC,CsE7+OM,kBAEE,2BtE++OR,CsE7+OM,kBAEE,6BtE++OR,CsE7+OM,kBAEE,8BtE++OR,CsE7+OM,kBAEE,4BtE++OR,CsE9/OM,QAAgC,qBtEigPtC,CsEhgPM,kBAEE,yBtEkgPR,CsEhgPM,kBAEE,2BtEkgPR,CsEhgPM,kBAEE,4BtEkgPR,CsEhgPM,kBAEE,0BtEkgPR,CsEjhPM,QAAgC,mBtEohPtC,CsEnhPM,kBAEE,uBtEqhPR,CsEnhPM,kBAEE,yBtEqhPR,CsEnhPM,kBAEE,0BtEqhPR,CsEnhPM,kBAEE,wBtEqhPR,CsEpiPM,QAAgC,wBtEuiPtC,CsEtiPM,kBAEE,4BtEwiPR,CsEtiPM,kBAEE,8BtEwiPR,CsEtiPM,kBAEE,+BtEwiPR,CsEtiPM,kBAEE,6BtEwiPR,CsEvjPM,QAAgC,uBtE0jPtC,CsEzjPM,kBAEE,2BtE2jPR,CsEzjPM,kBAEE,6BtE2jPR,CsEzjPM,kBAEE,8BtE2jPR,CsEzjPM,kBAEE,4BtE2jPR,CsE1kPM,QAAgC,sBtE6kPtC,CsE5kPM,kBAEE,0BtE8kPR,CsE5kPM,kBAEE,4BtE8kPR,CsE5kPM,kBAEE,6BtE8kPR,CsE5kPM,kBAEE,2BtE8kPR,CsE7lPM,QAAgC,wBtEgmPtC,CsE/lPM,kBAEE,4BtEimPR,CsE/lPM,kBAEE,8BtEimPR,CsE/lPM,kBAEE,+BtEimPR,CsE/lPM,kBAEE,6BtEimPR,CsEhnPM,QAAgC,sBtEmnPtC,CsElnPM,kBAEE,0BtEonPR,CsElnPM,kBAEE,4BtEonPR,CsElnPM,kBAEE,6BtEonPR,CsElnPM,kBAEE,2BtEonPR,CsE5mPM,SAAwB,wBtE+mP9B,CsE9mPM,oBAEE,4BtEgnPR,CsE9mPM,oBAEE,8BtEgnPR,CsE9mPM,oBAEE,+BtEgnPR,CsE9mPM,oBAEE,6BtEgnPR,CsE/nPM,SAAwB,uBtEkoP9B,CsEjoPM,oBAEE,2BtEmoPR,CsEjoPM,oBAEE,6BtEmoPR,CsEjoPM,oBAEE,8BtEmoPR,CsEjoPM,oBAEE,4BtEmoPR,CsElpPM,SAAwB,sBtEqpP9B,CsEppPM,oBAEE,0BtEspPR,CsEppPM,oBAEE,4BtEspPR,CsEppPM,oBAEE,6BtEspPR,CsEppPM,oBAEE,2BtEspPR,CsErqPM,SAAwB,wBtEwqP9B,CsEvqPM,oBAEE,4BtEyqPR,CsEvqPM,oBAEE,8BtEyqPR,CsEvqPM,oBAEE,+BtEyqPR,CsEvqPM,oBAEE,6BtEyqPR,CsExrPM,SAAwB,sBtE2rP9B,CsE1rPM,oBAEE,0BtE4rPR,CsE1rPM,oBAEE,4BtE4rPR,CsE1rPM,oBAEE,6BtE4rPR,CsE1rPM,oBAEE,2BtE4rPR,CsEtrPE,WAAmB,qBtEyrPrB,CsExrPE,wBAEE,yBtE0rPJ,CsExrPE,wBAEE,2BtE0rPJ,CsExrPE,wBAEE,4BtE0rPJ,CsExrPE,wBAEE,0BtE0rPJ,CACF,CcpsPI,yBwDlDI,QAAgC,kBtE0vPtC,CsEzvPM,kBAEE,sBtE2vPR,CsEzvPM,kBAEE,wBtE2vPR,CsEzvPM,kBAEE,yBtE2vPR,CsEzvPM,kBAEE,uBtE2vPR,CsE1wPM,QAAgC,uBtE6wPtC,CsE5wPM,kBAEE,2BtE8wPR,CsE5wPM,kBAEE,6BtE8wPR,CsE5wPM,kBAEE,8BtE8wPR,CsE5wPM,kBAEE,4BtE8wPR,CsE7xPM,QAAgC,sBtEgyPtC,CsE/xPM,kBAEE,0BtEiyPR,CsE/xPM,kBAEE,4BtEiyPR,CsE/xPM,kBAEE,6BtEiyPR,CsE/xPM,kBAEE,2BtEiyPR,CsEhzPM,QAAgC,qBtEmzPtC,CsElzPM,kBAEE,yBtEozPR,CsElzPM,kBAEE,2BtEozPR,CsElzPM,kBAEE,4BtEozPR,CsElzPM,kBAEE,0BtEozPR,CsEn0PM,QAAgC,uBtEs0PtC,CsEr0PM,kBAEE,2BtEu0PR,CsEr0PM,kBAEE,6BtEu0PR,CsEr0PM,kBAEE,8BtEu0PR,CsEr0PM,kBAEE,4BtEu0PR,CsEt1PM,QAAgC,qBtEy1PtC,CsEx1PM,kBAEE,yBtE01PR,CsEx1PM,kBAEE,2BtE01PR,CsEx1PM,kBAEE,4BtE01PR,CsEx1PM,kBAEE,0BtE01PR,CsEz2PM,QAAgC,mBtE42PtC,CsE32PM,kBAEE,uBtE62PR,CsE32PM,kBAEE,yBtE62PR,CsE32PM,kBAEE,0BtE62PR,CsE32PM,kBAEE,wBtE62PR,CsE53PM,QAAgC,wBtE+3PtC,CsE93PM,kBAEE,4BtEg4PR,CsE93PM,kBAEE,8BtEg4PR,CsE93PM,kBAEE,+BtEg4PR,CsE93PM,kBAEE,6BtEg4PR,CsE/4PM,QAAgC,uBtEk5PtC,CsEj5PM,kBAEE,2BtEm5PR,CsEj5PM,kBAEE,6BtEm5PR,CsEj5PM,kBAEE,8BtEm5PR,CsEj5PM,kBAEE,4BtEm5PR,CsEl6PM,QAAgC,sBtEq6PtC,CsEp6PM,kBAEE,0BtEs6PR,CsEp6PM,kBAEE,4BtEs6PR,CsEp6PM,kBAEE,6BtEs6PR,CsEp6PM,kBAEE,2BtEs6PR,CsEr7PM,QAAgC,wBtEw7PtC,CsEv7PM,kBAEE,4BtEy7PR,CsEv7PM,kBAEE,8BtEy7PR,CsEv7PM,kBAEE,+BtEy7PR,CsEv7PM,kBAEE,6BtEy7PR,CsEx8PM,QAAgC,sBtE28PtC,CsE18PM,kBAEE,0BtE48PR,CsE18PM,kBAEE,4BtE48PR,CsE18PM,kBAEE,6BtE48PR,CsE18PM,kBAEE,2BtE48PR,CsEp8PM,SAAwB,wBtEu8P9B,CsEt8PM,oBAEE,4BtEw8PR,CsEt8PM,oBAEE,8BtEw8PR,CsEt8PM,oBAEE,+BtEw8PR,CsEt8PM,oBAEE,6BtEw8PR,CsEv9PM,SAAwB,uBtE09P9B,CsEz9PM,oBAEE,2BtE29PR,CsEz9PM,oBAEE,6BtE29PR,CsEz9PM,oBAEE,8BtE29PR,CsEz9PM,oBAEE,4BtE29PR,CsE1+PM,SAAwB,sBtE6+P9B,CsE5+PM,oBAEE,0BtE8+PR,CsE5+PM,oBAEE,4BtE8+PR,CsE5+PM,oBAEE,6BtE8+PR,CsE5+PM,oBAEE,2BtE8+PR,CsE7/PM,SAAwB,wBtEggQ9B,CsE//PM,oBAEE,4BtEigQR,CsE//PM,oBAEE,8BtEigQR,CsE//PM,oBAEE,+BtEigQR,CsE//PM,oBAEE,6BtEigQR,CsEhhQM,SAAwB,sBtEmhQ9B,CsElhQM,oBAEE,0BtEohQR,CsElhQM,oBAEE,4BtEohQR,CsElhQM,oBAEE,6BtEohQR,CsElhQM,oBAEE,2BtEohQR,CsE9gQE,WAAmB,qBtEihQrB,CsEhhQE,wBAEE,yBtEkhQJ,CsEhhQE,wBAEE,2BtEkhQJ,CsEhhQE,wBAEE,4BtEkhQJ,CsEhhQE,wBAEE,0BtEkhQJ,CACF,Cc5hQI,0BwDlDI,QAAgC,kBtEklQtC,CsEjlQM,kBAEE,sBtEmlQR,CsEjlQM,kBAEE,wBtEmlQR,CsEjlQM,kBAEE,yBtEmlQR,CsEjlQM,kBAEE,uBtEmlQR,CsElmQM,QAAgC,uBtEqmQtC,CsEpmQM,kBAEE,2BtEsmQR,CsEpmQM,kBAEE,6BtEsmQR,CsEpmQM,kBAEE,8BtEsmQR,CsEpmQM,kBAEE,4BtEsmQR,CsErnQM,QAAgC,sBtEwnQtC,CsEvnQM,kBAEE,0BtEynQR,CsEvnQM,kBAEE,4BtEynQR,CsEvnQM,kBAEE,6BtEynQR,CsEvnQM,kBAEE,2BtEynQR,CsExoQM,QAAgC,qBtE2oQtC,CsE1oQM,kBAEE,yBtE4oQR,CsE1oQM,kBAEE,2BtE4oQR,CsE1oQM,kBAEE,4BtE4oQR,CsE1oQM,kBAEE,0BtE4oQR,CsE3pQM,QAAgC,uBtE8pQtC,CsE7pQM,kBAEE,2BtE+pQR,CsE7pQM,kBAEE,6BtE+pQR,CsE7pQM,kBAEE,8BtE+pQR,CsE7pQM,kBAEE,4BtE+pQR,CsE9qQM,QAAgC,qBtEirQtC,CsEhrQM,kBAEE,yBtEkrQR,CsEhrQM,kBAEE,2BtEkrQR,CsEhrQM,kBAEE,4BtEkrQR,CsEhrQM,kBAEE,0BtEkrQR,CsEjsQM,QAAgC,mBtEosQtC,CsEnsQM,kBAEE,uBtEqsQR,CsEnsQM,kBAEE,yBtEqsQR,CsEnsQM,kBAEE,0BtEqsQR,CsEnsQM,kBAEE,wBtEqsQR,CsEptQM,QAAgC,wBtEutQtC,CsEttQM,kBAEE,4BtEwtQR,CsEttQM,kBAEE,8BtEwtQR,CsEttQM,kBAEE,+BtEwtQR,CsEttQM,kBAEE,6BtEwtQR,CsEvuQM,QAAgC,uBtE0uQtC,CsEzuQM,kBAEE,2BtE2uQR,CsEzuQM,kBAEE,6BtE2uQR,CsEzuQM,kBAEE,8BtE2uQR,CsEzuQM,kBAEE,4BtE2uQR,CsE1vQM,QAAgC,sBtE6vQtC,CsE5vQM,kBAEE,0BtE8vQR,CsE5vQM,kBAEE,4BtE8vQR,CsE5vQM,kBAEE,6BtE8vQR,CsE5vQM,kBAEE,2BtE8vQR,CsE7wQM,QAAgC,wBtEgxQtC,CsE/wQM,kBAEE,4BtEixQR,CsE/wQM,kBAEE,8BtEixQR,CsE/wQM,kBAEE,+BtEixQR,CsE/wQM,kBAEE,6BtEixQR,CsEhyQM,QAAgC,sBtEmyQtC,CsElyQM,kBAEE,0BtEoyQR,CsElyQM,kBAEE,4BtEoyQR,CsElyQM,kBAEE,6BtEoyQR,CsElyQM,kBAEE,2BtEoyQR,CsE5xQM,SAAwB,wBtE+xQ9B,CsE9xQM,oBAEE,4BtEgyQR,CsE9xQM,oBAEE,8BtEgyQR,CsE9xQM,oBAEE,+BtEgyQR,CsE9xQM,oBAEE,6BtEgyQR,CsE/yQM,SAAwB,uBtEkzQ9B,CsEjzQM,oBAEE,2BtEmzQR,CsEjzQM,oBAEE,6BtEmzQR,CsEjzQM,oBAEE,8BtEmzQR,CsEjzQM,oBAEE,4BtEmzQR,CsEl0QM,SAAwB,sBtEq0Q9B,CsEp0QM,oBAEE,0BtEs0QR,CsEp0QM,oBAEE,4BtEs0QR,CsEp0QM,oBAEE,6BtEs0QR,CsEp0QM,oBAEE,2BtEs0QR,CsEr1QM,SAAwB,wBtEw1Q9B,CsEv1QM,oBAEE,4BtEy1QR,CsEv1QM,oBAEE,8BtEy1QR,CsEv1QM,oBAEE,+BtEy1QR,CsEv1QM,oBAEE,6BtEy1QR,CsEx2QM,SAAwB,sBtE22Q9B,CsE12QM,oBAEE,0BtE42QR,CsE12QM,oBAEE,4BtE42QR,CsE12QM,oBAEE,6BtE42QR,CsE12QM,oBAEE,2BtE42QR,CsEt2QE,WAAmB,qBtEy2QrB,CsEx2QE,wBAEE,yBtE02QJ,CsEx2QE,wBAEE,2BtE02QJ,CsEx2QE,wBAEE,4BtE02QJ,CsEx2QE,wBAEE,0BtE02QJ,CACF,CuE16QA,gBAAkB,gGvE66QlB,CuEz6QA,cAAiB,4BvE66QjB,CuE56QA,WAAiB,4BvEg7QjB,CuE/6QA,aAAiB,4BvEm7QjB,CuEl7QA,eCTE,gBACA,uBACA,kBxE+7QF,CuEh7QI,WAAwB,yBvEo7Q5B,CuEn7QI,YAAwB,0BvEu7Q5B,CuEt7QI,aAAwB,2BvE07Q5B,Ccr5QI,yByDvCA,cAAwB,yBvEi8Q1B,CuEh8QE,eAAwB,0BvEm8Q1B,CuEl8QE,gBAAwB,2BvEq8Q1B,CACF,Ccj6QI,yByDvCA,cAAwB,yBvE48Q1B,CuE38QE,eAAwB,0BvE88Q1B,CuE78QE,gBAAwB,2BvEg9Q1B,CACF,Cc56QI,yByDvCA,cAAwB,yBvEu9Q1B,CuEt9QE,eAAwB,0BvEy9Q1B,CuEx9QE,gBAAwB,2BvE29Q1B,CACF,Ccv7QI,0ByDvCA,cAAwB,yBvEk+Q1B,CuEj+QE,eAAwB,0BvEo+Q1B,CuEn+QE,gBAAwB,2BvEs+Q1B,CACF,CuEj+QA,gBAAmB,kCvEo+QnB,CuEn+QA,gBAAmB,kCvEu+QnB,CuEt+QA,iBAAmB,mCvE0+QnB,CuEt+QA,mBAAuB,yBvE0+QvB,CuEz+QA,qBAAuB,6BvE6+QvB,CuE5+QA,oBAAuB,yBvEg/QvB,CuE/+QA,kBAAuB,yBvEm/QvB,CuEl/QA,oBAAuB,4BvEs/QvB,CuEr/QA,aAAuB,2BvEy/QvB,CuEr/QA,YAAc,oBvEy/Qd,CyEhiRE,cACE,uBzEmiRJ,CMzhRE,0CmELM,uBzEkiRR,CyExiRE,gBACE,uBzE2iRJ,CMjiRE,8CmELM,uBzE0iRR,CyEhjRE,cACE,uBzEmjRJ,CMziRE,0CmELM,uBzEkjRR,CyExjRE,WACE,uBzE2jRJ,CMjjRE,oCmELM,uBzE0jRR,CyEhkRE,cACE,uBzEmkRJ,CMzjRE,0CmELM,uBzEkkRR,CyExkRE,aACE,uBzE2kRJ,CMjkRE,wCmELM,uBzE0kRR,CyEhlRE,YACE,uBzEmlRJ,CMzkRE,sCmELM,uBzEklRR,CyExlRE,WACE,uBzE2lRJ,CMjlRE,oCmELM,uBzE0lRR,CuEnjRA,WAAa,uBvEujRb,CuEtjRA,YAAc,uBvE0jRd,CuExjRA,eAAiB,yBvE4jRjB,CuE3jRA,eAAiB,yBvE+jRjB,CuE3jRA,WGpDE,yBACA,SAHA,YADA,WAEA,gB1EwnRF,CuE/jRA,sBAAwB,8BvEmkRxB,CuEjkRA,YAEE,mCADA,+BvEqkRF,CuE/jRA,YAAc,uBvEmkRd,C2EpoRA,SACE,4B3EuoRF,C2EpoRA,WACE,2B3EuoRF,C4EvoRE,aACE,iBAOE,0BAFA,0B5EyoRJ,C4EnoRI,YACE,yB5EqoRN,C4E5nRE,kBACE,4B5E8nRJ,C4EhnRE,IACE,8B5EknRJ,C4EhnRE,eAEE,yBACA,uB5EknRJ,C4E1mRE,MACE,0B5E4mRJ,C4EzmRE,OAEE,uB5E2mRJ,C4ExmRE,QAGE,UACA,Q5E0mRJ,C4EvmRE,MAEE,sB5EymRJ,C4EjmRE,MACE,O5EmmRJ,C4E9lRE,gBACE,yB5EmmRJ,C4E/lRE,QACE,Y5EimRJ,C4E/lRE,OACE,qB5EimRJ,C4E9lRE,OACE,kC5EgmRJ,C4E9lRI,oBAEE,+B5EgmRN,C4E3lRI,sCAEE,kC5E6lRN,C4EzlRE,YACE,a5E2lRJ,C4EzlRI,2EAIE,oB5E2lRN,C4EvlRE,sBAEE,oBxE7HK,CwE4HL,a5E0lRJ,CACF,CA7tRA,KACE,qBA+tRF,CA5tRA,iBAGE,kBA+tRF,CA5tRA,qCAGE,WADA,YADA,aAiuRF,CA5tRA,eACE,kBA+tRF,CA5tRA,uBAEE,WADA,YAguRF,CA5tRA,2KAOE,WADA,SAguRF,CA5tRA,0EAIE,yBADA,SAguRF,CA5tRA,iBACE,4CA+tRF,C", "sources": ["webpack://@gainhq/payday/./core/bootstrap/scss/_type.scss", "webpack://@gainhq/payday/./payslip.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/bootstrap.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_root.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_reboot.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_variables.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/vendor/_rfs.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_hover.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_lists.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_images.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_image.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_border-radius.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_code.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_grid.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_grid.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_breakpoints.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_grid-framework.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_tables.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_table-row.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_forms.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_transition.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_forms.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_gradients.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_buttons.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_buttons.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_transitions.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_dropdown.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_caret.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_nav-divider.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_button-group.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_input-group.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_custom-forms.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_nav.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_navbar.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_card.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_breadcrumb.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_pagination.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_pagination.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_badge.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_badge.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_jumbotron.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_alert.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_alert.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_progress.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_media.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_list-group.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_list-group.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_close.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_toasts.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_modal.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_tooltip.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_reset-text.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_popover.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_carousel.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_clearfix.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_spinners.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/utilities/_align.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_background-variant.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/utilities/_background.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/utilities/_borders.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/utilities/_display.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/utilities/_embed.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/utilities/_flex.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/utilities/_float.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/utilities/_overflow.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/utilities/_position.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/utilities/_screenreaders.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_screen-reader.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/utilities/_shadows.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/utilities/_sizing.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/utilities/_stretched-link.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/utilities/_spacing.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/utilities/_text.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_text-truncate.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_text-emphasis.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/mixins/_text-hide.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/utilities/_visibility.scss", "webpack://@gainhq/payday/./core/bootstrap/scss/_print.scss"], "sourcesContent": ["// stylelint-disable declaration-no-important, selector-list-comma-newline-after\r\n\r\n//\r\n// Headings\r\n//\r\n\r\nh1, h2, h3, h4, h5, h6,\r\n.h1, .h2, .h3, .h4, .h5, .h6 {\r\n  margin-bottom: $headings-margin-bottom;\r\n  font-family: $headings-font-family;\r\n  font-weight: $headings-font-weight;\r\n  line-height: $headings-line-height;\r\n  color: $headings-color;\r\n}\r\n\r\nh1, .h1 { @include font-size($h1-font-size); }\r\nh2, .h2 { @include font-size($h2-font-size); }\r\nh3, .h3 { @include font-size($h3-font-size); }\r\nh4, .h4 { @include font-size($h4-font-size); }\r\nh5, .h5 { @include font-size($h5-font-size); }\r\nh6, .h6 { @include font-size($h6-font-size); }\r\n\r\n.lead {\r\n  @include font-size($lead-font-size);\r\n  font-weight: $lead-font-weight;\r\n}\r\n\r\n// Type display classes\r\n.display-1 {\r\n  @include font-size($display1-size);\r\n  font-weight: $display1-weight;\r\n  line-height: $display-line-height;\r\n}\r\n.display-2 {\r\n  @include font-size($display2-size);\r\n  font-weight: $display2-weight;\r\n  line-height: $display-line-height;\r\n}\r\n.display-3 {\r\n  @include font-size($display3-size);\r\n  font-weight: $display3-weight;\r\n  line-height: $display-line-height;\r\n}\r\n.display-4 {\r\n  @include font-size($display4-size);\r\n  font-weight: $display4-weight;\r\n  line-height: $display-line-height;\r\n}\r\n\r\n\r\n//\r\n// Horizontal rules\r\n//\r\n\r\nhr {\r\n  margin-top: $hr-margin-y;\r\n  margin-bottom: $hr-margin-y;\r\n  border: 0;\r\n  border-top: $hr-border-width solid $hr-border-color;\r\n}\r\n\r\n\r\n//\r\n// Emphasis\r\n//\r\n\r\nsmall,\r\n.small {\r\n  @include font-size($small-font-size);\r\n  font-weight: $font-weight-normal;\r\n}\r\n\r\nmark,\r\n.mark {\r\n  padding: $mark-padding;\r\n  background-color: $mark-bg;\r\n}\r\n\r\n\r\n//\r\n// Lists\r\n//\r\n\r\n.list-unstyled {\r\n  @include list-unstyled;\r\n}\r\n\r\n// Inline turns list items into inline-block\r\n.list-inline {\r\n  @include list-unstyled;\r\n}\r\n.list-inline-item {\r\n  display: inline-block;\r\n\r\n  &:not(:last-child) {\r\n    margin-right: $list-inline-padding;\r\n  }\r\n}\r\n\r\n\r\n//\r\n// Misc\r\n//\r\n\r\n// Builds on `abbr`\r\n.initialism {\r\n  @include font-size(90%);\r\n  text-transform: uppercase;\r\n}\r\n\r\n// Blockquotes\r\n.blockquote {\r\n  margin-bottom: $spacer;\r\n  @include font-size($blockquote-font-size);\r\n}\r\n\r\n.blockquote-footer {\r\n  display: block;\r\n  @include font-size($blockquote-small-font-size);\r\n  color: $blockquote-small-color;\r\n\r\n  &::before {\r\n    content: \"\\2014\\00A0\"; // em dash, nbsp\r\n  }\r\n}\r\n", "@charset \"UTF-8\";\n/*!\n * Bootstrap v4.3.1 (https://getbootstrap.com/)\n * Copyright 2011-2019 The Bootstrap Authors\n * Copyright 2011-2019 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n */\n:root {\n  --blue: #007bff;\n  --indigo: #6610f2;\n  --purple: #6f42c1;\n  --pink: #e83e8c;\n  --red: #dc3545;\n  --orange: #fd7e14;\n  --yellow: #ffc107;\n  --green: #28a745;\n  --teal: #20c997;\n  --cyan: #17a2b8;\n  --white: #fff;\n  --gray: #6c757d;\n  --gray-dark: #343a40;\n  --primary: #007bff;\n  --secondary: #6c757d;\n  --success: #28a745;\n  --info: #17a2b8;\n  --warning: #ffc107;\n  --danger: #dc3545;\n  --light: #f8f9fa;\n  --dark: #343a40;\n  --breakpoint-xs: 0;\n  --breakpoint-sm: 576px;\n  --breakpoint-md: 768px;\n  --breakpoint-lg: 992px;\n  --breakpoint-xl: 1200px;\n  --font-family-sans-serif: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n}\n\n*,\n*::before,\n*::after {\n  box-sizing: border-box;\n}\n\nhtml {\n  font-family: sans-serif;\n  line-height: 1.15;\n  -webkit-text-size-adjust: 100%;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n}\n\narticle, aside, figcaption, figure, footer, header, hgroup, main, nav, section {\n  display: block;\n}\n\nbody {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  font-size: 1rem;\n  font-weight: 400;\n  line-height: 1.5;\n  color: #212529;\n  text-align: left;\n  background-color: #fff;\n}\n\n[tabindex=\"-1\"]:focus {\n  outline: 0 !important;\n}\n\nhr {\n  box-sizing: content-box;\n  height: 0;\n  overflow: visible;\n}\n\nh1, h2, h3, h4, h5, h6 {\n  margin-top: 0;\n  margin-bottom: 0.5rem;\n}\n\np {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nabbr[title],\nabbr[data-original-title] {\n  text-decoration: underline;\n  text-decoration: underline dotted;\n  cursor: help;\n  border-bottom: 0;\n  text-decoration-skip-ink: none;\n}\n\naddress {\n  margin-bottom: 1rem;\n  font-style: normal;\n  line-height: inherit;\n}\n\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0;\n}\n\ndt {\n  font-weight: 700;\n}\n\ndd {\n  margin-bottom: 0.5rem;\n  margin-left: 0;\n}\n\nblockquote {\n  margin: 0 0 1rem;\n}\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\nsmall {\n  font-size: 80%;\n}\n\nsub,\nsup {\n  position: relative;\n  font-size: 75%;\n  line-height: 0;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\na {\n  color: #007bff;\n  text-decoration: none;\n  background-color: transparent;\n}\na:hover {\n  color: #0056b3;\n  text-decoration: underline;\n}\n\na:not([href]):not([tabindex]) {\n  color: inherit;\n  text-decoration: none;\n}\na:not([href]):not([tabindex]):hover, a:not([href]):not([tabindex]):focus {\n  color: inherit;\n  text-decoration: none;\n}\na:not([href]):not([tabindex]):focus {\n  outline: 0;\n}\n\npre,\ncode,\nkbd,\nsamp {\n  font-family: SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n  font-size: 1em;\n}\n\npre {\n  margin-top: 0;\n  margin-bottom: 1rem;\n  overflow: auto;\n}\n\nfigure {\n  margin: 0 0 1rem;\n}\n\nimg {\n  vertical-align: middle;\n  border-style: none;\n}\n\nsvg {\n  overflow: hidden;\n  vertical-align: middle;\n}\n\ntable {\n  border-collapse: collapse;\n}\n\ncaption {\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n  color: #6c757d;\n  text-align: left;\n  caption-side: bottom;\n}\n\nth {\n  text-align: inherit;\n}\n\nlabel {\n  display: inline-block;\n  margin-bottom: 0.5rem;\n}\n\nbutton {\n  border-radius: 0;\n}\n\nbutton:focus {\n  outline: 1px dotted;\n  outline: 5px auto -webkit-focus-ring-color;\n}\n\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0;\n  font-family: inherit;\n  font-size: inherit;\n  line-height: inherit;\n}\n\nbutton,\ninput {\n  overflow: visible;\n}\n\nbutton,\nselect {\n  text-transform: none;\n}\n\nselect {\n  word-wrap: normal;\n}\n\nbutton,\n[type=button],\n[type=reset],\n[type=submit] {\n  -webkit-appearance: button;\n}\n\nbutton:not(:disabled),\n[type=button]:not(:disabled),\n[type=reset]:not(:disabled),\n[type=submit]:not(:disabled) {\n  cursor: pointer;\n}\n\nbutton::-moz-focus-inner,\n[type=button]::-moz-focus-inner,\n[type=reset]::-moz-focus-inner,\n[type=submit]::-moz-focus-inner {\n  padding: 0;\n  border-style: none;\n}\n\ninput[type=radio],\ninput[type=checkbox] {\n  box-sizing: border-box;\n  padding: 0;\n}\n\ninput[type=date],\ninput[type=time],\ninput[type=datetime-local],\ninput[type=month] {\n  -webkit-appearance: listbox;\n}\n\ntextarea {\n  overflow: auto;\n  resize: vertical;\n}\n\nfieldset {\n  min-width: 0;\n  padding: 0;\n  margin: 0;\n  border: 0;\n}\n\nlegend {\n  display: block;\n  width: 100%;\n  max-width: 100%;\n  padding: 0;\n  margin-bottom: 0.5rem;\n  font-size: 1.5rem;\n  line-height: inherit;\n  color: inherit;\n  white-space: normal;\n}\n\nprogress {\n  vertical-align: baseline;\n}\n\n[type=number]::-webkit-inner-spin-button,\n[type=number]::-webkit-outer-spin-button {\n  height: auto;\n}\n\n[type=search] {\n  outline-offset: -2px;\n  -webkit-appearance: none;\n}\n\n[type=search]::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n::-webkit-file-upload-button {\n  font: inherit;\n  -webkit-appearance: button;\n}\n\noutput {\n  display: inline-block;\n}\n\nsummary {\n  display: list-item;\n  cursor: pointer;\n}\n\ntemplate {\n  display: none;\n}\n\n[hidden] {\n  display: none !important;\n}\n\nh1, h2, h3, h4, h5, h6,\n.h1, .h2, .h3, .h4, .h5, .h6 {\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  line-height: 1.2;\n}\n\nh1, .h1 {\n  font-size: 2.5rem;\n}\n\nh2, .h2 {\n  font-size: 2rem;\n}\n\nh3, .h3 {\n  font-size: 1.75rem;\n}\n\nh4, .h4 {\n  font-size: 1.5rem;\n}\n\nh5, .h5 {\n  font-size: 1.25rem;\n}\n\nh6, .h6 {\n  font-size: 1rem;\n}\n\n.lead {\n  font-size: 1.25rem;\n  font-weight: 300;\n}\n\n.display-1 {\n  font-size: 6rem;\n  font-weight: 300;\n  line-height: 1.2;\n}\n\n.display-2 {\n  font-size: 5.5rem;\n  font-weight: 300;\n  line-height: 1.2;\n}\n\n.display-3 {\n  font-size: 4.5rem;\n  font-weight: 300;\n  line-height: 1.2;\n}\n\n.display-4 {\n  font-size: 3.5rem;\n  font-weight: 300;\n  line-height: 1.2;\n}\n\nhr {\n  margin-top: 1rem;\n  margin-bottom: 1rem;\n  border: 0;\n  border-top: 1px solid rgba(0, 0, 0, 0.1);\n}\n\nsmall,\n.small {\n  font-size: 80%;\n  font-weight: 400;\n}\n\nmark,\n.mark {\n  padding: 0.2em;\n  background-color: #fcf8e3;\n}\n\n.list-unstyled {\n  padding-left: 0;\n  list-style: none;\n}\n\n.list-inline {\n  padding-left: 0;\n  list-style: none;\n}\n\n.list-inline-item {\n  display: inline-block;\n}\n.list-inline-item:not(:last-child) {\n  margin-right: 0.5rem;\n}\n\n.initialism {\n  font-size: 90%;\n  text-transform: uppercase;\n}\n\n.blockquote {\n  margin-bottom: 1rem;\n  font-size: 1.25rem;\n}\n\n.blockquote-footer {\n  display: block;\n  font-size: 80%;\n  color: #6c757d;\n}\n.blockquote-footer::before {\n  content: \"— \";\n}\n\n.img-fluid {\n  max-width: 100%;\n  height: auto;\n}\n\n.img-thumbnail {\n  padding: 0.25rem;\n  background-color: #fff;\n  border: 1px solid #dee2e6;\n  border-radius: 0.25rem;\n  max-width: 100%;\n  height: auto;\n}\n\n.figure {\n  display: inline-block;\n}\n\n.figure-img {\n  margin-bottom: 0.5rem;\n  line-height: 1;\n}\n\n.figure-caption {\n  font-size: 90%;\n  color: #6c757d;\n}\n\ncode {\n  font-size: 87.5%;\n  color: #e83e8c;\n  word-break: break-word;\n}\na > code {\n  color: inherit;\n}\n\nkbd {\n  padding: 0.2rem 0.4rem;\n  font-size: 87.5%;\n  color: #fff;\n  background-color: #212529;\n  border-radius: 0.2rem;\n}\nkbd kbd {\n  padding: 0;\n  font-size: 100%;\n  font-weight: 700;\n}\n\npre {\n  display: block;\n  font-size: 87.5%;\n  color: #212529;\n}\npre code {\n  font-size: inherit;\n  color: inherit;\n  word-break: normal;\n}\n\n.pre-scrollable {\n  max-height: 340px;\n  overflow-y: scroll;\n}\n\n.container {\n  width: 100%;\n  padding-right: 15px;\n  padding-left: 15px;\n  margin-right: auto;\n  margin-left: auto;\n}\n@media (min-width: 576px) {\n  .container {\n    max-width: 540px;\n  }\n}\n@media (min-width: 768px) {\n  .container {\n    max-width: 720px;\n  }\n}\n@media (min-width: 992px) {\n  .container {\n    max-width: 960px;\n  }\n}\n@media (min-width: 1200px) {\n  .container {\n    max-width: 1140px;\n  }\n}\n\n.container-fluid {\n  width: 100%;\n  padding-right: 15px;\n  padding-left: 15px;\n  margin-right: auto;\n  margin-left: auto;\n}\n\n.row {\n  display: flex;\n  flex-wrap: wrap;\n  margin-right: -15px;\n  margin-left: -15px;\n}\n\n.no-gutters {\n  margin-right: 0;\n  margin-left: 0;\n}\n.no-gutters > .col,\n.no-gutters > [class*=col-] {\n  padding-right: 0;\n  padding-left: 0;\n}\n\n.col-xl,\n.col-xl-auto, .col-xl-12, .col-xl-11, .col-xl-10, .col-xl-9, .col-xl-8, .col-xl-7, .col-xl-6, .col-xl-5, .col-xl-4, .col-xl-3, .col-xl-2, .col-xl-1, .col-lg,\n.col-lg-auto, .col-lg-12, .col-lg-11, .col-lg-10, .col-lg-9, .col-lg-8, .col-lg-7, .col-lg-6, .col-lg-5, .col-lg-4, .col-lg-3, .col-lg-2, .col-lg-1, .col-md,\n.col-md-auto, .col-md-12, .col-md-11, .col-md-10, .col-md-9, .col-md-8, .col-md-7, .col-md-6, .col-md-5, .col-md-4, .col-md-3, .col-md-2, .col-md-1, .col-sm,\n.col-sm-auto, .col-sm-12, .col-sm-11, .col-sm-10, .col-sm-9, .col-sm-8, .col-sm-7, .col-sm-6, .col-sm-5, .col-sm-4, .col-sm-3, .col-sm-2, .col-sm-1, .col,\n.col-auto, .col-12, .col-11, .col-10, .col-9, .col-8, .col-7, .col-6, .col-5, .col-4, .col-3, .col-2, .col-1 {\n  position: relative;\n  width: 100%;\n  padding-right: 15px;\n  padding-left: 15px;\n}\n\n.col {\n  flex-basis: 0;\n  flex-grow: 1;\n  max-width: 100%;\n}\n\n.col-auto {\n  flex: 0 0 auto;\n  width: auto;\n  max-width: 100%;\n}\n\n.col-1 {\n  flex: 0 0 8.3333333333%;\n  max-width: 8.3333333333%;\n}\n\n.col-2 {\n  flex: 0 0 16.6666666667%;\n  max-width: 16.6666666667%;\n}\n\n.col-3 {\n  flex: 0 0 25%;\n  max-width: 25%;\n}\n\n.col-4 {\n  flex: 0 0 33.3333333333%;\n  max-width: 33.3333333333%;\n}\n\n.col-5 {\n  flex: 0 0 41.6666666667%;\n  max-width: 41.6666666667%;\n}\n\n.col-6 {\n  flex: 0 0 50%;\n  max-width: 50%;\n}\n\n.col-7 {\n  flex: 0 0 58.3333333333%;\n  max-width: 58.3333333333%;\n}\n\n.col-8 {\n  flex: 0 0 66.6666666667%;\n  max-width: 66.6666666667%;\n}\n\n.col-9 {\n  flex: 0 0 75%;\n  max-width: 75%;\n}\n\n.col-10 {\n  flex: 0 0 83.3333333333%;\n  max-width: 83.3333333333%;\n}\n\n.col-11 {\n  flex: 0 0 91.6666666667%;\n  max-width: 91.6666666667%;\n}\n\n.col-12 {\n  flex: 0 0 100%;\n  max-width: 100%;\n}\n\n.order-first {\n  order: -1;\n}\n\n.order-last {\n  order: 13;\n}\n\n.order-0 {\n  order: 0;\n}\n\n.order-1 {\n  order: 1;\n}\n\n.order-2 {\n  order: 2;\n}\n\n.order-3 {\n  order: 3;\n}\n\n.order-4 {\n  order: 4;\n}\n\n.order-5 {\n  order: 5;\n}\n\n.order-6 {\n  order: 6;\n}\n\n.order-7 {\n  order: 7;\n}\n\n.order-8 {\n  order: 8;\n}\n\n.order-9 {\n  order: 9;\n}\n\n.order-10 {\n  order: 10;\n}\n\n.order-11 {\n  order: 11;\n}\n\n.order-12 {\n  order: 12;\n}\n\n.offset-1 {\n  margin-left: 8.3333333333%;\n}\n\n.offset-2 {\n  margin-left: 16.6666666667%;\n}\n\n.offset-3 {\n  margin-left: 25%;\n}\n\n.offset-4 {\n  margin-left: 33.3333333333%;\n}\n\n.offset-5 {\n  margin-left: 41.6666666667%;\n}\n\n.offset-6 {\n  margin-left: 50%;\n}\n\n.offset-7 {\n  margin-left: 58.3333333333%;\n}\n\n.offset-8 {\n  margin-left: 66.6666666667%;\n}\n\n.offset-9 {\n  margin-left: 75%;\n}\n\n.offset-10 {\n  margin-left: 83.3333333333%;\n}\n\n.offset-11 {\n  margin-left: 91.6666666667%;\n}\n\n@media (min-width: 576px) {\n  .col-sm {\n    flex-basis: 0;\n    flex-grow: 1;\n    max-width: 100%;\n  }\n  .col-sm-auto {\n    flex: 0 0 auto;\n    width: auto;\n    max-width: 100%;\n  }\n  .col-sm-1 {\n    flex: 0 0 8.3333333333%;\n    max-width: 8.3333333333%;\n  }\n  .col-sm-2 {\n    flex: 0 0 16.6666666667%;\n    max-width: 16.6666666667%;\n  }\n  .col-sm-3 {\n    flex: 0 0 25%;\n    max-width: 25%;\n  }\n  .col-sm-4 {\n    flex: 0 0 33.3333333333%;\n    max-width: 33.3333333333%;\n  }\n  .col-sm-5 {\n    flex: 0 0 41.6666666667%;\n    max-width: 41.6666666667%;\n  }\n  .col-sm-6 {\n    flex: 0 0 50%;\n    max-width: 50%;\n  }\n  .col-sm-7 {\n    flex: 0 0 58.3333333333%;\n    max-width: 58.3333333333%;\n  }\n  .col-sm-8 {\n    flex: 0 0 66.6666666667%;\n    max-width: 66.6666666667%;\n  }\n  .col-sm-9 {\n    flex: 0 0 75%;\n    max-width: 75%;\n  }\n  .col-sm-10 {\n    flex: 0 0 83.3333333333%;\n    max-width: 83.3333333333%;\n  }\n  .col-sm-11 {\n    flex: 0 0 91.6666666667%;\n    max-width: 91.6666666667%;\n  }\n  .col-sm-12 {\n    flex: 0 0 100%;\n    max-width: 100%;\n  }\n  .order-sm-first {\n    order: -1;\n  }\n  .order-sm-last {\n    order: 13;\n  }\n  .order-sm-0 {\n    order: 0;\n  }\n  .order-sm-1 {\n    order: 1;\n  }\n  .order-sm-2 {\n    order: 2;\n  }\n  .order-sm-3 {\n    order: 3;\n  }\n  .order-sm-4 {\n    order: 4;\n  }\n  .order-sm-5 {\n    order: 5;\n  }\n  .order-sm-6 {\n    order: 6;\n  }\n  .order-sm-7 {\n    order: 7;\n  }\n  .order-sm-8 {\n    order: 8;\n  }\n  .order-sm-9 {\n    order: 9;\n  }\n  .order-sm-10 {\n    order: 10;\n  }\n  .order-sm-11 {\n    order: 11;\n  }\n  .order-sm-12 {\n    order: 12;\n  }\n  .offset-sm-0 {\n    margin-left: 0;\n  }\n  .offset-sm-1 {\n    margin-left: 8.3333333333%;\n  }\n  .offset-sm-2 {\n    margin-left: 16.6666666667%;\n  }\n  .offset-sm-3 {\n    margin-left: 25%;\n  }\n  .offset-sm-4 {\n    margin-left: 33.3333333333%;\n  }\n  .offset-sm-5 {\n    margin-left: 41.6666666667%;\n  }\n  .offset-sm-6 {\n    margin-left: 50%;\n  }\n  .offset-sm-7 {\n    margin-left: 58.3333333333%;\n  }\n  .offset-sm-8 {\n    margin-left: 66.6666666667%;\n  }\n  .offset-sm-9 {\n    margin-left: 75%;\n  }\n  .offset-sm-10 {\n    margin-left: 83.3333333333%;\n  }\n  .offset-sm-11 {\n    margin-left: 91.6666666667%;\n  }\n}\n@media (min-width: 768px) {\n  .col-md {\n    flex-basis: 0;\n    flex-grow: 1;\n    max-width: 100%;\n  }\n  .col-md-auto {\n    flex: 0 0 auto;\n    width: auto;\n    max-width: 100%;\n  }\n  .col-md-1 {\n    flex: 0 0 8.3333333333%;\n    max-width: 8.3333333333%;\n  }\n  .col-md-2 {\n    flex: 0 0 16.6666666667%;\n    max-width: 16.6666666667%;\n  }\n  .col-md-3 {\n    flex: 0 0 25%;\n    max-width: 25%;\n  }\n  .col-md-4 {\n    flex: 0 0 33.3333333333%;\n    max-width: 33.3333333333%;\n  }\n  .col-md-5 {\n    flex: 0 0 41.6666666667%;\n    max-width: 41.6666666667%;\n  }\n  .col-md-6 {\n    flex: 0 0 50%;\n    max-width: 50%;\n  }\n  .col-md-7 {\n    flex: 0 0 58.3333333333%;\n    max-width: 58.3333333333%;\n  }\n  .col-md-8 {\n    flex: 0 0 66.6666666667%;\n    max-width: 66.6666666667%;\n  }\n  .col-md-9 {\n    flex: 0 0 75%;\n    max-width: 75%;\n  }\n  .col-md-10 {\n    flex: 0 0 83.3333333333%;\n    max-width: 83.3333333333%;\n  }\n  .col-md-11 {\n    flex: 0 0 91.6666666667%;\n    max-width: 91.6666666667%;\n  }\n  .col-md-12 {\n    flex: 0 0 100%;\n    max-width: 100%;\n  }\n  .order-md-first {\n    order: -1;\n  }\n  .order-md-last {\n    order: 13;\n  }\n  .order-md-0 {\n    order: 0;\n  }\n  .order-md-1 {\n    order: 1;\n  }\n  .order-md-2 {\n    order: 2;\n  }\n  .order-md-3 {\n    order: 3;\n  }\n  .order-md-4 {\n    order: 4;\n  }\n  .order-md-5 {\n    order: 5;\n  }\n  .order-md-6 {\n    order: 6;\n  }\n  .order-md-7 {\n    order: 7;\n  }\n  .order-md-8 {\n    order: 8;\n  }\n  .order-md-9 {\n    order: 9;\n  }\n  .order-md-10 {\n    order: 10;\n  }\n  .order-md-11 {\n    order: 11;\n  }\n  .order-md-12 {\n    order: 12;\n  }\n  .offset-md-0 {\n    margin-left: 0;\n  }\n  .offset-md-1 {\n    margin-left: 8.3333333333%;\n  }\n  .offset-md-2 {\n    margin-left: 16.6666666667%;\n  }\n  .offset-md-3 {\n    margin-left: 25%;\n  }\n  .offset-md-4 {\n    margin-left: 33.3333333333%;\n  }\n  .offset-md-5 {\n    margin-left: 41.6666666667%;\n  }\n  .offset-md-6 {\n    margin-left: 50%;\n  }\n  .offset-md-7 {\n    margin-left: 58.3333333333%;\n  }\n  .offset-md-8 {\n    margin-left: 66.6666666667%;\n  }\n  .offset-md-9 {\n    margin-left: 75%;\n  }\n  .offset-md-10 {\n    margin-left: 83.3333333333%;\n  }\n  .offset-md-11 {\n    margin-left: 91.6666666667%;\n  }\n}\n@media (min-width: 992px) {\n  .col-lg {\n    flex-basis: 0;\n    flex-grow: 1;\n    max-width: 100%;\n  }\n  .col-lg-auto {\n    flex: 0 0 auto;\n    width: auto;\n    max-width: 100%;\n  }\n  .col-lg-1 {\n    flex: 0 0 8.3333333333%;\n    max-width: 8.3333333333%;\n  }\n  .col-lg-2 {\n    flex: 0 0 16.6666666667%;\n    max-width: 16.6666666667%;\n  }\n  .col-lg-3 {\n    flex: 0 0 25%;\n    max-width: 25%;\n  }\n  .col-lg-4 {\n    flex: 0 0 33.3333333333%;\n    max-width: 33.3333333333%;\n  }\n  .col-lg-5 {\n    flex: 0 0 41.6666666667%;\n    max-width: 41.6666666667%;\n  }\n  .col-lg-6 {\n    flex: 0 0 50%;\n    max-width: 50%;\n  }\n  .col-lg-7 {\n    flex: 0 0 58.3333333333%;\n    max-width: 58.3333333333%;\n  }\n  .col-lg-8 {\n    flex: 0 0 66.6666666667%;\n    max-width: 66.6666666667%;\n  }\n  .col-lg-9 {\n    flex: 0 0 75%;\n    max-width: 75%;\n  }\n  .col-lg-10 {\n    flex: 0 0 83.3333333333%;\n    max-width: 83.3333333333%;\n  }\n  .col-lg-11 {\n    flex: 0 0 91.6666666667%;\n    max-width: 91.6666666667%;\n  }\n  .col-lg-12 {\n    flex: 0 0 100%;\n    max-width: 100%;\n  }\n  .order-lg-first {\n    order: -1;\n  }\n  .order-lg-last {\n    order: 13;\n  }\n  .order-lg-0 {\n    order: 0;\n  }\n  .order-lg-1 {\n    order: 1;\n  }\n  .order-lg-2 {\n    order: 2;\n  }\n  .order-lg-3 {\n    order: 3;\n  }\n  .order-lg-4 {\n    order: 4;\n  }\n  .order-lg-5 {\n    order: 5;\n  }\n  .order-lg-6 {\n    order: 6;\n  }\n  .order-lg-7 {\n    order: 7;\n  }\n  .order-lg-8 {\n    order: 8;\n  }\n  .order-lg-9 {\n    order: 9;\n  }\n  .order-lg-10 {\n    order: 10;\n  }\n  .order-lg-11 {\n    order: 11;\n  }\n  .order-lg-12 {\n    order: 12;\n  }\n  .offset-lg-0 {\n    margin-left: 0;\n  }\n  .offset-lg-1 {\n    margin-left: 8.3333333333%;\n  }\n  .offset-lg-2 {\n    margin-left: 16.6666666667%;\n  }\n  .offset-lg-3 {\n    margin-left: 25%;\n  }\n  .offset-lg-4 {\n    margin-left: 33.3333333333%;\n  }\n  .offset-lg-5 {\n    margin-left: 41.6666666667%;\n  }\n  .offset-lg-6 {\n    margin-left: 50%;\n  }\n  .offset-lg-7 {\n    margin-left: 58.3333333333%;\n  }\n  .offset-lg-8 {\n    margin-left: 66.6666666667%;\n  }\n  .offset-lg-9 {\n    margin-left: 75%;\n  }\n  .offset-lg-10 {\n    margin-left: 83.3333333333%;\n  }\n  .offset-lg-11 {\n    margin-left: 91.6666666667%;\n  }\n}\n@media (min-width: 1200px) {\n  .col-xl {\n    flex-basis: 0;\n    flex-grow: 1;\n    max-width: 100%;\n  }\n  .col-xl-auto {\n    flex: 0 0 auto;\n    width: auto;\n    max-width: 100%;\n  }\n  .col-xl-1 {\n    flex: 0 0 8.3333333333%;\n    max-width: 8.3333333333%;\n  }\n  .col-xl-2 {\n    flex: 0 0 16.6666666667%;\n    max-width: 16.6666666667%;\n  }\n  .col-xl-3 {\n    flex: 0 0 25%;\n    max-width: 25%;\n  }\n  .col-xl-4 {\n    flex: 0 0 33.3333333333%;\n    max-width: 33.3333333333%;\n  }\n  .col-xl-5 {\n    flex: 0 0 41.6666666667%;\n    max-width: 41.6666666667%;\n  }\n  .col-xl-6 {\n    flex: 0 0 50%;\n    max-width: 50%;\n  }\n  .col-xl-7 {\n    flex: 0 0 58.3333333333%;\n    max-width: 58.3333333333%;\n  }\n  .col-xl-8 {\n    flex: 0 0 66.6666666667%;\n    max-width: 66.6666666667%;\n  }\n  .col-xl-9 {\n    flex: 0 0 75%;\n    max-width: 75%;\n  }\n  .col-xl-10 {\n    flex: 0 0 83.3333333333%;\n    max-width: 83.3333333333%;\n  }\n  .col-xl-11 {\n    flex: 0 0 91.6666666667%;\n    max-width: 91.6666666667%;\n  }\n  .col-xl-12 {\n    flex: 0 0 100%;\n    max-width: 100%;\n  }\n  .order-xl-first {\n    order: -1;\n  }\n  .order-xl-last {\n    order: 13;\n  }\n  .order-xl-0 {\n    order: 0;\n  }\n  .order-xl-1 {\n    order: 1;\n  }\n  .order-xl-2 {\n    order: 2;\n  }\n  .order-xl-3 {\n    order: 3;\n  }\n  .order-xl-4 {\n    order: 4;\n  }\n  .order-xl-5 {\n    order: 5;\n  }\n  .order-xl-6 {\n    order: 6;\n  }\n  .order-xl-7 {\n    order: 7;\n  }\n  .order-xl-8 {\n    order: 8;\n  }\n  .order-xl-9 {\n    order: 9;\n  }\n  .order-xl-10 {\n    order: 10;\n  }\n  .order-xl-11 {\n    order: 11;\n  }\n  .order-xl-12 {\n    order: 12;\n  }\n  .offset-xl-0 {\n    margin-left: 0;\n  }\n  .offset-xl-1 {\n    margin-left: 8.3333333333%;\n  }\n  .offset-xl-2 {\n    margin-left: 16.6666666667%;\n  }\n  .offset-xl-3 {\n    margin-left: 25%;\n  }\n  .offset-xl-4 {\n    margin-left: 33.3333333333%;\n  }\n  .offset-xl-5 {\n    margin-left: 41.6666666667%;\n  }\n  .offset-xl-6 {\n    margin-left: 50%;\n  }\n  .offset-xl-7 {\n    margin-left: 58.3333333333%;\n  }\n  .offset-xl-8 {\n    margin-left: 66.6666666667%;\n  }\n  .offset-xl-9 {\n    margin-left: 75%;\n  }\n  .offset-xl-10 {\n    margin-left: 83.3333333333%;\n  }\n  .offset-xl-11 {\n    margin-left: 91.6666666667%;\n  }\n}\n.table {\n  width: 100%;\n  margin-bottom: 1rem;\n  color: #212529;\n}\n.table th,\n.table td {\n  padding: 0.75rem;\n  vertical-align: top;\n  border-top: 1px solid #dee2e6;\n}\n.table thead th {\n  vertical-align: bottom;\n  border-bottom: 2px solid #dee2e6;\n}\n.table tbody + tbody {\n  border-top: 2px solid #dee2e6;\n}\n\n.table-sm th,\n.table-sm td {\n  padding: 0.3rem;\n}\n\n.table-bordered {\n  border: 1px solid #dee2e6;\n}\n.table-bordered th,\n.table-bordered td {\n  border: 1px solid #dee2e6;\n}\n.table-bordered thead th,\n.table-bordered thead td {\n  border-bottom-width: 2px;\n}\n\n.table-borderless th,\n.table-borderless td,\n.table-borderless thead th,\n.table-borderless tbody + tbody {\n  border: 0;\n}\n\n.table-striped tbody tr:nth-of-type(odd) {\n  background-color: rgba(0, 0, 0, 0.05);\n}\n\n.table-hover tbody tr:hover {\n  color: #212529;\n  background-color: rgba(0, 0, 0, 0.075);\n}\n\n.table-primary,\n.table-primary > th,\n.table-primary > td {\n  background-color: #b8daff;\n}\n.table-primary th,\n.table-primary td,\n.table-primary thead th,\n.table-primary tbody + tbody {\n  border-color: #7abaff;\n}\n\n.table-hover .table-primary:hover {\n  background-color: #9fcdff;\n}\n.table-hover .table-primary:hover > td,\n.table-hover .table-primary:hover > th {\n  background-color: #9fcdff;\n}\n\n.table-secondary,\n.table-secondary > th,\n.table-secondary > td {\n  background-color: #d6d8db;\n}\n.table-secondary th,\n.table-secondary td,\n.table-secondary thead th,\n.table-secondary tbody + tbody {\n  border-color: #b3b7bb;\n}\n\n.table-hover .table-secondary:hover {\n  background-color: #c8cbcf;\n}\n.table-hover .table-secondary:hover > td,\n.table-hover .table-secondary:hover > th {\n  background-color: #c8cbcf;\n}\n\n.table-success,\n.table-success > th,\n.table-success > td {\n  background-color: #c3e6cb;\n}\n.table-success th,\n.table-success td,\n.table-success thead th,\n.table-success tbody + tbody {\n  border-color: #8fd19e;\n}\n\n.table-hover .table-success:hover {\n  background-color: #b1dfbb;\n}\n.table-hover .table-success:hover > td,\n.table-hover .table-success:hover > th {\n  background-color: #b1dfbb;\n}\n\n.table-info,\n.table-info > th,\n.table-info > td {\n  background-color: #bee5eb;\n}\n.table-info th,\n.table-info td,\n.table-info thead th,\n.table-info tbody + tbody {\n  border-color: #86cfda;\n}\n\n.table-hover .table-info:hover {\n  background-color: #abdde5;\n}\n.table-hover .table-info:hover > td,\n.table-hover .table-info:hover > th {\n  background-color: #abdde5;\n}\n\n.table-warning,\n.table-warning > th,\n.table-warning > td {\n  background-color: #ffeeba;\n}\n.table-warning th,\n.table-warning td,\n.table-warning thead th,\n.table-warning tbody + tbody {\n  border-color: #ffdf7e;\n}\n\n.table-hover .table-warning:hover {\n  background-color: #ffe8a1;\n}\n.table-hover .table-warning:hover > td,\n.table-hover .table-warning:hover > th {\n  background-color: #ffe8a1;\n}\n\n.table-danger,\n.table-danger > th,\n.table-danger > td {\n  background-color: #f5c6cb;\n}\n.table-danger th,\n.table-danger td,\n.table-danger thead th,\n.table-danger tbody + tbody {\n  border-color: #ed969e;\n}\n\n.table-hover .table-danger:hover {\n  background-color: #f1b0b7;\n}\n.table-hover .table-danger:hover > td,\n.table-hover .table-danger:hover > th {\n  background-color: #f1b0b7;\n}\n\n.table-light,\n.table-light > th,\n.table-light > td {\n  background-color: #fdfdfe;\n}\n.table-light th,\n.table-light td,\n.table-light thead th,\n.table-light tbody + tbody {\n  border-color: #fbfcfc;\n}\n\n.table-hover .table-light:hover {\n  background-color: #ececf6;\n}\n.table-hover .table-light:hover > td,\n.table-hover .table-light:hover > th {\n  background-color: #ececf6;\n}\n\n.table-dark,\n.table-dark > th,\n.table-dark > td {\n  background-color: #c6c8ca;\n}\n.table-dark th,\n.table-dark td,\n.table-dark thead th,\n.table-dark tbody + tbody {\n  border-color: #95999c;\n}\n\n.table-hover .table-dark:hover {\n  background-color: #b9bbbe;\n}\n.table-hover .table-dark:hover > td,\n.table-hover .table-dark:hover > th {\n  background-color: #b9bbbe;\n}\n\n.table-active,\n.table-active > th,\n.table-active > td {\n  background-color: rgba(0, 0, 0, 0.075);\n}\n\n.table-hover .table-active:hover {\n  background-color: rgba(0, 0, 0, 0.075);\n}\n.table-hover .table-active:hover > td,\n.table-hover .table-active:hover > th {\n  background-color: rgba(0, 0, 0, 0.075);\n}\n\n.table .thead-dark th {\n  color: #fff;\n  background-color: #343a40;\n  border-color: #454d55;\n}\n.table .thead-light th {\n  color: #495057;\n  background-color: #e9ecef;\n  border-color: #dee2e6;\n}\n\n.table-dark {\n  color: #fff;\n  background-color: #343a40;\n}\n.table-dark th,\n.table-dark td,\n.table-dark thead th {\n  border-color: #454d55;\n}\n.table-dark.table-bordered {\n  border: 0;\n}\n.table-dark.table-striped tbody tr:nth-of-type(odd) {\n  background-color: rgba(255, 255, 255, 0.05);\n}\n.table-dark.table-hover tbody tr:hover {\n  color: #fff;\n  background-color: rgba(255, 255, 255, 0.075);\n}\n\n@media (max-width: 575.98px) {\n  .table-responsive-sm {\n    display: block;\n    width: 100%;\n    overflow-x: auto;\n    -webkit-overflow-scrolling: touch;\n  }\n  .table-responsive-sm > .table-bordered {\n    border: 0;\n  }\n}\n@media (max-width: 767.98px) {\n  .table-responsive-md {\n    display: block;\n    width: 100%;\n    overflow-x: auto;\n    -webkit-overflow-scrolling: touch;\n  }\n  .table-responsive-md > .table-bordered {\n    border: 0;\n  }\n}\n@media (max-width: 991.98px) {\n  .table-responsive-lg {\n    display: block;\n    width: 100%;\n    overflow-x: auto;\n    -webkit-overflow-scrolling: touch;\n  }\n  .table-responsive-lg > .table-bordered {\n    border: 0;\n  }\n}\n@media (max-width: 1199.98px) {\n  .table-responsive-xl {\n    display: block;\n    width: 100%;\n    overflow-x: auto;\n    -webkit-overflow-scrolling: touch;\n  }\n  .table-responsive-xl > .table-bordered {\n    border: 0;\n  }\n}\n.table-responsive {\n  display: block;\n  width: 100%;\n  overflow-x: auto;\n  -webkit-overflow-scrolling: touch;\n}\n.table-responsive > .table-bordered {\n  border: 0;\n}\n\n.form-control {\n  display: block;\n  width: 100%;\n  height: calc(1.5em + 0.75rem + 2px);\n  padding: 0.375rem 0.75rem;\n  font-size: 1rem;\n  font-weight: 400;\n  line-height: 1.5;\n  color: #495057;\n  background-color: #fff;\n  background-clip: padding-box;\n  border: 1px solid #ced4da;\n  border-radius: 0.25rem;\n  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n@media (prefers-reduced-motion: reduce) {\n  .form-control {\n    transition: none;\n  }\n}\n.form-control::-ms-expand {\n  background-color: transparent;\n  border: 0;\n}\n.form-control:focus {\n  color: #495057;\n  background-color: #fff;\n  border-color: #80bdff;\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n.form-control::placeholder {\n  color: #6c757d;\n  opacity: 1;\n}\n.form-control:disabled, .form-control[readonly] {\n  background-color: #e9ecef;\n  opacity: 1;\n}\n\nselect.form-control:focus::-ms-value {\n  color: #495057;\n  background-color: #fff;\n}\n\n.form-control-file,\n.form-control-range {\n  display: block;\n  width: 100%;\n}\n\n.col-form-label {\n  padding-top: calc(0.375rem + 1px);\n  padding-bottom: calc(0.375rem + 1px);\n  margin-bottom: 0;\n  font-size: inherit;\n  line-height: 1.5;\n}\n\n.col-form-label-lg {\n  padding-top: calc(0.5rem + 1px);\n  padding-bottom: calc(0.5rem + 1px);\n  font-size: 1.25rem;\n  line-height: 1.5;\n}\n\n.col-form-label-sm {\n  padding-top: calc(0.25rem + 1px);\n  padding-bottom: calc(0.25rem + 1px);\n  font-size: 0.875rem;\n  line-height: 1.5;\n}\n\n.form-control-plaintext {\n  display: block;\n  width: 100%;\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n  margin-bottom: 0;\n  line-height: 1.5;\n  color: #212529;\n  background-color: transparent;\n  border: solid transparent;\n  border-width: 1px 0;\n}\n.form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg {\n  padding-right: 0;\n  padding-left: 0;\n}\n\n.form-control-sm {\n  height: calc(1.5em + 0.5rem + 2px);\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  border-radius: 0.2rem;\n}\n\n.form-control-lg {\n  height: calc(1.5em + 1rem + 2px);\n  padding: 0.5rem 1rem;\n  font-size: 1.25rem;\n  line-height: 1.5;\n  border-radius: 0.3rem;\n}\n\nselect.form-control[size], select.form-control[multiple] {\n  height: auto;\n}\n\ntextarea.form-control {\n  height: auto;\n}\n\n.form-group {\n  margin-bottom: 1rem;\n}\n\n.form-text {\n  display: block;\n  margin-top: 0.25rem;\n}\n\n.form-row {\n  display: flex;\n  flex-wrap: wrap;\n  margin-right: -5px;\n  margin-left: -5px;\n}\n.form-row > .col,\n.form-row > [class*=col-] {\n  padding-right: 5px;\n  padding-left: 5px;\n}\n\n.form-check {\n  position: relative;\n  display: block;\n  padding-left: 1.25rem;\n}\n\n.form-check-input {\n  position: absolute;\n  margin-top: 0.3rem;\n  margin-left: -1.25rem;\n}\n.form-check-input:disabled ~ .form-check-label {\n  color: #6c757d;\n}\n\n.form-check-label {\n  margin-bottom: 0;\n}\n\n.form-check-inline {\n  display: inline-flex;\n  align-items: center;\n  padding-left: 0;\n  margin-right: 0.75rem;\n}\n.form-check-inline .form-check-input {\n  position: static;\n  margin-top: 0;\n  margin-right: 0.3125rem;\n  margin-left: 0;\n}\n\n.valid-feedback {\n  display: none;\n  width: 100%;\n  margin-top: 0.25rem;\n  font-size: 80%;\n  color: #28a745;\n}\n\n.valid-tooltip {\n  position: absolute;\n  top: 100%;\n  z-index: 5;\n  display: none;\n  max-width: 100%;\n  padding: 0.25rem 0.5rem;\n  margin-top: 0.1rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  color: #fff;\n  background-color: rgba(40, 167, 69, 0.9);\n  border-radius: 0.25rem;\n}\n\n.was-validated .form-control:valid, .form-control.is-valid {\n  border-color: #28a745;\n  padding-right: calc(1.5em + 0.75rem);\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e\");\n  background-repeat: no-repeat;\n  background-position: center right calc(0.375em + 0.1875rem);\n  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);\n}\n.was-validated .form-control:valid:focus, .form-control.is-valid:focus {\n  border-color: #28a745;\n  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\n}\n.was-validated .form-control:valid ~ .valid-feedback,\n.was-validated .form-control:valid ~ .valid-tooltip, .form-control.is-valid ~ .valid-feedback,\n.form-control.is-valid ~ .valid-tooltip {\n  display: block;\n}\n\n.was-validated textarea.form-control:valid, textarea.form-control.is-valid {\n  padding-right: calc(1.5em + 0.75rem);\n  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);\n}\n\n.was-validated .custom-select:valid, .custom-select.is-valid {\n  border-color: #28a745;\n  padding-right: calc((1em + 0.75rem) * 3 / 4 + 1.75rem);\n  background: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e\") no-repeat right 0.75rem center/8px 10px, url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e\") #fff no-repeat center right 1.75rem/calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);\n}\n.was-validated .custom-select:valid:focus, .custom-select.is-valid:focus {\n  border-color: #28a745;\n  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\n}\n.was-validated .custom-select:valid ~ .valid-feedback,\n.was-validated .custom-select:valid ~ .valid-tooltip, .custom-select.is-valid ~ .valid-feedback,\n.custom-select.is-valid ~ .valid-tooltip {\n  display: block;\n}\n\n.was-validated .form-control-file:valid ~ .valid-feedback,\n.was-validated .form-control-file:valid ~ .valid-tooltip, .form-control-file.is-valid ~ .valid-feedback,\n.form-control-file.is-valid ~ .valid-tooltip {\n  display: block;\n}\n\n.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label {\n  color: #28a745;\n}\n.was-validated .form-check-input:valid ~ .valid-feedback,\n.was-validated .form-check-input:valid ~ .valid-tooltip, .form-check-input.is-valid ~ .valid-feedback,\n.form-check-input.is-valid ~ .valid-tooltip {\n  display: block;\n}\n\n.was-validated .custom-control-input:valid ~ .custom-control-label, .custom-control-input.is-valid ~ .custom-control-label {\n  color: #28a745;\n}\n.was-validated .custom-control-input:valid ~ .custom-control-label::before, .custom-control-input.is-valid ~ .custom-control-label::before {\n  border-color: #28a745;\n}\n.was-validated .custom-control-input:valid ~ .valid-feedback,\n.was-validated .custom-control-input:valid ~ .valid-tooltip, .custom-control-input.is-valid ~ .valid-feedback,\n.custom-control-input.is-valid ~ .valid-tooltip {\n  display: block;\n}\n.was-validated .custom-control-input:valid:checked ~ .custom-control-label::before, .custom-control-input.is-valid:checked ~ .custom-control-label::before {\n  border-color: #34ce57;\n  background-color: #34ce57;\n}\n.was-validated .custom-control-input:valid:focus ~ .custom-control-label::before, .custom-control-input.is-valid:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\n}\n.was-validated .custom-control-input:valid:focus:not(:checked) ~ .custom-control-label::before, .custom-control-input.is-valid:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #28a745;\n}\n\n.was-validated .custom-file-input:valid ~ .custom-file-label, .custom-file-input.is-valid ~ .custom-file-label {\n  border-color: #28a745;\n}\n.was-validated .custom-file-input:valid ~ .valid-feedback,\n.was-validated .custom-file-input:valid ~ .valid-tooltip, .custom-file-input.is-valid ~ .valid-feedback,\n.custom-file-input.is-valid ~ .valid-tooltip {\n  display: block;\n}\n.was-validated .custom-file-input:valid:focus ~ .custom-file-label, .custom-file-input.is-valid:focus ~ .custom-file-label {\n  border-color: #28a745;\n  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\n}\n\n.invalid-feedback {\n  display: none;\n  width: 100%;\n  margin-top: 0.25rem;\n  font-size: 80%;\n  color: #dc3545;\n}\n\n.invalid-tooltip {\n  position: absolute;\n  top: 100%;\n  z-index: 5;\n  display: none;\n  max-width: 100%;\n  padding: 0.25rem 0.5rem;\n  margin-top: 0.1rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  color: #fff;\n  background-color: rgba(220, 53, 69, 0.9);\n  border-radius: 0.25rem;\n}\n\n.was-validated .form-control:invalid, .form-control.is-invalid {\n  border-color: #dc3545;\n  padding-right: calc(1.5em + 0.75rem);\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23dc3545' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23dc3545' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E\");\n  background-repeat: no-repeat;\n  background-position: center right calc(0.375em + 0.1875rem);\n  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);\n}\n.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus {\n  border-color: #dc3545;\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n.was-validated .form-control:invalid ~ .invalid-feedback,\n.was-validated .form-control:invalid ~ .invalid-tooltip, .form-control.is-invalid ~ .invalid-feedback,\n.form-control.is-invalid ~ .invalid-tooltip {\n  display: block;\n}\n\n.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid {\n  padding-right: calc(1.5em + 0.75rem);\n  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);\n}\n\n.was-validated .custom-select:invalid, .custom-select.is-invalid {\n  border-color: #dc3545;\n  padding-right: calc((1em + 0.75rem) * 3 / 4 + 1.75rem);\n  background: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e\") no-repeat right 0.75rem center/8px 10px, url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23dc3545' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23dc3545' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E\") #fff no-repeat center right 1.75rem/calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);\n}\n.was-validated .custom-select:invalid:focus, .custom-select.is-invalid:focus {\n  border-color: #dc3545;\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n.was-validated .custom-select:invalid ~ .invalid-feedback,\n.was-validated .custom-select:invalid ~ .invalid-tooltip, .custom-select.is-invalid ~ .invalid-feedback,\n.custom-select.is-invalid ~ .invalid-tooltip {\n  display: block;\n}\n\n.was-validated .form-control-file:invalid ~ .invalid-feedback,\n.was-validated .form-control-file:invalid ~ .invalid-tooltip, .form-control-file.is-invalid ~ .invalid-feedback,\n.form-control-file.is-invalid ~ .invalid-tooltip {\n  display: block;\n}\n\n.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label {\n  color: #dc3545;\n}\n.was-validated .form-check-input:invalid ~ .invalid-feedback,\n.was-validated .form-check-input:invalid ~ .invalid-tooltip, .form-check-input.is-invalid ~ .invalid-feedback,\n.form-check-input.is-invalid ~ .invalid-tooltip {\n  display: block;\n}\n\n.was-validated .custom-control-input:invalid ~ .custom-control-label, .custom-control-input.is-invalid ~ .custom-control-label {\n  color: #dc3545;\n}\n.was-validated .custom-control-input:invalid ~ .custom-control-label::before, .custom-control-input.is-invalid ~ .custom-control-label::before {\n  border-color: #dc3545;\n}\n.was-validated .custom-control-input:invalid ~ .invalid-feedback,\n.was-validated .custom-control-input:invalid ~ .invalid-tooltip, .custom-control-input.is-invalid ~ .invalid-feedback,\n.custom-control-input.is-invalid ~ .invalid-tooltip {\n  display: block;\n}\n.was-validated .custom-control-input:invalid:checked ~ .custom-control-label::before, .custom-control-input.is-invalid:checked ~ .custom-control-label::before {\n  border-color: #e4606d;\n  background-color: #e4606d;\n}\n.was-validated .custom-control-input:invalid:focus ~ .custom-control-label::before, .custom-control-input.is-invalid:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n.was-validated .custom-control-input:invalid:focus:not(:checked) ~ .custom-control-label::before, .custom-control-input.is-invalid:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #dc3545;\n}\n\n.was-validated .custom-file-input:invalid ~ .custom-file-label, .custom-file-input.is-invalid ~ .custom-file-label {\n  border-color: #dc3545;\n}\n.was-validated .custom-file-input:invalid ~ .invalid-feedback,\n.was-validated .custom-file-input:invalid ~ .invalid-tooltip, .custom-file-input.is-invalid ~ .invalid-feedback,\n.custom-file-input.is-invalid ~ .invalid-tooltip {\n  display: block;\n}\n.was-validated .custom-file-input:invalid:focus ~ .custom-file-label, .custom-file-input.is-invalid:focus ~ .custom-file-label {\n  border-color: #dc3545;\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n\n.form-inline {\n  display: flex;\n  flex-flow: row wrap;\n  align-items: center;\n}\n.form-inline .form-check {\n  width: 100%;\n}\n@media (min-width: 576px) {\n  .form-inline label {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-bottom: 0;\n  }\n  .form-inline .form-group {\n    display: flex;\n    flex: 0 0 auto;\n    flex-flow: row wrap;\n    align-items: center;\n    margin-bottom: 0;\n  }\n  .form-inline .form-control {\n    display: inline-block;\n    width: auto;\n    vertical-align: middle;\n  }\n  .form-inline .form-control-plaintext {\n    display: inline-block;\n  }\n  .form-inline .input-group,\n  .form-inline .custom-select {\n    width: auto;\n  }\n  .form-inline .form-check {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: auto;\n    padding-left: 0;\n  }\n  .form-inline .form-check-input {\n    position: relative;\n    flex-shrink: 0;\n    margin-top: 0;\n    margin-right: 0.25rem;\n    margin-left: 0;\n  }\n  .form-inline .custom-control {\n    align-items: center;\n    justify-content: center;\n  }\n  .form-inline .custom-control-label {\n    margin-bottom: 0;\n  }\n}\n\n.btn {\n  display: inline-block;\n  font-weight: 400;\n  color: #212529;\n  text-align: center;\n  vertical-align: middle;\n  user-select: none;\n  background-color: transparent;\n  border: 1px solid transparent;\n  padding: 0.375rem 0.75rem;\n  font-size: 1rem;\n  line-height: 1.5;\n  border-radius: 0.25rem;\n  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n@media (prefers-reduced-motion: reduce) {\n  .btn {\n    transition: none;\n  }\n}\n.btn:hover {\n  color: #212529;\n  text-decoration: none;\n}\n.btn:focus, .btn.focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n.btn.disabled, .btn:disabled {\n  opacity: 0.65;\n}\na.btn.disabled,\nfieldset:disabled a.btn {\n  pointer-events: none;\n}\n\n.btn-primary {\n  color: #fff;\n  background-color: #007bff;\n  border-color: #007bff;\n}\n.btn-primary:hover {\n  color: #fff;\n  background-color: #0069d9;\n  border-color: #0062cc;\n}\n.btn-primary:focus, .btn-primary.focus {\n  box-shadow: 0 0 0 0.2rem rgba(38, 143, 255, 0.5);\n}\n.btn-primary.disabled, .btn-primary:disabled {\n  color: #fff;\n  background-color: #007bff;\n  border-color: #007bff;\n}\n.btn-primary:not(:disabled):not(.disabled):active, .btn-primary:not(:disabled):not(.disabled).active, .show > .btn-primary.dropdown-toggle {\n  color: #fff;\n  background-color: #0062cc;\n  border-color: #005cbf;\n}\n.btn-primary:not(:disabled):not(.disabled):active:focus, .btn-primary:not(:disabled):not(.disabled).active:focus, .show > .btn-primary.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(38, 143, 255, 0.5);\n}\n\n.btn-secondary {\n  color: #fff;\n  background-color: #6c757d;\n  border-color: #6c757d;\n}\n.btn-secondary:hover {\n  color: #fff;\n  background-color: #5a6268;\n  border-color: #545b62;\n}\n.btn-secondary:focus, .btn-secondary.focus {\n  box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);\n}\n.btn-secondary.disabled, .btn-secondary:disabled {\n  color: #fff;\n  background-color: #6c757d;\n  border-color: #6c757d;\n}\n.btn-secondary:not(:disabled):not(.disabled):active, .btn-secondary:not(:disabled):not(.disabled).active, .show > .btn-secondary.dropdown-toggle {\n  color: #fff;\n  background-color: #545b62;\n  border-color: #4e555b;\n}\n.btn-secondary:not(:disabled):not(.disabled):active:focus, .btn-secondary:not(:disabled):not(.disabled).active:focus, .show > .btn-secondary.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);\n}\n\n.btn-success {\n  color: #fff;\n  background-color: #28a745;\n  border-color: #28a745;\n}\n.btn-success:hover {\n  color: #fff;\n  background-color: #218838;\n  border-color: #1e7e34;\n}\n.btn-success:focus, .btn-success.focus {\n  box-shadow: 0 0 0 0.2rem rgba(72, 180, 97, 0.5);\n}\n.btn-success.disabled, .btn-success:disabled {\n  color: #fff;\n  background-color: #28a745;\n  border-color: #28a745;\n}\n.btn-success:not(:disabled):not(.disabled):active, .btn-success:not(:disabled):not(.disabled).active, .show > .btn-success.dropdown-toggle {\n  color: #fff;\n  background-color: #1e7e34;\n  border-color: #1c7430;\n}\n.btn-success:not(:disabled):not(.disabled):active:focus, .btn-success:not(:disabled):not(.disabled).active:focus, .show > .btn-success.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(72, 180, 97, 0.5);\n}\n\n.btn-info {\n  color: #fff;\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n}\n.btn-info:hover {\n  color: #fff;\n  background-color: #138496;\n  border-color: #117a8b;\n}\n.btn-info:focus, .btn-info.focus {\n  box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5);\n}\n.btn-info.disabled, .btn-info:disabled {\n  color: #fff;\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n}\n.btn-info:not(:disabled):not(.disabled):active, .btn-info:not(:disabled):not(.disabled).active, .show > .btn-info.dropdown-toggle {\n  color: #fff;\n  background-color: #117a8b;\n  border-color: #10707f;\n}\n.btn-info:not(:disabled):not(.disabled):active:focus, .btn-info:not(:disabled):not(.disabled).active:focus, .show > .btn-info.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5);\n}\n\n.btn-warning {\n  color: #212529;\n  background-color: #ffc107;\n  border-color: #ffc107;\n}\n.btn-warning:hover {\n  color: #212529;\n  background-color: #e0a800;\n  border-color: #d39e00;\n}\n.btn-warning:focus, .btn-warning.focus {\n  box-shadow: 0 0 0 0.2rem rgba(222, 170, 12, 0.5);\n}\n.btn-warning.disabled, .btn-warning:disabled {\n  color: #212529;\n  background-color: #ffc107;\n  border-color: #ffc107;\n}\n.btn-warning:not(:disabled):not(.disabled):active, .btn-warning:not(:disabled):not(.disabled).active, .show > .btn-warning.dropdown-toggle {\n  color: #212529;\n  background-color: #d39e00;\n  border-color: #c69500;\n}\n.btn-warning:not(:disabled):not(.disabled):active:focus, .btn-warning:not(:disabled):not(.disabled).active:focus, .show > .btn-warning.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(222, 170, 12, 0.5);\n}\n\n.btn-danger {\n  color: #fff;\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n.btn-danger:hover {\n  color: #fff;\n  background-color: #c82333;\n  border-color: #bd2130;\n}\n.btn-danger:focus, .btn-danger.focus {\n  box-shadow: 0 0 0 0.2rem rgba(225, 83, 97, 0.5);\n}\n.btn-danger.disabled, .btn-danger:disabled {\n  color: #fff;\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n.btn-danger:not(:disabled):not(.disabled):active, .btn-danger:not(:disabled):not(.disabled).active, .show > .btn-danger.dropdown-toggle {\n  color: #fff;\n  background-color: #bd2130;\n  border-color: #b21f2d;\n}\n.btn-danger:not(:disabled):not(.disabled):active:focus, .btn-danger:not(:disabled):not(.disabled).active:focus, .show > .btn-danger.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(225, 83, 97, 0.5);\n}\n\n.btn-light {\n  color: #212529;\n  background-color: #f8f9fa;\n  border-color: #f8f9fa;\n}\n.btn-light:hover {\n  color: #212529;\n  background-color: #e2e6ea;\n  border-color: #dae0e5;\n}\n.btn-light:focus, .btn-light.focus {\n  box-shadow: 0 0 0 0.2rem rgba(216, 217, 219, 0.5);\n}\n.btn-light.disabled, .btn-light:disabled {\n  color: #212529;\n  background-color: #f8f9fa;\n  border-color: #f8f9fa;\n}\n.btn-light:not(:disabled):not(.disabled):active, .btn-light:not(:disabled):not(.disabled).active, .show > .btn-light.dropdown-toggle {\n  color: #212529;\n  background-color: #dae0e5;\n  border-color: #d3d9df;\n}\n.btn-light:not(:disabled):not(.disabled):active:focus, .btn-light:not(:disabled):not(.disabled).active:focus, .show > .btn-light.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(216, 217, 219, 0.5);\n}\n\n.btn-dark {\n  color: #fff;\n  background-color: #343a40;\n  border-color: #343a40;\n}\n.btn-dark:hover {\n  color: #fff;\n  background-color: #23272b;\n  border-color: #1d2124;\n}\n.btn-dark:focus, .btn-dark.focus {\n  box-shadow: 0 0 0 0.2rem rgba(82, 88, 93, 0.5);\n}\n.btn-dark.disabled, .btn-dark:disabled {\n  color: #fff;\n  background-color: #343a40;\n  border-color: #343a40;\n}\n.btn-dark:not(:disabled):not(.disabled):active, .btn-dark:not(:disabled):not(.disabled).active, .show > .btn-dark.dropdown-toggle {\n  color: #fff;\n  background-color: #1d2124;\n  border-color: #171a1d;\n}\n.btn-dark:not(:disabled):not(.disabled):active:focus, .btn-dark:not(:disabled):not(.disabled).active:focus, .show > .btn-dark.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(82, 88, 93, 0.5);\n}\n\n.btn-outline-primary {\n  color: #007bff;\n  border-color: #007bff;\n}\n.btn-outline-primary:hover {\n  color: #fff;\n  background-color: #007bff;\n  border-color: #007bff;\n}\n.btn-outline-primary:focus, .btn-outline-primary.focus {\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);\n}\n.btn-outline-primary.disabled, .btn-outline-primary:disabled {\n  color: #007bff;\n  background-color: transparent;\n}\n.btn-outline-primary:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active, .show > .btn-outline-primary.dropdown-toggle {\n  color: #fff;\n  background-color: #007bff;\n  border-color: #007bff;\n}\n.btn-outline-primary:not(:disabled):not(.disabled):active:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-primary.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);\n}\n\n.btn-outline-secondary {\n  color: #6c757d;\n  border-color: #6c757d;\n}\n.btn-outline-secondary:hover {\n  color: #fff;\n  background-color: #6c757d;\n  border-color: #6c757d;\n}\n.btn-outline-secondary:focus, .btn-outline-secondary.focus {\n  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);\n}\n.btn-outline-secondary.disabled, .btn-outline-secondary:disabled {\n  color: #6c757d;\n  background-color: transparent;\n}\n.btn-outline-secondary:not(:disabled):not(.disabled):active, .btn-outline-secondary:not(:disabled):not(.disabled).active, .show > .btn-outline-secondary.dropdown-toggle {\n  color: #fff;\n  background-color: #6c757d;\n  border-color: #6c757d;\n}\n.btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .btn-outline-secondary:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-secondary.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);\n}\n\n.btn-outline-success {\n  color: #28a745;\n  border-color: #28a745;\n}\n.btn-outline-success:hover {\n  color: #fff;\n  background-color: #28a745;\n  border-color: #28a745;\n}\n.btn-outline-success:focus, .btn-outline-success.focus {\n  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);\n}\n.btn-outline-success.disabled, .btn-outline-success:disabled {\n  color: #28a745;\n  background-color: transparent;\n}\n.btn-outline-success:not(:disabled):not(.disabled):active, .btn-outline-success:not(:disabled):not(.disabled).active, .show > .btn-outline-success.dropdown-toggle {\n  color: #fff;\n  background-color: #28a745;\n  border-color: #28a745;\n}\n.btn-outline-success:not(:disabled):not(.disabled):active:focus, .btn-outline-success:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-success.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);\n}\n\n.btn-outline-info {\n  color: #17a2b8;\n  border-color: #17a2b8;\n}\n.btn-outline-info:hover {\n  color: #fff;\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n}\n.btn-outline-info:focus, .btn-outline-info.focus {\n  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);\n}\n.btn-outline-info.disabled, .btn-outline-info:disabled {\n  color: #17a2b8;\n  background-color: transparent;\n}\n.btn-outline-info:not(:disabled):not(.disabled):active, .btn-outline-info:not(:disabled):not(.disabled).active, .show > .btn-outline-info.dropdown-toggle {\n  color: #fff;\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n}\n.btn-outline-info:not(:disabled):not(.disabled):active:focus, .btn-outline-info:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-info.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);\n}\n\n.btn-outline-warning {\n  color: #ffc107;\n  border-color: #ffc107;\n}\n.btn-outline-warning:hover {\n  color: #212529;\n  background-color: #ffc107;\n  border-color: #ffc107;\n}\n.btn-outline-warning:focus, .btn-outline-warning.focus {\n  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);\n}\n.btn-outline-warning.disabled, .btn-outline-warning:disabled {\n  color: #ffc107;\n  background-color: transparent;\n}\n.btn-outline-warning:not(:disabled):not(.disabled):active, .btn-outline-warning:not(:disabled):not(.disabled).active, .show > .btn-outline-warning.dropdown-toggle {\n  color: #212529;\n  background-color: #ffc107;\n  border-color: #ffc107;\n}\n.btn-outline-warning:not(:disabled):not(.disabled):active:focus, .btn-outline-warning:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-warning.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);\n}\n\n.btn-outline-danger {\n  color: #dc3545;\n  border-color: #dc3545;\n}\n.btn-outline-danger:hover {\n  color: #fff;\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n.btn-outline-danger:focus, .btn-outline-danger.focus {\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);\n}\n.btn-outline-danger.disabled, .btn-outline-danger:disabled {\n  color: #dc3545;\n  background-color: transparent;\n}\n.btn-outline-danger:not(:disabled):not(.disabled):active, .btn-outline-danger:not(:disabled):not(.disabled).active, .show > .btn-outline-danger.dropdown-toggle {\n  color: #fff;\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n.btn-outline-danger:not(:disabled):not(.disabled):active:focus, .btn-outline-danger:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-danger.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);\n}\n\n.btn-outline-light {\n  color: #f8f9fa;\n  border-color: #f8f9fa;\n}\n.btn-outline-light:hover {\n  color: #212529;\n  background-color: #f8f9fa;\n  border-color: #f8f9fa;\n}\n.btn-outline-light:focus, .btn-outline-light.focus {\n  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);\n}\n.btn-outline-light.disabled, .btn-outline-light:disabled {\n  color: #f8f9fa;\n  background-color: transparent;\n}\n.btn-outline-light:not(:disabled):not(.disabled):active, .btn-outline-light:not(:disabled):not(.disabled).active, .show > .btn-outline-light.dropdown-toggle {\n  color: #212529;\n  background-color: #f8f9fa;\n  border-color: #f8f9fa;\n}\n.btn-outline-light:not(:disabled):not(.disabled):active:focus, .btn-outline-light:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-light.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);\n}\n\n.btn-outline-dark {\n  color: #343a40;\n  border-color: #343a40;\n}\n.btn-outline-dark:hover {\n  color: #fff;\n  background-color: #343a40;\n  border-color: #343a40;\n}\n.btn-outline-dark:focus, .btn-outline-dark.focus {\n  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);\n}\n.btn-outline-dark.disabled, .btn-outline-dark:disabled {\n  color: #343a40;\n  background-color: transparent;\n}\n.btn-outline-dark:not(:disabled):not(.disabled):active, .btn-outline-dark:not(:disabled):not(.disabled).active, .show > .btn-outline-dark.dropdown-toggle {\n  color: #fff;\n  background-color: #343a40;\n  border-color: #343a40;\n}\n.btn-outline-dark:not(:disabled):not(.disabled):active:focus, .btn-outline-dark:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-dark.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);\n}\n\n.btn-link {\n  font-weight: 400;\n  color: #007bff;\n  text-decoration: none;\n}\n.btn-link:hover {\n  color: #0056b3;\n  text-decoration: underline;\n}\n.btn-link:focus, .btn-link.focus {\n  text-decoration: underline;\n  box-shadow: none;\n}\n.btn-link:disabled, .btn-link.disabled {\n  color: #6c757d;\n  pointer-events: none;\n}\n\n.btn-lg, .btn-group-lg > .btn {\n  padding: 0.5rem 1rem;\n  font-size: 1.25rem;\n  line-height: 1.5;\n  border-radius: 0.3rem;\n}\n\n.btn-sm, .btn-group-sm > .btn {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  border-radius: 0.2rem;\n}\n\n.btn-block {\n  display: block;\n  width: 100%;\n}\n.btn-block + .btn-block {\n  margin-top: 0.5rem;\n}\n\ninput[type=submit].btn-block,\ninput[type=reset].btn-block,\ninput[type=button].btn-block {\n  width: 100%;\n}\n\n.fade {\n  transition: opacity 0.15s linear;\n}\n@media (prefers-reduced-motion: reduce) {\n  .fade {\n    transition: none;\n  }\n}\n.fade:not(.show) {\n  opacity: 0;\n}\n\n.collapse:not(.show) {\n  display: none;\n}\n\n.collapsing {\n  position: relative;\n  height: 0;\n  overflow: hidden;\n  transition: height 0.35s ease;\n}\n@media (prefers-reduced-motion: reduce) {\n  .collapsing {\n    transition: none;\n  }\n}\n\n.dropup,\n.dropright,\n.dropdown,\n.dropleft {\n  position: relative;\n}\n\n.dropdown-toggle {\n  white-space: nowrap;\n}\n.dropdown-toggle::after {\n  display: inline-block;\n  margin-left: 0.255em;\n  vertical-align: 0.255em;\n  content: \"\";\n  border-top: 0.3em solid;\n  border-right: 0.3em solid transparent;\n  border-bottom: 0;\n  border-left: 0.3em solid transparent;\n}\n.dropdown-toggle:empty::after {\n  margin-left: 0;\n}\n\n.dropdown-menu {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  z-index: 1000;\n  display: none;\n  float: left;\n  min-width: 10rem;\n  padding: 0.5rem 0;\n  margin: 0.125rem 0 0;\n  font-size: 1rem;\n  color: #212529;\n  text-align: left;\n  list-style: none;\n  background-color: #fff;\n  background-clip: padding-box;\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  border-radius: 0.25rem;\n}\n\n.dropdown-menu-left {\n  right: auto;\n  left: 0;\n}\n\n.dropdown-menu-right {\n  right: 0;\n  left: auto;\n}\n\n@media (min-width: 576px) {\n  .dropdown-menu-sm-left {\n    right: auto;\n    left: 0;\n  }\n  .dropdown-menu-sm-right {\n    right: 0;\n    left: auto;\n  }\n}\n@media (min-width: 768px) {\n  .dropdown-menu-md-left {\n    right: auto;\n    left: 0;\n  }\n  .dropdown-menu-md-right {\n    right: 0;\n    left: auto;\n  }\n}\n@media (min-width: 992px) {\n  .dropdown-menu-lg-left {\n    right: auto;\n    left: 0;\n  }\n  .dropdown-menu-lg-right {\n    right: 0;\n    left: auto;\n  }\n}\n@media (min-width: 1200px) {\n  .dropdown-menu-xl-left {\n    right: auto;\n    left: 0;\n  }\n  .dropdown-menu-xl-right {\n    right: 0;\n    left: auto;\n  }\n}\n.dropup .dropdown-menu {\n  top: auto;\n  bottom: 100%;\n  margin-top: 0;\n  margin-bottom: 0.125rem;\n}\n.dropup .dropdown-toggle::after {\n  display: inline-block;\n  margin-left: 0.255em;\n  vertical-align: 0.255em;\n  content: \"\";\n  border-top: 0;\n  border-right: 0.3em solid transparent;\n  border-bottom: 0.3em solid;\n  border-left: 0.3em solid transparent;\n}\n.dropup .dropdown-toggle:empty::after {\n  margin-left: 0;\n}\n\n.dropright .dropdown-menu {\n  top: 0;\n  right: auto;\n  left: 100%;\n  margin-top: 0;\n  margin-left: 0.125rem;\n}\n.dropright .dropdown-toggle::after {\n  display: inline-block;\n  margin-left: 0.255em;\n  vertical-align: 0.255em;\n  content: \"\";\n  border-top: 0.3em solid transparent;\n  border-right: 0;\n  border-bottom: 0.3em solid transparent;\n  border-left: 0.3em solid;\n}\n.dropright .dropdown-toggle:empty::after {\n  margin-left: 0;\n}\n.dropright .dropdown-toggle::after {\n  vertical-align: 0;\n}\n\n.dropleft .dropdown-menu {\n  top: 0;\n  right: 100%;\n  left: auto;\n  margin-top: 0;\n  margin-right: 0.125rem;\n}\n.dropleft .dropdown-toggle::after {\n  display: inline-block;\n  margin-left: 0.255em;\n  vertical-align: 0.255em;\n  content: \"\";\n}\n.dropleft .dropdown-toggle::after {\n  display: none;\n}\n.dropleft .dropdown-toggle::before {\n  display: inline-block;\n  margin-right: 0.255em;\n  vertical-align: 0.255em;\n  content: \"\";\n  border-top: 0.3em solid transparent;\n  border-right: 0.3em solid;\n  border-bottom: 0.3em solid transparent;\n}\n.dropleft .dropdown-toggle:empty::after {\n  margin-left: 0;\n}\n.dropleft .dropdown-toggle::before {\n  vertical-align: 0;\n}\n\n.dropdown-menu[x-placement^=top], .dropdown-menu[x-placement^=right], .dropdown-menu[x-placement^=bottom], .dropdown-menu[x-placement^=left] {\n  right: auto;\n  bottom: auto;\n}\n\n.dropdown-divider {\n  height: 0;\n  margin: 0.5rem 0;\n  overflow: hidden;\n  border-top: 1px solid #e9ecef;\n}\n\n.dropdown-item {\n  display: block;\n  width: 100%;\n  padding: 0.25rem 1.5rem;\n  clear: both;\n  font-weight: 400;\n  color: #212529;\n  text-align: inherit;\n  white-space: nowrap;\n  background-color: transparent;\n  border: 0;\n}\n.dropdown-item:hover, .dropdown-item:focus {\n  color: #16181b;\n  text-decoration: none;\n  background-color: #f8f9fa;\n}\n.dropdown-item.active, .dropdown-item:active {\n  color: #fff;\n  text-decoration: none;\n  background-color: #007bff;\n}\n.dropdown-item.disabled, .dropdown-item:disabled {\n  color: #6c757d;\n  pointer-events: none;\n  background-color: transparent;\n}\n\n.dropdown-menu.show {\n  display: block;\n}\n\n.dropdown-header {\n  display: block;\n  padding: 0.5rem 1.5rem;\n  margin-bottom: 0;\n  font-size: 0.875rem;\n  color: #6c757d;\n  white-space: nowrap;\n}\n\n.dropdown-item-text {\n  display: block;\n  padding: 0.25rem 1.5rem;\n  color: #212529;\n}\n\n.btn-group,\n.btn-group-vertical {\n  position: relative;\n  display: inline-flex;\n  vertical-align: middle;\n}\n.btn-group > .btn,\n.btn-group-vertical > .btn {\n  position: relative;\n  flex: 1 1 auto;\n}\n.btn-group > .btn:hover,\n.btn-group-vertical > .btn:hover {\n  z-index: 1;\n}\n.btn-group > .btn:focus, .btn-group > .btn:active, .btn-group > .btn.active,\n.btn-group-vertical > .btn:focus,\n.btn-group-vertical > .btn:active,\n.btn-group-vertical > .btn.active {\n  z-index: 1;\n}\n\n.btn-toolbar {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n}\n.btn-toolbar .input-group {\n  width: auto;\n}\n\n.btn-group > .btn:not(:first-child),\n.btn-group > .btn-group:not(:first-child) {\n  margin-left: -1px;\n}\n.btn-group > .btn:not(:last-child):not(.dropdown-toggle),\n.btn-group > .btn-group:not(:last-child) > .btn {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n.btn-group > .btn:not(:first-child),\n.btn-group > .btn-group:not(:first-child) > .btn {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.dropdown-toggle-split {\n  padding-right: 0.5625rem;\n  padding-left: 0.5625rem;\n}\n.dropdown-toggle-split::after, .dropup .dropdown-toggle-split::after, .dropright .dropdown-toggle-split::after {\n  margin-left: 0;\n}\n.dropleft .dropdown-toggle-split::before {\n  margin-right: 0;\n}\n\n.btn-sm + .dropdown-toggle-split, .btn-group-sm > .btn + .dropdown-toggle-split {\n  padding-right: 0.375rem;\n  padding-left: 0.375rem;\n}\n\n.btn-lg + .dropdown-toggle-split, .btn-group-lg > .btn + .dropdown-toggle-split {\n  padding-right: 0.75rem;\n  padding-left: 0.75rem;\n}\n\n.btn-group-vertical {\n  flex-direction: column;\n  align-items: flex-start;\n  justify-content: center;\n}\n.btn-group-vertical > .btn,\n.btn-group-vertical > .btn-group {\n  width: 100%;\n}\n.btn-group-vertical > .btn:not(:first-child),\n.btn-group-vertical > .btn-group:not(:first-child) {\n  margin-top: -1px;\n}\n.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle),\n.btn-group-vertical > .btn-group:not(:last-child) > .btn {\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n}\n.btn-group-vertical > .btn:not(:first-child),\n.btn-group-vertical > .btn-group:not(:first-child) > .btn {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n\n.btn-group-toggle > .btn,\n.btn-group-toggle > .btn-group > .btn {\n  margin-bottom: 0;\n}\n.btn-group-toggle > .btn input[type=radio],\n.btn-group-toggle > .btn input[type=checkbox],\n.btn-group-toggle > .btn-group > .btn input[type=radio],\n.btn-group-toggle > .btn-group > .btn input[type=checkbox] {\n  position: absolute;\n  clip: rect(0, 0, 0, 0);\n  pointer-events: none;\n}\n\n.input-group {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: stretch;\n  width: 100%;\n}\n.input-group > .form-control,\n.input-group > .form-control-plaintext,\n.input-group > .custom-select,\n.input-group > .custom-file {\n  position: relative;\n  flex: 1 1 auto;\n  width: 1%;\n  margin-bottom: 0;\n}\n.input-group > .form-control + .form-control,\n.input-group > .form-control + .custom-select,\n.input-group > .form-control + .custom-file,\n.input-group > .form-control-plaintext + .form-control,\n.input-group > .form-control-plaintext + .custom-select,\n.input-group > .form-control-plaintext + .custom-file,\n.input-group > .custom-select + .form-control,\n.input-group > .custom-select + .custom-select,\n.input-group > .custom-select + .custom-file,\n.input-group > .custom-file + .form-control,\n.input-group > .custom-file + .custom-select,\n.input-group > .custom-file + .custom-file {\n  margin-left: -1px;\n}\n.input-group > .form-control:focus,\n.input-group > .custom-select:focus,\n.input-group > .custom-file .custom-file-input:focus ~ .custom-file-label {\n  z-index: 3;\n}\n.input-group > .custom-file .custom-file-input:focus {\n  z-index: 4;\n}\n.input-group > .form-control:not(:last-child),\n.input-group > .custom-select:not(:last-child) {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n.input-group > .form-control:not(:first-child),\n.input-group > .custom-select:not(:first-child) {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n.input-group > .custom-file {\n  display: flex;\n  align-items: center;\n}\n.input-group > .custom-file:not(:last-child) .custom-file-label, .input-group > .custom-file:not(:last-child) .custom-file-label::after {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n.input-group > .custom-file:not(:first-child) .custom-file-label {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.input-group-prepend,\n.input-group-append {\n  display: flex;\n}\n.input-group-prepend .btn,\n.input-group-append .btn {\n  position: relative;\n  z-index: 2;\n}\n.input-group-prepend .btn:focus,\n.input-group-append .btn:focus {\n  z-index: 3;\n}\n.input-group-prepend .btn + .btn,\n.input-group-prepend .btn + .input-group-text,\n.input-group-prepend .input-group-text + .input-group-text,\n.input-group-prepend .input-group-text + .btn,\n.input-group-append .btn + .btn,\n.input-group-append .btn + .input-group-text,\n.input-group-append .input-group-text + .input-group-text,\n.input-group-append .input-group-text + .btn {\n  margin-left: -1px;\n}\n\n.input-group-prepend {\n  margin-right: -1px;\n}\n\n.input-group-append {\n  margin-left: -1px;\n}\n\n.input-group-text {\n  display: flex;\n  align-items: center;\n  padding: 0.375rem 0.75rem;\n  margin-bottom: 0;\n  font-size: 1rem;\n  font-weight: 400;\n  line-height: 1.5;\n  color: #495057;\n  text-align: center;\n  white-space: nowrap;\n  background-color: #e9ecef;\n  border: 1px solid #ced4da;\n  border-radius: 0.25rem;\n}\n.input-group-text input[type=radio],\n.input-group-text input[type=checkbox] {\n  margin-top: 0;\n}\n\n.input-group-lg > .form-control:not(textarea),\n.input-group-lg > .custom-select {\n  height: calc(1.5em + 1rem + 2px);\n}\n\n.input-group-lg > .form-control,\n.input-group-lg > .custom-select,\n.input-group-lg > .input-group-prepend > .input-group-text,\n.input-group-lg > .input-group-append > .input-group-text,\n.input-group-lg > .input-group-prepend > .btn,\n.input-group-lg > .input-group-append > .btn {\n  padding: 0.5rem 1rem;\n  font-size: 1.25rem;\n  line-height: 1.5;\n  border-radius: 0.3rem;\n}\n\n.input-group-sm > .form-control:not(textarea),\n.input-group-sm > .custom-select {\n  height: calc(1.5em + 0.5rem + 2px);\n}\n\n.input-group-sm > .form-control,\n.input-group-sm > .custom-select,\n.input-group-sm > .input-group-prepend > .input-group-text,\n.input-group-sm > .input-group-append > .input-group-text,\n.input-group-sm > .input-group-prepend > .btn,\n.input-group-sm > .input-group-append > .btn {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  border-radius: 0.2rem;\n}\n\n.input-group-lg > .custom-select,\n.input-group-sm > .custom-select {\n  padding-right: 1.75rem;\n}\n\n.input-group > .input-group-prepend > .btn,\n.input-group > .input-group-prepend > .input-group-text,\n.input-group > .input-group-append:not(:last-child) > .btn,\n.input-group > .input-group-append:not(:last-child) > .input-group-text,\n.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),\n.input-group > .input-group-append:last-child > .input-group-text:not(:last-child) {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n.input-group > .input-group-append > .btn,\n.input-group > .input-group-append > .input-group-text,\n.input-group > .input-group-prepend:not(:first-child) > .btn,\n.input-group > .input-group-prepend:not(:first-child) > .input-group-text,\n.input-group > .input-group-prepend:first-child > .btn:not(:first-child),\n.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child) {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.custom-control {\n  position: relative;\n  display: block;\n  min-height: 1.5rem;\n  padding-left: 1.5rem;\n}\n\n.custom-control-inline {\n  display: inline-flex;\n  margin-right: 1rem;\n}\n\n.custom-control-input {\n  position: absolute;\n  z-index: -1;\n  opacity: 0;\n}\n.custom-control-input:checked ~ .custom-control-label::before {\n  color: #fff;\n  border-color: #007bff;\n  background-color: #007bff;\n}\n.custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n.custom-control-input:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #80bdff;\n}\n.custom-control-input:not(:disabled):active ~ .custom-control-label::before {\n  color: #fff;\n  background-color: #b3d7ff;\n  border-color: #b3d7ff;\n}\n.custom-control-input:disabled ~ .custom-control-label {\n  color: #6c757d;\n}\n.custom-control-input:disabled ~ .custom-control-label::before {\n  background-color: #e9ecef;\n}\n\n.custom-control-label {\n  position: relative;\n  margin-bottom: 0;\n  vertical-align: top;\n}\n.custom-control-label::before {\n  position: absolute;\n  top: 0.25rem;\n  left: -1.5rem;\n  display: block;\n  width: 1rem;\n  height: 1rem;\n  pointer-events: none;\n  content: \"\";\n  background-color: #fff;\n  border: #adb5bd solid 1px;\n}\n.custom-control-label::after {\n  position: absolute;\n  top: 0.25rem;\n  left: -1.5rem;\n  display: block;\n  width: 1rem;\n  height: 1rem;\n  content: \"\";\n  background: no-repeat 50%/50% 50%;\n}\n\n.custom-checkbox .custom-control-label::before {\n  border-radius: 0.25rem;\n}\n.custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3e%3c/svg%3e\");\n}\n.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::before {\n  border-color: #007bff;\n  background-color: #007bff;\n}\n.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3e%3cpath stroke='%23fff' d='M0 2h4'/%3e%3c/svg%3e\");\n}\n.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before {\n  background-color: rgba(0, 123, 255, 0.5);\n}\n.custom-checkbox .custom-control-input:disabled:indeterminate ~ .custom-control-label::before {\n  background-color: rgba(0, 123, 255, 0.5);\n}\n\n.custom-radio .custom-control-label::before {\n  border-radius: 50%;\n}\n.custom-radio .custom-control-input:checked ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e\");\n}\n.custom-radio .custom-control-input:disabled:checked ~ .custom-control-label::before {\n  background-color: rgba(0, 123, 255, 0.5);\n}\n\n.custom-switch {\n  padding-left: 2.25rem;\n}\n.custom-switch .custom-control-label::before {\n  left: -2.25rem;\n  width: 1.75rem;\n  pointer-events: all;\n  border-radius: 0.5rem;\n}\n.custom-switch .custom-control-label::after {\n  top: calc(0.25rem + 2px);\n  left: calc(-2.25rem + 2px);\n  width: calc(1rem - 4px);\n  height: calc(1rem - 4px);\n  background-color: #adb5bd;\n  border-radius: 0.5rem;\n  transition: transform 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n@media (prefers-reduced-motion: reduce) {\n  .custom-switch .custom-control-label::after {\n    transition: none;\n  }\n}\n.custom-switch .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #fff;\n  transform: translateX(0.75rem);\n}\n.custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {\n  background-color: rgba(0, 123, 255, 0.5);\n}\n\n.custom-select {\n  display: inline-block;\n  width: 100%;\n  height: calc(1.5em + 0.75rem + 2px);\n  padding: 0.375rem 1.75rem 0.375rem 0.75rem;\n  font-size: 1rem;\n  font-weight: 400;\n  line-height: 1.5;\n  color: #495057;\n  vertical-align: middle;\n  background: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e\") no-repeat right 0.75rem center/8px 10px;\n  background-color: #fff;\n  border: 1px solid #ced4da;\n  border-radius: 0.25rem;\n  appearance: none;\n}\n.custom-select:focus {\n  border-color: #80bdff;\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n.custom-select:focus::-ms-value {\n  color: #495057;\n  background-color: #fff;\n}\n.custom-select[multiple], .custom-select[size]:not([size=\"1\"]) {\n  height: auto;\n  padding-right: 0.75rem;\n  background-image: none;\n}\n.custom-select:disabled {\n  color: #6c757d;\n  background-color: #e9ecef;\n}\n.custom-select::-ms-expand {\n  display: none;\n}\n\n.custom-select-sm {\n  height: calc(1.5em + 0.5rem + 2px);\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n  padding-left: 0.5rem;\n  font-size: 0.875rem;\n}\n\n.custom-select-lg {\n  height: calc(1.5em + 1rem + 2px);\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n  padding-left: 1rem;\n  font-size: 1.25rem;\n}\n\n.custom-file {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  height: calc(1.5em + 0.75rem + 2px);\n  margin-bottom: 0;\n}\n\n.custom-file-input {\n  position: relative;\n  z-index: 2;\n  width: 100%;\n  height: calc(1.5em + 0.75rem + 2px);\n  margin: 0;\n  opacity: 0;\n}\n.custom-file-input:focus ~ .custom-file-label {\n  border-color: #80bdff;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n.custom-file-input:disabled ~ .custom-file-label {\n  background-color: #e9ecef;\n}\n.custom-file-input:lang(en) ~ .custom-file-label::after {\n  content: \"Browse\";\n}\n.custom-file-input ~ .custom-file-label[data-browse]::after {\n  content: attr(data-browse);\n}\n\n.custom-file-label {\n  position: absolute;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1;\n  height: calc(1.5em + 0.75rem + 2px);\n  padding: 0.375rem 0.75rem;\n  font-weight: 400;\n  line-height: 1.5;\n  color: #495057;\n  background-color: #fff;\n  border: 1px solid #ced4da;\n  border-radius: 0.25rem;\n}\n.custom-file-label::after {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 3;\n  display: block;\n  height: calc(1.5em + 0.75rem);\n  padding: 0.375rem 0.75rem;\n  line-height: 1.5;\n  color: #495057;\n  content: \"Browse\";\n  background-color: #e9ecef;\n  border-left: inherit;\n  border-radius: 0 0.25rem 0.25rem 0;\n}\n\n.custom-range {\n  width: 100%;\n  height: calc(1rem + 0.4rem);\n  padding: 0;\n  background-color: transparent;\n  appearance: none;\n}\n.custom-range:focus {\n  outline: none;\n}\n.custom-range:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n.custom-range:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n.custom-range:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n.custom-range::-moz-focus-outer {\n  border: 0;\n}\n.custom-range::-webkit-slider-thumb {\n  width: 1rem;\n  height: 1rem;\n  margin-top: -0.25rem;\n  background-color: #007bff;\n  border: 0;\n  border-radius: 1rem;\n  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n  appearance: none;\n}\n@media (prefers-reduced-motion: reduce) {\n  .custom-range::-webkit-slider-thumb {\n    transition: none;\n  }\n}\n.custom-range::-webkit-slider-thumb:active {\n  background-color: #b3d7ff;\n}\n.custom-range::-webkit-slider-runnable-track {\n  width: 100%;\n  height: 0.5rem;\n  color: transparent;\n  cursor: pointer;\n  background-color: #dee2e6;\n  border-color: transparent;\n  border-radius: 1rem;\n}\n.custom-range::-moz-range-thumb {\n  width: 1rem;\n  height: 1rem;\n  background-color: #007bff;\n  border: 0;\n  border-radius: 1rem;\n  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n  appearance: none;\n}\n@media (prefers-reduced-motion: reduce) {\n  .custom-range::-moz-range-thumb {\n    transition: none;\n  }\n}\n.custom-range::-moz-range-thumb:active {\n  background-color: #b3d7ff;\n}\n.custom-range::-moz-range-track {\n  width: 100%;\n  height: 0.5rem;\n  color: transparent;\n  cursor: pointer;\n  background-color: #dee2e6;\n  border-color: transparent;\n  border-radius: 1rem;\n}\n.custom-range::-ms-thumb {\n  width: 1rem;\n  height: 1rem;\n  margin-top: 0;\n  margin-right: 0.2rem;\n  margin-left: 0.2rem;\n  background-color: #007bff;\n  border: 0;\n  border-radius: 1rem;\n  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n  appearance: none;\n}\n@media (prefers-reduced-motion: reduce) {\n  .custom-range::-ms-thumb {\n    transition: none;\n  }\n}\n.custom-range::-ms-thumb:active {\n  background-color: #b3d7ff;\n}\n.custom-range::-ms-track {\n  width: 100%;\n  height: 0.5rem;\n  color: transparent;\n  cursor: pointer;\n  background-color: transparent;\n  border-color: transparent;\n  border-width: 0.5rem;\n}\n.custom-range::-ms-fill-lower {\n  background-color: #dee2e6;\n  border-radius: 1rem;\n}\n.custom-range::-ms-fill-upper {\n  margin-right: 15px;\n  background-color: #dee2e6;\n  border-radius: 1rem;\n}\n.custom-range:disabled::-webkit-slider-thumb {\n  background-color: #adb5bd;\n}\n.custom-range:disabled::-webkit-slider-runnable-track {\n  cursor: default;\n}\n.custom-range:disabled::-moz-range-thumb {\n  background-color: #adb5bd;\n}\n.custom-range:disabled::-moz-range-track {\n  cursor: default;\n}\n.custom-range:disabled::-ms-thumb {\n  background-color: #adb5bd;\n}\n\n.custom-control-label::before,\n.custom-file-label,\n.custom-select {\n  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n@media (prefers-reduced-motion: reduce) {\n  .custom-control-label::before,\n  .custom-file-label,\n  .custom-select {\n    transition: none;\n  }\n}\n\n.nav {\n  display: flex;\n  flex-wrap: wrap;\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n}\n\n.nav-link {\n  display: block;\n  padding: 0.5rem 1rem;\n}\n.nav-link:hover, .nav-link:focus {\n  text-decoration: none;\n}\n.nav-link.disabled {\n  color: #6c757d;\n  pointer-events: none;\n  cursor: default;\n}\n\n.nav-tabs {\n  border-bottom: 1px solid #dee2e6;\n}\n.nav-tabs .nav-item {\n  margin-bottom: -1px;\n}\n.nav-tabs .nav-link {\n  border: 1px solid transparent;\n  border-top-left-radius: 0.25rem;\n  border-top-right-radius: 0.25rem;\n}\n.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {\n  border-color: #e9ecef #e9ecef #dee2e6;\n}\n.nav-tabs .nav-link.disabled {\n  color: #6c757d;\n  background-color: transparent;\n  border-color: transparent;\n}\n.nav-tabs .nav-link.active,\n.nav-tabs .nav-item.show .nav-link {\n  color: #495057;\n  background-color: #fff;\n  border-color: #dee2e6 #dee2e6 #fff;\n}\n.nav-tabs .dropdown-menu {\n  margin-top: -1px;\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n\n.nav-pills .nav-link {\n  border-radius: 0.25rem;\n}\n.nav-pills .nav-link.active,\n.nav-pills .show > .nav-link {\n  color: #fff;\n  background-color: #007bff;\n}\n\n.nav-fill .nav-item {\n  flex: 1 1 auto;\n  text-align: center;\n}\n\n.nav-justified .nav-item {\n  flex-basis: 0;\n  flex-grow: 1;\n  text-align: center;\n}\n\n.tab-content > .tab-pane {\n  display: none;\n}\n.tab-content > .active {\n  display: block;\n}\n\n.navbar {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0.5rem 1rem;\n}\n.navbar > .container,\n.navbar > .container-fluid {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.navbar-brand {\n  display: inline-block;\n  padding-top: 0.3125rem;\n  padding-bottom: 0.3125rem;\n  margin-right: 1rem;\n  font-size: 1.25rem;\n  line-height: inherit;\n  white-space: nowrap;\n}\n.navbar-brand:hover, .navbar-brand:focus {\n  text-decoration: none;\n}\n\n.navbar-nav {\n  display: flex;\n  flex-direction: column;\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n}\n.navbar-nav .nav-link {\n  padding-right: 0;\n  padding-left: 0;\n}\n.navbar-nav .dropdown-menu {\n  position: static;\n  float: none;\n}\n\n.navbar-text {\n  display: inline-block;\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\n\n.navbar-collapse {\n  flex-basis: 100%;\n  flex-grow: 1;\n  align-items: center;\n}\n\n.navbar-toggler {\n  padding: 0.25rem 0.75rem;\n  font-size: 1.25rem;\n  line-height: 1;\n  background-color: transparent;\n  border: 1px solid transparent;\n  border-radius: 0.25rem;\n}\n.navbar-toggler:hover, .navbar-toggler:focus {\n  text-decoration: none;\n}\n\n.navbar-toggler-icon {\n  display: inline-block;\n  width: 1.5em;\n  height: 1.5em;\n  vertical-align: middle;\n  content: \"\";\n  background: no-repeat center center;\n  background-size: 100% 100%;\n}\n\n@media (max-width: 575.98px) {\n  .navbar-expand-sm > .container,\n  .navbar-expand-sm > .container-fluid {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n@media (min-width: 576px) {\n  .navbar-expand-sm {\n    flex-flow: row nowrap;\n    justify-content: flex-start;\n  }\n  .navbar-expand-sm .navbar-nav {\n    flex-direction: row;\n  }\n  .navbar-expand-sm .navbar-nav .dropdown-menu {\n    position: absolute;\n  }\n  .navbar-expand-sm .navbar-nav .nav-link {\n    padding-right: 0.5rem;\n    padding-left: 0.5rem;\n  }\n  .navbar-expand-sm > .container,\n  .navbar-expand-sm > .container-fluid {\n    flex-wrap: nowrap;\n  }\n  .navbar-expand-sm .navbar-collapse {\n    display: flex !important;\n    flex-basis: auto;\n  }\n  .navbar-expand-sm .navbar-toggler {\n    display: none;\n  }\n}\n@media (max-width: 767.98px) {\n  .navbar-expand-md > .container,\n  .navbar-expand-md > .container-fluid {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n@media (min-width: 768px) {\n  .navbar-expand-md {\n    flex-flow: row nowrap;\n    justify-content: flex-start;\n  }\n  .navbar-expand-md .navbar-nav {\n    flex-direction: row;\n  }\n  .navbar-expand-md .navbar-nav .dropdown-menu {\n    position: absolute;\n  }\n  .navbar-expand-md .navbar-nav .nav-link {\n    padding-right: 0.5rem;\n    padding-left: 0.5rem;\n  }\n  .navbar-expand-md > .container,\n  .navbar-expand-md > .container-fluid {\n    flex-wrap: nowrap;\n  }\n  .navbar-expand-md .navbar-collapse {\n    display: flex !important;\n    flex-basis: auto;\n  }\n  .navbar-expand-md .navbar-toggler {\n    display: none;\n  }\n}\n@media (max-width: 991.98px) {\n  .navbar-expand-lg > .container,\n  .navbar-expand-lg > .container-fluid {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n@media (min-width: 992px) {\n  .navbar-expand-lg {\n    flex-flow: row nowrap;\n    justify-content: flex-start;\n  }\n  .navbar-expand-lg .navbar-nav {\n    flex-direction: row;\n  }\n  .navbar-expand-lg .navbar-nav .dropdown-menu {\n    position: absolute;\n  }\n  .navbar-expand-lg .navbar-nav .nav-link {\n    padding-right: 0.5rem;\n    padding-left: 0.5rem;\n  }\n  .navbar-expand-lg > .container,\n  .navbar-expand-lg > .container-fluid {\n    flex-wrap: nowrap;\n  }\n  .navbar-expand-lg .navbar-collapse {\n    display: flex !important;\n    flex-basis: auto;\n  }\n  .navbar-expand-lg .navbar-toggler {\n    display: none;\n  }\n}\n@media (max-width: 1199.98px) {\n  .navbar-expand-xl > .container,\n  .navbar-expand-xl > .container-fluid {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n@media (min-width: 1200px) {\n  .navbar-expand-xl {\n    flex-flow: row nowrap;\n    justify-content: flex-start;\n  }\n  .navbar-expand-xl .navbar-nav {\n    flex-direction: row;\n  }\n  .navbar-expand-xl .navbar-nav .dropdown-menu {\n    position: absolute;\n  }\n  .navbar-expand-xl .navbar-nav .nav-link {\n    padding-right: 0.5rem;\n    padding-left: 0.5rem;\n  }\n  .navbar-expand-xl > .container,\n  .navbar-expand-xl > .container-fluid {\n    flex-wrap: nowrap;\n  }\n  .navbar-expand-xl .navbar-collapse {\n    display: flex !important;\n    flex-basis: auto;\n  }\n  .navbar-expand-xl .navbar-toggler {\n    display: none;\n  }\n}\n.navbar-expand {\n  flex-flow: row nowrap;\n  justify-content: flex-start;\n}\n.navbar-expand > .container,\n.navbar-expand > .container-fluid {\n  padding-right: 0;\n  padding-left: 0;\n}\n.navbar-expand .navbar-nav {\n  flex-direction: row;\n}\n.navbar-expand .navbar-nav .dropdown-menu {\n  position: absolute;\n}\n.navbar-expand .navbar-nav .nav-link {\n  padding-right: 0.5rem;\n  padding-left: 0.5rem;\n}\n.navbar-expand > .container,\n.navbar-expand > .container-fluid {\n  flex-wrap: nowrap;\n}\n.navbar-expand .navbar-collapse {\n  display: flex !important;\n  flex-basis: auto;\n}\n.navbar-expand .navbar-toggler {\n  display: none;\n}\n\n.navbar-light .navbar-brand {\n  color: rgba(0, 0, 0, 0.9);\n}\n.navbar-light .navbar-brand:hover, .navbar-light .navbar-brand:focus {\n  color: rgba(0, 0, 0, 0.9);\n}\n.navbar-light .navbar-nav .nav-link {\n  color: rgba(0, 0, 0, 0.5);\n}\n.navbar-light .navbar-nav .nav-link:hover, .navbar-light .navbar-nav .nav-link:focus {\n  color: rgba(0, 0, 0, 0.7);\n}\n.navbar-light .navbar-nav .nav-link.disabled {\n  color: rgba(0, 0, 0, 0.3);\n}\n.navbar-light .navbar-nav .show > .nav-link,\n.navbar-light .navbar-nav .active > .nav-link,\n.navbar-light .navbar-nav .nav-link.show,\n.navbar-light .navbar-nav .nav-link.active {\n  color: rgba(0, 0, 0, 0.9);\n}\n.navbar-light .navbar-toggler {\n  color: rgba(0, 0, 0, 0.5);\n  border-color: rgba(0, 0, 0, 0.1);\n}\n.navbar-light .navbar-toggler-icon {\n  background-image: url(\"data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='rgba(0, 0, 0, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e\");\n}\n.navbar-light .navbar-text {\n  color: rgba(0, 0, 0, 0.5);\n}\n.navbar-light .navbar-text a {\n  color: rgba(0, 0, 0, 0.9);\n}\n.navbar-light .navbar-text a:hover, .navbar-light .navbar-text a:focus {\n  color: rgba(0, 0, 0, 0.9);\n}\n\n.navbar-dark .navbar-brand {\n  color: #fff;\n}\n.navbar-dark .navbar-brand:hover, .navbar-dark .navbar-brand:focus {\n  color: #fff;\n}\n.navbar-dark .navbar-nav .nav-link {\n  color: rgba(255, 255, 255, 0.5);\n}\n.navbar-dark .navbar-nav .nav-link:hover, .navbar-dark .navbar-nav .nav-link:focus {\n  color: rgba(255, 255, 255, 0.75);\n}\n.navbar-dark .navbar-nav .nav-link.disabled {\n  color: rgba(255, 255, 255, 0.25);\n}\n.navbar-dark .navbar-nav .show > .nav-link,\n.navbar-dark .navbar-nav .active > .nav-link,\n.navbar-dark .navbar-nav .nav-link.show,\n.navbar-dark .navbar-nav .nav-link.active {\n  color: #fff;\n}\n.navbar-dark .navbar-toggler {\n  color: rgba(255, 255, 255, 0.5);\n  border-color: rgba(255, 255, 255, 0.1);\n}\n.navbar-dark .navbar-toggler-icon {\n  background-image: url(\"data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='rgba(255, 255, 255, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e\");\n}\n.navbar-dark .navbar-text {\n  color: rgba(255, 255, 255, 0.5);\n}\n.navbar-dark .navbar-text a {\n  color: #fff;\n}\n.navbar-dark .navbar-text a:hover, .navbar-dark .navbar-text a:focus {\n  color: #fff;\n}\n\n.card {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  min-width: 0;\n  word-wrap: break-word;\n  background-color: #fff;\n  background-clip: border-box;\n  border: 1px solid rgba(0, 0, 0, 0.125);\n  border-radius: 0.25rem;\n}\n.card > hr {\n  margin-right: 0;\n  margin-left: 0;\n}\n.card > .list-group:first-child .list-group-item:first-child {\n  border-top-left-radius: 0.25rem;\n  border-top-right-radius: 0.25rem;\n}\n.card > .list-group:last-child .list-group-item:last-child {\n  border-bottom-right-radius: 0.25rem;\n  border-bottom-left-radius: 0.25rem;\n}\n\n.card-body {\n  flex: 1 1 auto;\n  padding: 1.25rem;\n}\n\n.card-title {\n  margin-bottom: 0.75rem;\n}\n\n.card-subtitle {\n  margin-top: -0.375rem;\n  margin-bottom: 0;\n}\n\n.card-text:last-child {\n  margin-bottom: 0;\n}\n\n.card-link:hover {\n  text-decoration: none;\n}\n.card-link + .card-link {\n  margin-left: 1.25rem;\n}\n\n.card-header {\n  padding: 0.75rem 1.25rem;\n  margin-bottom: 0;\n  background-color: rgba(0, 0, 0, 0.03);\n  border-bottom: 1px solid rgba(0, 0, 0, 0.125);\n}\n.card-header:first-child {\n  border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;\n}\n.card-header + .list-group .list-group-item:first-child {\n  border-top: 0;\n}\n\n.card-footer {\n  padding: 0.75rem 1.25rem;\n  background-color: rgba(0, 0, 0, 0.03);\n  border-top: 1px solid rgba(0, 0, 0, 0.125);\n}\n.card-footer:last-child {\n  border-radius: 0 0 calc(0.25rem - 1px) calc(0.25rem - 1px);\n}\n\n.card-header-tabs {\n  margin-right: -0.625rem;\n  margin-bottom: -0.75rem;\n  margin-left: -0.625rem;\n  border-bottom: 0;\n}\n\n.card-header-pills {\n  margin-right: -0.625rem;\n  margin-left: -0.625rem;\n}\n\n.card-img-overlay {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  padding: 1.25rem;\n}\n\n.card-img {\n  width: 100%;\n  border-radius: calc(0.25rem - 1px);\n}\n\n.card-img-top {\n  width: 100%;\n  border-top-left-radius: calc(0.25rem - 1px);\n  border-top-right-radius: calc(0.25rem - 1px);\n}\n\n.card-img-bottom {\n  width: 100%;\n  border-bottom-right-radius: calc(0.25rem - 1px);\n  border-bottom-left-radius: calc(0.25rem - 1px);\n}\n\n.card-deck {\n  display: flex;\n  flex-direction: column;\n}\n.card-deck .card {\n  margin-bottom: 15px;\n}\n@media (min-width: 576px) {\n  .card-deck {\n    flex-flow: row wrap;\n    margin-right: -15px;\n    margin-left: -15px;\n  }\n  .card-deck .card {\n    display: flex;\n    flex: 1 0 0%;\n    flex-direction: column;\n    margin-right: 15px;\n    margin-bottom: 0;\n    margin-left: 15px;\n  }\n}\n\n.card-group {\n  display: flex;\n  flex-direction: column;\n}\n.card-group > .card {\n  margin-bottom: 15px;\n}\n@media (min-width: 576px) {\n  .card-group {\n    flex-flow: row wrap;\n  }\n  .card-group > .card {\n    flex: 1 0 0%;\n    margin-bottom: 0;\n  }\n  .card-group > .card + .card {\n    margin-left: 0;\n    border-left: 0;\n  }\n  .card-group > .card:not(:last-child) {\n    border-top-right-radius: 0;\n    border-bottom-right-radius: 0;\n  }\n  .card-group > .card:not(:last-child) .card-img-top,\n  .card-group > .card:not(:last-child) .card-header {\n    border-top-right-radius: 0;\n  }\n  .card-group > .card:not(:last-child) .card-img-bottom,\n  .card-group > .card:not(:last-child) .card-footer {\n    border-bottom-right-radius: 0;\n  }\n  .card-group > .card:not(:first-child) {\n    border-top-left-radius: 0;\n    border-bottom-left-radius: 0;\n  }\n  .card-group > .card:not(:first-child) .card-img-top,\n  .card-group > .card:not(:first-child) .card-header {\n    border-top-left-radius: 0;\n  }\n  .card-group > .card:not(:first-child) .card-img-bottom,\n  .card-group > .card:not(:first-child) .card-footer {\n    border-bottom-left-radius: 0;\n  }\n}\n\n.card-columns .card {\n  margin-bottom: 0.75rem;\n}\n@media (min-width: 576px) {\n  .card-columns {\n    column-count: 3;\n    column-gap: 1.25rem;\n    orphans: 1;\n    widows: 1;\n  }\n  .card-columns .card {\n    display: inline-block;\n    width: 100%;\n  }\n}\n\n.accordion > .card {\n  overflow: hidden;\n}\n.accordion > .card:not(:first-of-type) .card-header:first-child {\n  border-radius: 0;\n}\n.accordion > .card:not(:first-of-type):not(:last-of-type) {\n  border-bottom: 0;\n  border-radius: 0;\n}\n.accordion > .card:first-of-type {\n  border-bottom: 0;\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n}\n.accordion > .card:last-of-type {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n.accordion > .card .card-header {\n  margin-bottom: -1px;\n}\n\n.breadcrumb {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 0.75rem 1rem;\n  margin-bottom: 1rem;\n  list-style: none;\n  background-color: #e9ecef;\n  border-radius: 0.25rem;\n}\n\n.breadcrumb-item + .breadcrumb-item {\n  padding-left: 0.5rem;\n}\n.breadcrumb-item + .breadcrumb-item::before {\n  display: inline-block;\n  padding-right: 0.5rem;\n  color: #6c757d;\n  content: \"/\";\n}\n.breadcrumb-item + .breadcrumb-item:hover::before {\n  text-decoration: underline;\n}\n.breadcrumb-item + .breadcrumb-item:hover::before {\n  text-decoration: none;\n}\n.breadcrumb-item.active {\n  color: #6c757d;\n}\n\n.pagination {\n  display: flex;\n  padding-left: 0;\n  list-style: none;\n  border-radius: 0.25rem;\n}\n\n.page-link {\n  position: relative;\n  display: block;\n  padding: 0.5rem 0.75rem;\n  margin-left: -1px;\n  line-height: 1.25;\n  color: #007bff;\n  background-color: #fff;\n  border: 1px solid #dee2e6;\n}\n.page-link:hover {\n  z-index: 2;\n  color: #0056b3;\n  text-decoration: none;\n  background-color: #e9ecef;\n  border-color: #dee2e6;\n}\n.page-link:focus {\n  z-index: 2;\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.page-item:first-child .page-link {\n  margin-left: 0;\n  border-top-left-radius: 0.25rem;\n  border-bottom-left-radius: 0.25rem;\n}\n.page-item:last-child .page-link {\n  border-top-right-radius: 0.25rem;\n  border-bottom-right-radius: 0.25rem;\n}\n.page-item.active .page-link {\n  z-index: 1;\n  color: #fff;\n  background-color: #007bff;\n  border-color: #007bff;\n}\n.page-item.disabled .page-link {\n  color: #6c757d;\n  pointer-events: none;\n  cursor: auto;\n  background-color: #fff;\n  border-color: #dee2e6;\n}\n\n.pagination-lg .page-link {\n  padding: 0.75rem 1.5rem;\n  font-size: 1.25rem;\n  line-height: 1.5;\n}\n.pagination-lg .page-item:first-child .page-link {\n  border-top-left-radius: 0.3rem;\n  border-bottom-left-radius: 0.3rem;\n}\n.pagination-lg .page-item:last-child .page-link {\n  border-top-right-radius: 0.3rem;\n  border-bottom-right-radius: 0.3rem;\n}\n\n.pagination-sm .page-link {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n}\n.pagination-sm .page-item:first-child .page-link {\n  border-top-left-radius: 0.2rem;\n  border-bottom-left-radius: 0.2rem;\n}\n.pagination-sm .page-item:last-child .page-link {\n  border-top-right-radius: 0.2rem;\n  border-bottom-right-radius: 0.2rem;\n}\n\n.badge {\n  display: inline-block;\n  padding: 0.25em 0.4em;\n  font-size: 75%;\n  font-weight: 700;\n  line-height: 1;\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: baseline;\n  border-radius: 0.25rem;\n  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n@media (prefers-reduced-motion: reduce) {\n  .badge {\n    transition: none;\n  }\n}\na.badge:hover, a.badge:focus {\n  text-decoration: none;\n}\n\n.badge:empty {\n  display: none;\n}\n\n.btn .badge {\n  position: relative;\n  top: -1px;\n}\n\n.badge-pill {\n  padding-right: 0.6em;\n  padding-left: 0.6em;\n  border-radius: 10rem;\n}\n\n.badge-primary {\n  color: #fff;\n  background-color: #007bff;\n}\na.badge-primary:hover, a.badge-primary:focus {\n  color: #fff;\n  background-color: #0062cc;\n}\na.badge-primary:focus, a.badge-primary.focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);\n}\n\n.badge-secondary {\n  color: #fff;\n  background-color: #6c757d;\n}\na.badge-secondary:hover, a.badge-secondary:focus {\n  color: #fff;\n  background-color: #545b62;\n}\na.badge-secondary:focus, a.badge-secondary.focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);\n}\n\n.badge-success {\n  color: #fff;\n  background-color: #28a745;\n}\na.badge-success:hover, a.badge-success:focus {\n  color: #fff;\n  background-color: #1e7e34;\n}\na.badge-success:focus, a.badge-success.focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);\n}\n\n.badge-info {\n  color: #fff;\n  background-color: #17a2b8;\n}\na.badge-info:hover, a.badge-info:focus {\n  color: #fff;\n  background-color: #117a8b;\n}\na.badge-info:focus, a.badge-info.focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);\n}\n\n.badge-warning {\n  color: #212529;\n  background-color: #ffc107;\n}\na.badge-warning:hover, a.badge-warning:focus {\n  color: #212529;\n  background-color: #d39e00;\n}\na.badge-warning:focus, a.badge-warning.focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);\n}\n\n.badge-danger {\n  color: #fff;\n  background-color: #dc3545;\n}\na.badge-danger:hover, a.badge-danger:focus {\n  color: #fff;\n  background-color: #bd2130;\n}\na.badge-danger:focus, a.badge-danger.focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);\n}\n\n.badge-light {\n  color: #212529;\n  background-color: #f8f9fa;\n}\na.badge-light:hover, a.badge-light:focus {\n  color: #212529;\n  background-color: #dae0e5;\n}\na.badge-light:focus, a.badge-light.focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);\n}\n\n.badge-dark {\n  color: #fff;\n  background-color: #343a40;\n}\na.badge-dark:hover, a.badge-dark:focus {\n  color: #fff;\n  background-color: #1d2124;\n}\na.badge-dark:focus, a.badge-dark.focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);\n}\n\n.jumbotron {\n  padding: 2rem 1rem;\n  margin-bottom: 2rem;\n  background-color: #e9ecef;\n  border-radius: 0.3rem;\n}\n@media (min-width: 576px) {\n  .jumbotron {\n    padding: 4rem 2rem;\n  }\n}\n\n.jumbotron-fluid {\n  padding-right: 0;\n  padding-left: 0;\n  border-radius: 0;\n}\n\n.alert {\n  position: relative;\n  padding: 0.75rem 1.25rem;\n  margin-bottom: 1rem;\n  border: 1px solid transparent;\n  border-radius: 0.25rem;\n}\n\n.alert-heading {\n  color: inherit;\n}\n\n.alert-link {\n  font-weight: 700;\n}\n\n.alert-dismissible {\n  padding-right: 4rem;\n}\n.alert-dismissible .close {\n  position: absolute;\n  top: 0;\n  right: 0;\n  padding: 0.75rem 1.25rem;\n  color: inherit;\n}\n\n.alert-primary {\n  color: #004085;\n  background-color: #cce5ff;\n  border-color: #b8daff;\n}\n.alert-primary hr {\n  border-top-color: #9fcdff;\n}\n.alert-primary .alert-link {\n  color: #002752;\n}\n\n.alert-secondary {\n  color: #383d41;\n  background-color: #e2e3e5;\n  border-color: #d6d8db;\n}\n.alert-secondary hr {\n  border-top-color: #c8cbcf;\n}\n.alert-secondary .alert-link {\n  color: #202326;\n}\n\n.alert-success {\n  color: #155724;\n  background-color: #d4edda;\n  border-color: #c3e6cb;\n}\n.alert-success hr {\n  border-top-color: #b1dfbb;\n}\n.alert-success .alert-link {\n  color: #0b2e13;\n}\n\n.alert-info {\n  color: #0c5460;\n  background-color: #d1ecf1;\n  border-color: #bee5eb;\n}\n.alert-info hr {\n  border-top-color: #abdde5;\n}\n.alert-info .alert-link {\n  color: #062c33;\n}\n\n.alert-warning {\n  color: #856404;\n  background-color: #fff3cd;\n  border-color: #ffeeba;\n}\n.alert-warning hr {\n  border-top-color: #ffe8a1;\n}\n.alert-warning .alert-link {\n  color: #533f03;\n}\n\n.alert-danger {\n  color: #721c24;\n  background-color: #f8d7da;\n  border-color: #f5c6cb;\n}\n.alert-danger hr {\n  border-top-color: #f1b0b7;\n}\n.alert-danger .alert-link {\n  color: #491217;\n}\n\n.alert-light {\n  color: #818182;\n  background-color: #fefefe;\n  border-color: #fdfdfe;\n}\n.alert-light hr {\n  border-top-color: #ececf6;\n}\n.alert-light .alert-link {\n  color: #686868;\n}\n\n.alert-dark {\n  color: #1b1e21;\n  background-color: #d6d8d9;\n  border-color: #c6c8ca;\n}\n.alert-dark hr {\n  border-top-color: #b9bbbe;\n}\n.alert-dark .alert-link {\n  color: #040505;\n}\n\n@keyframes progress-bar-stripes {\n  from {\n    background-position: 1rem 0;\n  }\n  to {\n    background-position: 0 0;\n  }\n}\n.progress {\n  display: flex;\n  height: 1rem;\n  overflow: hidden;\n  font-size: 0.75rem;\n  background-color: #e9ecef;\n  border-radius: 0.25rem;\n}\n\n.progress-bar {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  color: #fff;\n  text-align: center;\n  white-space: nowrap;\n  background-color: #007bff;\n  transition: width 0.6s ease;\n}\n@media (prefers-reduced-motion: reduce) {\n  .progress-bar {\n    transition: none;\n  }\n}\n\n.progress-bar-striped {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-size: 1rem 1rem;\n}\n\n.progress-bar-animated {\n  animation: progress-bar-stripes 1s linear infinite;\n}\n@media (prefers-reduced-motion: reduce) {\n  .progress-bar-animated {\n    animation: none;\n  }\n}\n\n.media {\n  display: flex;\n  align-items: flex-start;\n}\n\n.media-body {\n  flex: 1;\n}\n\n.list-group {\n  display: flex;\n  flex-direction: column;\n  padding-left: 0;\n  margin-bottom: 0;\n}\n\n.list-group-item-action {\n  width: 100%;\n  color: #495057;\n  text-align: inherit;\n}\n.list-group-item-action:hover, .list-group-item-action:focus {\n  z-index: 1;\n  color: #495057;\n  text-decoration: none;\n  background-color: #f8f9fa;\n}\n.list-group-item-action:active {\n  color: #212529;\n  background-color: #e9ecef;\n}\n\n.list-group-item {\n  position: relative;\n  display: block;\n  padding: 0.75rem 1.25rem;\n  margin-bottom: -1px;\n  background-color: #fff;\n  border: 1px solid rgba(0, 0, 0, 0.125);\n}\n.list-group-item:first-child {\n  border-top-left-radius: 0.25rem;\n  border-top-right-radius: 0.25rem;\n}\n.list-group-item:last-child {\n  margin-bottom: 0;\n  border-bottom-right-radius: 0.25rem;\n  border-bottom-left-radius: 0.25rem;\n}\n.list-group-item.disabled, .list-group-item:disabled {\n  color: #6c757d;\n  pointer-events: none;\n  background-color: #fff;\n}\n.list-group-item.active {\n  z-index: 2;\n  color: #fff;\n  background-color: #007bff;\n  border-color: #007bff;\n}\n\n.list-group-horizontal {\n  flex-direction: row;\n}\n.list-group-horizontal .list-group-item {\n  margin-right: -1px;\n  margin-bottom: 0;\n}\n.list-group-horizontal .list-group-item:first-child {\n  border-top-left-radius: 0.25rem;\n  border-bottom-left-radius: 0.25rem;\n  border-top-right-radius: 0;\n}\n.list-group-horizontal .list-group-item:last-child {\n  margin-right: 0;\n  border-top-right-radius: 0.25rem;\n  border-bottom-right-radius: 0.25rem;\n  border-bottom-left-radius: 0;\n}\n\n@media (min-width: 576px) {\n  .list-group-horizontal-sm {\n    flex-direction: row;\n  }\n  .list-group-horizontal-sm .list-group-item {\n    margin-right: -1px;\n    margin-bottom: 0;\n  }\n  .list-group-horizontal-sm .list-group-item:first-child {\n    border-top-left-radius: 0.25rem;\n    border-bottom-left-radius: 0.25rem;\n    border-top-right-radius: 0;\n  }\n  .list-group-horizontal-sm .list-group-item:last-child {\n    margin-right: 0;\n    border-top-right-radius: 0.25rem;\n    border-bottom-right-radius: 0.25rem;\n    border-bottom-left-radius: 0;\n  }\n}\n@media (min-width: 768px) {\n  .list-group-horizontal-md {\n    flex-direction: row;\n  }\n  .list-group-horizontal-md .list-group-item {\n    margin-right: -1px;\n    margin-bottom: 0;\n  }\n  .list-group-horizontal-md .list-group-item:first-child {\n    border-top-left-radius: 0.25rem;\n    border-bottom-left-radius: 0.25rem;\n    border-top-right-radius: 0;\n  }\n  .list-group-horizontal-md .list-group-item:last-child {\n    margin-right: 0;\n    border-top-right-radius: 0.25rem;\n    border-bottom-right-radius: 0.25rem;\n    border-bottom-left-radius: 0;\n  }\n}\n@media (min-width: 992px) {\n  .list-group-horizontal-lg {\n    flex-direction: row;\n  }\n  .list-group-horizontal-lg .list-group-item {\n    margin-right: -1px;\n    margin-bottom: 0;\n  }\n  .list-group-horizontal-lg .list-group-item:first-child {\n    border-top-left-radius: 0.25rem;\n    border-bottom-left-radius: 0.25rem;\n    border-top-right-radius: 0;\n  }\n  .list-group-horizontal-lg .list-group-item:last-child {\n    margin-right: 0;\n    border-top-right-radius: 0.25rem;\n    border-bottom-right-radius: 0.25rem;\n    border-bottom-left-radius: 0;\n  }\n}\n@media (min-width: 1200px) {\n  .list-group-horizontal-xl {\n    flex-direction: row;\n  }\n  .list-group-horizontal-xl .list-group-item {\n    margin-right: -1px;\n    margin-bottom: 0;\n  }\n  .list-group-horizontal-xl .list-group-item:first-child {\n    border-top-left-radius: 0.25rem;\n    border-bottom-left-radius: 0.25rem;\n    border-top-right-radius: 0;\n  }\n  .list-group-horizontal-xl .list-group-item:last-child {\n    margin-right: 0;\n    border-top-right-radius: 0.25rem;\n    border-bottom-right-radius: 0.25rem;\n    border-bottom-left-radius: 0;\n  }\n}\n.list-group-flush .list-group-item {\n  border-right: 0;\n  border-left: 0;\n  border-radius: 0;\n}\n.list-group-flush .list-group-item:last-child {\n  margin-bottom: -1px;\n}\n.list-group-flush:first-child .list-group-item:first-child {\n  border-top: 0;\n}\n.list-group-flush:last-child .list-group-item:last-child {\n  margin-bottom: 0;\n  border-bottom: 0;\n}\n\n.list-group-item-primary {\n  color: #004085;\n  background-color: #b8daff;\n}\n.list-group-item-primary.list-group-item-action:hover, .list-group-item-primary.list-group-item-action:focus {\n  color: #004085;\n  background-color: #9fcdff;\n}\n.list-group-item-primary.list-group-item-action.active {\n  color: #fff;\n  background-color: #004085;\n  border-color: #004085;\n}\n\n.list-group-item-secondary {\n  color: #383d41;\n  background-color: #d6d8db;\n}\n.list-group-item-secondary.list-group-item-action:hover, .list-group-item-secondary.list-group-item-action:focus {\n  color: #383d41;\n  background-color: #c8cbcf;\n}\n.list-group-item-secondary.list-group-item-action.active {\n  color: #fff;\n  background-color: #383d41;\n  border-color: #383d41;\n}\n\n.list-group-item-success {\n  color: #155724;\n  background-color: #c3e6cb;\n}\n.list-group-item-success.list-group-item-action:hover, .list-group-item-success.list-group-item-action:focus {\n  color: #155724;\n  background-color: #b1dfbb;\n}\n.list-group-item-success.list-group-item-action.active {\n  color: #fff;\n  background-color: #155724;\n  border-color: #155724;\n}\n\n.list-group-item-info {\n  color: #0c5460;\n  background-color: #bee5eb;\n}\n.list-group-item-info.list-group-item-action:hover, .list-group-item-info.list-group-item-action:focus {\n  color: #0c5460;\n  background-color: #abdde5;\n}\n.list-group-item-info.list-group-item-action.active {\n  color: #fff;\n  background-color: #0c5460;\n  border-color: #0c5460;\n}\n\n.list-group-item-warning {\n  color: #856404;\n  background-color: #ffeeba;\n}\n.list-group-item-warning.list-group-item-action:hover, .list-group-item-warning.list-group-item-action:focus {\n  color: #856404;\n  background-color: #ffe8a1;\n}\n.list-group-item-warning.list-group-item-action.active {\n  color: #fff;\n  background-color: #856404;\n  border-color: #856404;\n}\n\n.list-group-item-danger {\n  color: #721c24;\n  background-color: #f5c6cb;\n}\n.list-group-item-danger.list-group-item-action:hover, .list-group-item-danger.list-group-item-action:focus {\n  color: #721c24;\n  background-color: #f1b0b7;\n}\n.list-group-item-danger.list-group-item-action.active {\n  color: #fff;\n  background-color: #721c24;\n  border-color: #721c24;\n}\n\n.list-group-item-light {\n  color: #818182;\n  background-color: #fdfdfe;\n}\n.list-group-item-light.list-group-item-action:hover, .list-group-item-light.list-group-item-action:focus {\n  color: #818182;\n  background-color: #ececf6;\n}\n.list-group-item-light.list-group-item-action.active {\n  color: #fff;\n  background-color: #818182;\n  border-color: #818182;\n}\n\n.list-group-item-dark {\n  color: #1b1e21;\n  background-color: #c6c8ca;\n}\n.list-group-item-dark.list-group-item-action:hover, .list-group-item-dark.list-group-item-action:focus {\n  color: #1b1e21;\n  background-color: #b9bbbe;\n}\n.list-group-item-dark.list-group-item-action.active {\n  color: #fff;\n  background-color: #1b1e21;\n  border-color: #1b1e21;\n}\n\n.close {\n  float: right;\n  font-size: 1.5rem;\n  font-weight: 700;\n  line-height: 1;\n  color: #000;\n  text-shadow: 0 1px 0 #fff;\n  opacity: 0.5;\n}\n.close:hover {\n  color: #000;\n  text-decoration: none;\n}\n.close:not(:disabled):not(.disabled):hover, .close:not(:disabled):not(.disabled):focus {\n  opacity: 0.75;\n}\n\nbutton.close {\n  padding: 0;\n  background-color: transparent;\n  border: 0;\n  appearance: none;\n}\n\na.close.disabled {\n  pointer-events: none;\n}\n\n.toast {\n  max-width: 350px;\n  overflow: hidden;\n  font-size: 0.875rem;\n  background-color: rgba(255, 255, 255, 0.85);\n  background-clip: padding-box;\n  border: 1px solid rgba(0, 0, 0, 0.1);\n  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(10px);\n  opacity: 0;\n  border-radius: 0.25rem;\n}\n.toast:not(:last-child) {\n  margin-bottom: 0.75rem;\n}\n.toast.showing {\n  opacity: 1;\n}\n.toast.show {\n  display: block;\n  opacity: 1;\n}\n.toast.hide {\n  display: none;\n}\n\n.toast-header {\n  display: flex;\n  align-items: center;\n  padding: 0.25rem 0.75rem;\n  color: #6c757d;\n  background-color: rgba(255, 255, 255, 0.85);\n  background-clip: padding-box;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\n}\n\n.toast-body {\n  padding: 0.75rem;\n}\n\n.modal-open {\n  overflow: hidden;\n}\n.modal-open .modal {\n  overflow-x: hidden;\n  overflow-y: auto;\n}\n\n.modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: 1050;\n  display: none;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n  outline: 0;\n}\n\n.modal-dialog {\n  position: relative;\n  width: auto;\n  margin: 0.5rem;\n  pointer-events: none;\n}\n.modal.fade .modal-dialog {\n  transition: transform 0.3s ease-out;\n  transform: translate(0, -50px);\n}\n@media (prefers-reduced-motion: reduce) {\n  .modal.fade .modal-dialog {\n    transition: none;\n  }\n}\n.modal.show .modal-dialog {\n  transform: none;\n}\n\n.modal-dialog-scrollable {\n  display: flex;\n  max-height: calc(100% - 1rem);\n}\n.modal-dialog-scrollable .modal-content {\n  max-height: calc(100vh - 1rem);\n  overflow: hidden;\n}\n.modal-dialog-scrollable .modal-header,\n.modal-dialog-scrollable .modal-footer {\n  flex-shrink: 0;\n}\n.modal-dialog-scrollable .modal-body {\n  overflow-y: auto;\n}\n\n.modal-dialog-centered {\n  display: flex;\n  align-items: center;\n  min-height: calc(100% - 1rem);\n}\n.modal-dialog-centered::before {\n  display: block;\n  height: calc(100vh - 1rem);\n  content: \"\";\n}\n.modal-dialog-centered.modal-dialog-scrollable {\n  flex-direction: column;\n  justify-content: center;\n  height: 100%;\n}\n.modal-dialog-centered.modal-dialog-scrollable .modal-content {\n  max-height: none;\n}\n.modal-dialog-centered.modal-dialog-scrollable::before {\n  content: none;\n}\n\n.modal-content {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n  pointer-events: auto;\n  background-color: #fff;\n  background-clip: padding-box;\n  border: 1px solid rgba(0, 0, 0, 0.2);\n  border-radius: 0.3rem;\n  outline: 0;\n}\n\n.modal-backdrop {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: 1040;\n  width: 100vw;\n  height: 100vh;\n  background-color: #000;\n}\n.modal-backdrop.fade {\n  opacity: 0;\n}\n.modal-backdrop.show {\n  opacity: 0.5;\n}\n\n.modal-header {\n  display: flex;\n  align-items: flex-start;\n  justify-content: space-between;\n  padding: 1rem 1rem;\n  border-bottom: 1px solid #dee2e6;\n  border-top-left-radius: 0.3rem;\n  border-top-right-radius: 0.3rem;\n}\n.modal-header .close {\n  padding: 1rem 1rem;\n  margin: -1rem -1rem -1rem auto;\n}\n\n.modal-title {\n  margin-bottom: 0;\n  line-height: 1.5;\n}\n\n.modal-body {\n  position: relative;\n  flex: 1 1 auto;\n  padding: 1rem;\n}\n\n.modal-footer {\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  padding: 1rem;\n  border-top: 1px solid #dee2e6;\n  border-bottom-right-radius: 0.3rem;\n  border-bottom-left-radius: 0.3rem;\n}\n.modal-footer > :not(:first-child) {\n  margin-left: 0.25rem;\n}\n.modal-footer > :not(:last-child) {\n  margin-right: 0.25rem;\n}\n\n.modal-scrollbar-measure {\n  position: absolute;\n  top: -9999px;\n  width: 50px;\n  height: 50px;\n  overflow: scroll;\n}\n\n@media (min-width: 576px) {\n  .modal-dialog {\n    max-width: 500px;\n    margin: 1.75rem auto;\n  }\n  .modal-dialog-scrollable {\n    max-height: calc(100% - 3.5rem);\n  }\n  .modal-dialog-scrollable .modal-content {\n    max-height: calc(100vh - 3.5rem);\n  }\n  .modal-dialog-centered {\n    min-height: calc(100% - 3.5rem);\n  }\n  .modal-dialog-centered::before {\n    height: calc(100vh - 3.5rem);\n  }\n  .modal-sm {\n    max-width: 300px;\n  }\n}\n@media (min-width: 992px) {\n  .modal-lg,\n  .modal-xl {\n    max-width: 800px;\n  }\n}\n@media (min-width: 1200px) {\n  .modal-xl {\n    max-width: 1140px;\n  }\n}\n.tooltip {\n  position: absolute;\n  z-index: 1070;\n  display: block;\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  font-style: normal;\n  font-weight: 400;\n  line-height: 1.5;\n  text-align: left;\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n  font-size: 0.875rem;\n  word-wrap: break-word;\n  opacity: 0;\n}\n.tooltip.show {\n  opacity: 0.9;\n}\n.tooltip .arrow {\n  position: absolute;\n  display: block;\n  width: 0.8rem;\n  height: 0.4rem;\n}\n.tooltip .arrow::before {\n  position: absolute;\n  content: \"\";\n  border-color: transparent;\n  border-style: solid;\n}\n\n.bs-tooltip-top, .bs-tooltip-auto[x-placement^=top] {\n  padding: 0.4rem 0;\n}\n.bs-tooltip-top .arrow, .bs-tooltip-auto[x-placement^=top] .arrow {\n  bottom: 0;\n}\n.bs-tooltip-top .arrow::before, .bs-tooltip-auto[x-placement^=top] .arrow::before {\n  top: 0;\n  border-width: 0.4rem 0.4rem 0;\n  border-top-color: #000;\n}\n\n.bs-tooltip-right, .bs-tooltip-auto[x-placement^=right] {\n  padding: 0 0.4rem;\n}\n.bs-tooltip-right .arrow, .bs-tooltip-auto[x-placement^=right] .arrow {\n  left: 0;\n  width: 0.4rem;\n  height: 0.8rem;\n}\n.bs-tooltip-right .arrow::before, .bs-tooltip-auto[x-placement^=right] .arrow::before {\n  right: 0;\n  border-width: 0.4rem 0.4rem 0.4rem 0;\n  border-right-color: #000;\n}\n\n.bs-tooltip-bottom, .bs-tooltip-auto[x-placement^=bottom] {\n  padding: 0.4rem 0;\n}\n.bs-tooltip-bottom .arrow, .bs-tooltip-auto[x-placement^=bottom] .arrow {\n  top: 0;\n}\n.bs-tooltip-bottom .arrow::before, .bs-tooltip-auto[x-placement^=bottom] .arrow::before {\n  bottom: 0;\n  border-width: 0 0.4rem 0.4rem;\n  border-bottom-color: #000;\n}\n\n.bs-tooltip-left, .bs-tooltip-auto[x-placement^=left] {\n  padding: 0 0.4rem;\n}\n.bs-tooltip-left .arrow, .bs-tooltip-auto[x-placement^=left] .arrow {\n  right: 0;\n  width: 0.4rem;\n  height: 0.8rem;\n}\n.bs-tooltip-left .arrow::before, .bs-tooltip-auto[x-placement^=left] .arrow::before {\n  left: 0;\n  border-width: 0.4rem 0 0.4rem 0.4rem;\n  border-left-color: #000;\n}\n\n.tooltip-inner {\n  max-width: 200px;\n  padding: 0.25rem 0.5rem;\n  color: #fff;\n  text-align: center;\n  background-color: #000;\n  border-radius: 0.25rem;\n}\n\n.popover {\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: 1060;\n  display: block;\n  max-width: 276px;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  font-style: normal;\n  font-weight: 400;\n  line-height: 1.5;\n  text-align: left;\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n  font-size: 0.875rem;\n  word-wrap: break-word;\n  background-color: #fff;\n  background-clip: padding-box;\n  border: 1px solid rgba(0, 0, 0, 0.2);\n  border-radius: 0.3rem;\n}\n.popover .arrow {\n  position: absolute;\n  display: block;\n  width: 1rem;\n  height: 0.5rem;\n  margin: 0 0.3rem;\n}\n.popover .arrow::before, .popover .arrow::after {\n  position: absolute;\n  display: block;\n  content: \"\";\n  border-color: transparent;\n  border-style: solid;\n}\n\n.bs-popover-top, .bs-popover-auto[x-placement^=top] {\n  margin-bottom: 0.5rem;\n}\n.bs-popover-top > .arrow, .bs-popover-auto[x-placement^=top] > .arrow {\n  bottom: calc((0.5rem + 1px) * -1);\n}\n.bs-popover-top > .arrow::before, .bs-popover-auto[x-placement^=top] > .arrow::before {\n  bottom: 0;\n  border-width: 0.5rem 0.5rem 0;\n  border-top-color: rgba(0, 0, 0, 0.25);\n}\n.bs-popover-top > .arrow::after, .bs-popover-auto[x-placement^=top] > .arrow::after {\n  bottom: 1px;\n  border-width: 0.5rem 0.5rem 0;\n  border-top-color: #fff;\n}\n\n.bs-popover-right, .bs-popover-auto[x-placement^=right] {\n  margin-left: 0.5rem;\n}\n.bs-popover-right > .arrow, .bs-popover-auto[x-placement^=right] > .arrow {\n  left: calc((0.5rem + 1px) * -1);\n  width: 0.5rem;\n  height: 1rem;\n  margin: 0.3rem 0;\n}\n.bs-popover-right > .arrow::before, .bs-popover-auto[x-placement^=right] > .arrow::before {\n  left: 0;\n  border-width: 0.5rem 0.5rem 0.5rem 0;\n  border-right-color: rgba(0, 0, 0, 0.25);\n}\n.bs-popover-right > .arrow::after, .bs-popover-auto[x-placement^=right] > .arrow::after {\n  left: 1px;\n  border-width: 0.5rem 0.5rem 0.5rem 0;\n  border-right-color: #fff;\n}\n\n.bs-popover-bottom, .bs-popover-auto[x-placement^=bottom] {\n  margin-top: 0.5rem;\n}\n.bs-popover-bottom > .arrow, .bs-popover-auto[x-placement^=bottom] > .arrow {\n  top: calc((0.5rem + 1px) * -1);\n}\n.bs-popover-bottom > .arrow::before, .bs-popover-auto[x-placement^=bottom] > .arrow::before {\n  top: 0;\n  border-width: 0 0.5rem 0.5rem 0.5rem;\n  border-bottom-color: rgba(0, 0, 0, 0.25);\n}\n.bs-popover-bottom > .arrow::after, .bs-popover-auto[x-placement^=bottom] > .arrow::after {\n  top: 1px;\n  border-width: 0 0.5rem 0.5rem 0.5rem;\n  border-bottom-color: #fff;\n}\n.bs-popover-bottom .popover-header::before, .bs-popover-auto[x-placement^=bottom] .popover-header::before {\n  position: absolute;\n  top: 0;\n  left: 50%;\n  display: block;\n  width: 1rem;\n  margin-left: -0.5rem;\n  content: \"\";\n  border-bottom: 1px solid #f7f7f7;\n}\n\n.bs-popover-left, .bs-popover-auto[x-placement^=left] {\n  margin-right: 0.5rem;\n}\n.bs-popover-left > .arrow, .bs-popover-auto[x-placement^=left] > .arrow {\n  right: calc((0.5rem + 1px) * -1);\n  width: 0.5rem;\n  height: 1rem;\n  margin: 0.3rem 0;\n}\n.bs-popover-left > .arrow::before, .bs-popover-auto[x-placement^=left] > .arrow::before {\n  right: 0;\n  border-width: 0.5rem 0 0.5rem 0.5rem;\n  border-left-color: rgba(0, 0, 0, 0.25);\n}\n.bs-popover-left > .arrow::after, .bs-popover-auto[x-placement^=left] > .arrow::after {\n  right: 1px;\n  border-width: 0.5rem 0 0.5rem 0.5rem;\n  border-left-color: #fff;\n}\n\n.popover-header {\n  padding: 0.5rem 0.75rem;\n  margin-bottom: 0;\n  font-size: 1rem;\n  background-color: #f7f7f7;\n  border-bottom: 1px solid #ebebeb;\n  border-top-left-radius: calc(0.3rem - 1px);\n  border-top-right-radius: calc(0.3rem - 1px);\n}\n.popover-header:empty {\n  display: none;\n}\n\n.popover-body {\n  padding: 0.5rem 0.75rem;\n  color: #212529;\n}\n\n.carousel {\n  position: relative;\n}\n\n.carousel.pointer-event {\n  touch-action: pan-y;\n}\n\n.carousel-inner {\n  position: relative;\n  width: 100%;\n  overflow: hidden;\n}\n.carousel-inner::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.carousel-item {\n  position: relative;\n  display: none;\n  float: left;\n  width: 100%;\n  margin-right: -100%;\n  backface-visibility: hidden;\n  transition: transform 0.6s ease-in-out;\n}\n@media (prefers-reduced-motion: reduce) {\n  .carousel-item {\n    transition: none;\n  }\n}\n\n.carousel-item.active,\n.carousel-item-next,\n.carousel-item-prev {\n  display: block;\n}\n\n.carousel-item-next:not(.carousel-item-left),\n.active.carousel-item-right {\n  transform: translateX(100%);\n}\n\n.carousel-item-prev:not(.carousel-item-right),\n.active.carousel-item-left {\n  transform: translateX(-100%);\n}\n\n.carousel-fade .carousel-item {\n  opacity: 0;\n  transition-property: opacity;\n  transform: none;\n}\n.carousel-fade .carousel-item.active,\n.carousel-fade .carousel-item-next.carousel-item-left,\n.carousel-fade .carousel-item-prev.carousel-item-right {\n  z-index: 1;\n  opacity: 1;\n}\n.carousel-fade .active.carousel-item-left,\n.carousel-fade .active.carousel-item-right {\n  z-index: 0;\n  opacity: 0;\n  transition: 0s 0.6s opacity;\n}\n@media (prefers-reduced-motion: reduce) {\n  .carousel-fade .active.carousel-item-left,\n  .carousel-fade .active.carousel-item-right {\n    transition: none;\n  }\n}\n\n.carousel-control-prev,\n.carousel-control-next {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  z-index: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 15%;\n  color: #fff;\n  text-align: center;\n  opacity: 0.5;\n  transition: opacity 0.15s ease;\n}\n@media (prefers-reduced-motion: reduce) {\n  .carousel-control-prev,\n  .carousel-control-next {\n    transition: none;\n  }\n}\n.carousel-control-prev:hover, .carousel-control-prev:focus,\n.carousel-control-next:hover,\n.carousel-control-next:focus {\n  color: #fff;\n  text-decoration: none;\n  outline: 0;\n  opacity: 0.9;\n}\n\n.carousel-control-prev {\n  left: 0;\n}\n\n.carousel-control-next {\n  right: 0;\n}\n\n.carousel-control-prev-icon,\n.carousel-control-next-icon {\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  background: no-repeat 50%/100% 100%;\n}\n\n.carousel-control-prev-icon {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3e%3c/svg%3e\");\n}\n\n.carousel-control-next-icon {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3e%3c/svg%3e\");\n}\n\n.carousel-indicators {\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 15;\n  display: flex;\n  justify-content: center;\n  padding-left: 0;\n  margin-right: 15%;\n  margin-left: 15%;\n  list-style: none;\n}\n.carousel-indicators li {\n  box-sizing: content-box;\n  flex: 0 1 auto;\n  width: 30px;\n  height: 3px;\n  margin-right: 3px;\n  margin-left: 3px;\n  text-indent: -999px;\n  cursor: pointer;\n  background-color: #fff;\n  background-clip: padding-box;\n  border-top: 10px solid transparent;\n  border-bottom: 10px solid transparent;\n  opacity: 0.5;\n  transition: opacity 0.6s ease;\n}\n@media (prefers-reduced-motion: reduce) {\n  .carousel-indicators li {\n    transition: none;\n  }\n}\n.carousel-indicators .active {\n  opacity: 1;\n}\n\n.carousel-caption {\n  position: absolute;\n  right: 15%;\n  bottom: 20px;\n  left: 15%;\n  z-index: 10;\n  padding-top: 20px;\n  padding-bottom: 20px;\n  color: #fff;\n  text-align: center;\n}\n\n@keyframes spinner-border {\n  to {\n    transform: rotate(360deg);\n  }\n}\n.spinner-border {\n  display: inline-block;\n  width: 2rem;\n  height: 2rem;\n  vertical-align: text-bottom;\n  border: 0.25em solid currentColor;\n  border-right-color: transparent;\n  border-radius: 50%;\n  animation: spinner-border 0.75s linear infinite;\n}\n\n.spinner-border-sm {\n  width: 1rem;\n  height: 1rem;\n  border-width: 0.2em;\n}\n\n@keyframes spinner-grow {\n  0% {\n    transform: scale(0);\n  }\n  50% {\n    opacity: 1;\n  }\n}\n.spinner-grow {\n  display: inline-block;\n  width: 2rem;\n  height: 2rem;\n  vertical-align: text-bottom;\n  background-color: currentColor;\n  border-radius: 50%;\n  opacity: 0;\n  animation: spinner-grow 0.75s linear infinite;\n}\n\n.spinner-grow-sm {\n  width: 1rem;\n  height: 1rem;\n}\n\n.align-baseline {\n  vertical-align: baseline !important;\n}\n\n.align-top {\n  vertical-align: top !important;\n}\n\n.align-middle {\n  vertical-align: middle !important;\n}\n\n.align-bottom {\n  vertical-align: bottom !important;\n}\n\n.align-text-bottom {\n  vertical-align: text-bottom !important;\n}\n\n.align-text-top {\n  vertical-align: text-top !important;\n}\n\n.bg-primary {\n  background-color: #007bff !important;\n}\n\na.bg-primary:hover, a.bg-primary:focus,\nbutton.bg-primary:hover,\nbutton.bg-primary:focus {\n  background-color: #0062cc !important;\n}\n\n.bg-secondary {\n  background-color: #6c757d !important;\n}\n\na.bg-secondary:hover, a.bg-secondary:focus,\nbutton.bg-secondary:hover,\nbutton.bg-secondary:focus {\n  background-color: #545b62 !important;\n}\n\n.bg-success {\n  background-color: #28a745 !important;\n}\n\na.bg-success:hover, a.bg-success:focus,\nbutton.bg-success:hover,\nbutton.bg-success:focus {\n  background-color: #1e7e34 !important;\n}\n\n.bg-info {\n  background-color: #17a2b8 !important;\n}\n\na.bg-info:hover, a.bg-info:focus,\nbutton.bg-info:hover,\nbutton.bg-info:focus {\n  background-color: #117a8b !important;\n}\n\n.bg-warning {\n  background-color: #ffc107 !important;\n}\n\na.bg-warning:hover, a.bg-warning:focus,\nbutton.bg-warning:hover,\nbutton.bg-warning:focus {\n  background-color: #d39e00 !important;\n}\n\n.bg-danger {\n  background-color: #dc3545 !important;\n}\n\na.bg-danger:hover, a.bg-danger:focus,\nbutton.bg-danger:hover,\nbutton.bg-danger:focus {\n  background-color: #bd2130 !important;\n}\n\n.bg-light {\n  background-color: #f8f9fa !important;\n}\n\na.bg-light:hover, a.bg-light:focus,\nbutton.bg-light:hover,\nbutton.bg-light:focus {\n  background-color: #dae0e5 !important;\n}\n\n.bg-dark {\n  background-color: #343a40 !important;\n}\n\na.bg-dark:hover, a.bg-dark:focus,\nbutton.bg-dark:hover,\nbutton.bg-dark:focus {\n  background-color: #1d2124 !important;\n}\n\n.bg-white {\n  background-color: #fff !important;\n}\n\n.bg-transparent {\n  background-color: transparent !important;\n}\n\n.border {\n  border: 1px solid #dee2e6 !important;\n}\n\n.border-top {\n  border-top: 1px solid #dee2e6 !important;\n}\n\n.border-right {\n  border-right: 1px solid #dee2e6 !important;\n}\n\n.border-bottom {\n  border-bottom: 1px solid #dee2e6 !important;\n}\n\n.border-left {\n  border-left: 1px solid #dee2e6 !important;\n}\n\n.border-0 {\n  border: 0 !important;\n}\n\n.border-top-0 {\n  border-top: 0 !important;\n}\n\n.border-right-0 {\n  border-right: 0 !important;\n}\n\n.border-bottom-0 {\n  border-bottom: 0 !important;\n}\n\n.border-left-0 {\n  border-left: 0 !important;\n}\n\n.border-primary {\n  border-color: #007bff !important;\n}\n\n.border-secondary {\n  border-color: #6c757d !important;\n}\n\n.border-success {\n  border-color: #28a745 !important;\n}\n\n.border-info {\n  border-color: #17a2b8 !important;\n}\n\n.border-warning {\n  border-color: #ffc107 !important;\n}\n\n.border-danger {\n  border-color: #dc3545 !important;\n}\n\n.border-light {\n  border-color: #f8f9fa !important;\n}\n\n.border-dark {\n  border-color: #343a40 !important;\n}\n\n.border-white {\n  border-color: #fff !important;\n}\n\n.rounded-sm {\n  border-radius: 0.2rem !important;\n}\n\n.rounded {\n  border-radius: 0.25rem !important;\n}\n\n.rounded-top {\n  border-top-left-radius: 0.25rem !important;\n  border-top-right-radius: 0.25rem !important;\n}\n\n.rounded-right {\n  border-top-right-radius: 0.25rem !important;\n  border-bottom-right-radius: 0.25rem !important;\n}\n\n.rounded-bottom {\n  border-bottom-right-radius: 0.25rem !important;\n  border-bottom-left-radius: 0.25rem !important;\n}\n\n.rounded-left {\n  border-top-left-radius: 0.25rem !important;\n  border-bottom-left-radius: 0.25rem !important;\n}\n\n.rounded-lg {\n  border-radius: 0.3rem !important;\n}\n\n.rounded-circle {\n  border-radius: 50% !important;\n}\n\n.rounded-pill {\n  border-radius: 50rem !important;\n}\n\n.rounded-0 {\n  border-radius: 0 !important;\n}\n\n.clearfix::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.d-none {\n  display: none !important;\n}\n\n.d-inline {\n  display: inline !important;\n}\n\n.d-inline-block {\n  display: inline-block !important;\n}\n\n.d-block {\n  display: block !important;\n}\n\n.d-table {\n  display: table !important;\n}\n\n.d-table-row {\n  display: table-row !important;\n}\n\n.d-table-cell {\n  display: table-cell !important;\n}\n\n.d-flex {\n  display: flex !important;\n}\n\n.d-inline-flex {\n  display: inline-flex !important;\n}\n\n@media (min-width: 576px) {\n  .d-sm-none {\n    display: none !important;\n  }\n  .d-sm-inline {\n    display: inline !important;\n  }\n  .d-sm-inline-block {\n    display: inline-block !important;\n  }\n  .d-sm-block {\n    display: block !important;\n  }\n  .d-sm-table {\n    display: table !important;\n  }\n  .d-sm-table-row {\n    display: table-row !important;\n  }\n  .d-sm-table-cell {\n    display: table-cell !important;\n  }\n  .d-sm-flex {\n    display: flex !important;\n  }\n  .d-sm-inline-flex {\n    display: inline-flex !important;\n  }\n}\n@media (min-width: 768px) {\n  .d-md-none {\n    display: none !important;\n  }\n  .d-md-inline {\n    display: inline !important;\n  }\n  .d-md-inline-block {\n    display: inline-block !important;\n  }\n  .d-md-block {\n    display: block !important;\n  }\n  .d-md-table {\n    display: table !important;\n  }\n  .d-md-table-row {\n    display: table-row !important;\n  }\n  .d-md-table-cell {\n    display: table-cell !important;\n  }\n  .d-md-flex {\n    display: flex !important;\n  }\n  .d-md-inline-flex {\n    display: inline-flex !important;\n  }\n}\n@media (min-width: 992px) {\n  .d-lg-none {\n    display: none !important;\n  }\n  .d-lg-inline {\n    display: inline !important;\n  }\n  .d-lg-inline-block {\n    display: inline-block !important;\n  }\n  .d-lg-block {\n    display: block !important;\n  }\n  .d-lg-table {\n    display: table !important;\n  }\n  .d-lg-table-row {\n    display: table-row !important;\n  }\n  .d-lg-table-cell {\n    display: table-cell !important;\n  }\n  .d-lg-flex {\n    display: flex !important;\n  }\n  .d-lg-inline-flex {\n    display: inline-flex !important;\n  }\n}\n@media (min-width: 1200px) {\n  .d-xl-none {\n    display: none !important;\n  }\n  .d-xl-inline {\n    display: inline !important;\n  }\n  .d-xl-inline-block {\n    display: inline-block !important;\n  }\n  .d-xl-block {\n    display: block !important;\n  }\n  .d-xl-table {\n    display: table !important;\n  }\n  .d-xl-table-row {\n    display: table-row !important;\n  }\n  .d-xl-table-cell {\n    display: table-cell !important;\n  }\n  .d-xl-flex {\n    display: flex !important;\n  }\n  .d-xl-inline-flex {\n    display: inline-flex !important;\n  }\n}\n@media print {\n  .d-print-none {\n    display: none !important;\n  }\n  .d-print-inline {\n    display: inline !important;\n  }\n  .d-print-inline-block {\n    display: inline-block !important;\n  }\n  .d-print-block {\n    display: block !important;\n  }\n  .d-print-table {\n    display: table !important;\n  }\n  .d-print-table-row {\n    display: table-row !important;\n  }\n  .d-print-table-cell {\n    display: table-cell !important;\n  }\n  .d-print-flex {\n    display: flex !important;\n  }\n  .d-print-inline-flex {\n    display: inline-flex !important;\n  }\n}\n.embed-responsive {\n  position: relative;\n  display: block;\n  width: 100%;\n  padding: 0;\n  overflow: hidden;\n}\n.embed-responsive::before {\n  display: block;\n  content: \"\";\n}\n.embed-responsive .embed-responsive-item,\n.embed-responsive iframe,\n.embed-responsive embed,\n.embed-responsive object,\n.embed-responsive video {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  border: 0;\n}\n\n.embed-responsive-21by9::before {\n  padding-top: 42.8571428571%;\n}\n\n.embed-responsive-16by9::before {\n  padding-top: 56.25%;\n}\n\n.embed-responsive-4by3::before {\n  padding-top: 75%;\n}\n\n.embed-responsive-1by1::before {\n  padding-top: 100%;\n}\n\n.flex-row {\n  flex-direction: row !important;\n}\n\n.flex-column {\n  flex-direction: column !important;\n}\n\n.flex-row-reverse {\n  flex-direction: row-reverse !important;\n}\n\n.flex-column-reverse {\n  flex-direction: column-reverse !important;\n}\n\n.flex-wrap {\n  flex-wrap: wrap !important;\n}\n\n.flex-nowrap {\n  flex-wrap: nowrap !important;\n}\n\n.flex-wrap-reverse {\n  flex-wrap: wrap-reverse !important;\n}\n\n.flex-fill {\n  flex: 1 1 auto !important;\n}\n\n.flex-grow-0 {\n  flex-grow: 0 !important;\n}\n\n.flex-grow-1 {\n  flex-grow: 1 !important;\n}\n\n.flex-shrink-0 {\n  flex-shrink: 0 !important;\n}\n\n.flex-shrink-1 {\n  flex-shrink: 1 !important;\n}\n\n.justify-content-start {\n  justify-content: flex-start !important;\n}\n\n.justify-content-end {\n  justify-content: flex-end !important;\n}\n\n.justify-content-center {\n  justify-content: center !important;\n}\n\n.justify-content-between {\n  justify-content: space-between !important;\n}\n\n.justify-content-around {\n  justify-content: space-around !important;\n}\n\n.align-items-start {\n  align-items: flex-start !important;\n}\n\n.align-items-end {\n  align-items: flex-end !important;\n}\n\n.align-items-center {\n  align-items: center !important;\n}\n\n.align-items-baseline {\n  align-items: baseline !important;\n}\n\n.align-items-stretch {\n  align-items: stretch !important;\n}\n\n.align-content-start {\n  align-content: flex-start !important;\n}\n\n.align-content-end {\n  align-content: flex-end !important;\n}\n\n.align-content-center {\n  align-content: center !important;\n}\n\n.align-content-between {\n  align-content: space-between !important;\n}\n\n.align-content-around {\n  align-content: space-around !important;\n}\n\n.align-content-stretch {\n  align-content: stretch !important;\n}\n\n.align-self-auto {\n  align-self: auto !important;\n}\n\n.align-self-start {\n  align-self: flex-start !important;\n}\n\n.align-self-end {\n  align-self: flex-end !important;\n}\n\n.align-self-center {\n  align-self: center !important;\n}\n\n.align-self-baseline {\n  align-self: baseline !important;\n}\n\n.align-self-stretch {\n  align-self: stretch !important;\n}\n\n@media (min-width: 576px) {\n  .flex-sm-row {\n    flex-direction: row !important;\n  }\n  .flex-sm-column {\n    flex-direction: column !important;\n  }\n  .flex-sm-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-sm-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-sm-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-sm-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-sm-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .flex-sm-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-sm-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-sm-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-sm-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-sm-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .justify-content-sm-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-sm-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-sm-center {\n    justify-content: center !important;\n  }\n  .justify-content-sm-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-sm-around {\n    justify-content: space-around !important;\n  }\n  .align-items-sm-start {\n    align-items: flex-start !important;\n  }\n  .align-items-sm-end {\n    align-items: flex-end !important;\n  }\n  .align-items-sm-center {\n    align-items: center !important;\n  }\n  .align-items-sm-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-sm-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-sm-start {\n    align-content: flex-start !important;\n  }\n  .align-content-sm-end {\n    align-content: flex-end !important;\n  }\n  .align-content-sm-center {\n    align-content: center !important;\n  }\n  .align-content-sm-between {\n    align-content: space-between !important;\n  }\n  .align-content-sm-around {\n    align-content: space-around !important;\n  }\n  .align-content-sm-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-sm-auto {\n    align-self: auto !important;\n  }\n  .align-self-sm-start {\n    align-self: flex-start !important;\n  }\n  .align-self-sm-end {\n    align-self: flex-end !important;\n  }\n  .align-self-sm-center {\n    align-self: center !important;\n  }\n  .align-self-sm-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-sm-stretch {\n    align-self: stretch !important;\n  }\n}\n@media (min-width: 768px) {\n  .flex-md-row {\n    flex-direction: row !important;\n  }\n  .flex-md-column {\n    flex-direction: column !important;\n  }\n  .flex-md-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-md-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-md-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-md-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-md-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .flex-md-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-md-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-md-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-md-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-md-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .justify-content-md-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-md-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-md-center {\n    justify-content: center !important;\n  }\n  .justify-content-md-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-md-around {\n    justify-content: space-around !important;\n  }\n  .align-items-md-start {\n    align-items: flex-start !important;\n  }\n  .align-items-md-end {\n    align-items: flex-end !important;\n  }\n  .align-items-md-center {\n    align-items: center !important;\n  }\n  .align-items-md-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-md-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-md-start {\n    align-content: flex-start !important;\n  }\n  .align-content-md-end {\n    align-content: flex-end !important;\n  }\n  .align-content-md-center {\n    align-content: center !important;\n  }\n  .align-content-md-between {\n    align-content: space-between !important;\n  }\n  .align-content-md-around {\n    align-content: space-around !important;\n  }\n  .align-content-md-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-md-auto {\n    align-self: auto !important;\n  }\n  .align-self-md-start {\n    align-self: flex-start !important;\n  }\n  .align-self-md-end {\n    align-self: flex-end !important;\n  }\n  .align-self-md-center {\n    align-self: center !important;\n  }\n  .align-self-md-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-md-stretch {\n    align-self: stretch !important;\n  }\n}\n@media (min-width: 992px) {\n  .flex-lg-row {\n    flex-direction: row !important;\n  }\n  .flex-lg-column {\n    flex-direction: column !important;\n  }\n  .flex-lg-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-lg-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-lg-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-lg-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-lg-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .flex-lg-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-lg-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-lg-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-lg-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-lg-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .justify-content-lg-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-lg-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-lg-center {\n    justify-content: center !important;\n  }\n  .justify-content-lg-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-lg-around {\n    justify-content: space-around !important;\n  }\n  .align-items-lg-start {\n    align-items: flex-start !important;\n  }\n  .align-items-lg-end {\n    align-items: flex-end !important;\n  }\n  .align-items-lg-center {\n    align-items: center !important;\n  }\n  .align-items-lg-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-lg-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-lg-start {\n    align-content: flex-start !important;\n  }\n  .align-content-lg-end {\n    align-content: flex-end !important;\n  }\n  .align-content-lg-center {\n    align-content: center !important;\n  }\n  .align-content-lg-between {\n    align-content: space-between !important;\n  }\n  .align-content-lg-around {\n    align-content: space-around !important;\n  }\n  .align-content-lg-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-lg-auto {\n    align-self: auto !important;\n  }\n  .align-self-lg-start {\n    align-self: flex-start !important;\n  }\n  .align-self-lg-end {\n    align-self: flex-end !important;\n  }\n  .align-self-lg-center {\n    align-self: center !important;\n  }\n  .align-self-lg-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-lg-stretch {\n    align-self: stretch !important;\n  }\n}\n@media (min-width: 1200px) {\n  .flex-xl-row {\n    flex-direction: row !important;\n  }\n  .flex-xl-column {\n    flex-direction: column !important;\n  }\n  .flex-xl-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-xl-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-xl-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-xl-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-xl-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .flex-xl-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-xl-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-xl-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-xl-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-xl-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .justify-content-xl-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-xl-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-xl-center {\n    justify-content: center !important;\n  }\n  .justify-content-xl-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-xl-around {\n    justify-content: space-around !important;\n  }\n  .align-items-xl-start {\n    align-items: flex-start !important;\n  }\n  .align-items-xl-end {\n    align-items: flex-end !important;\n  }\n  .align-items-xl-center {\n    align-items: center !important;\n  }\n  .align-items-xl-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-xl-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-xl-start {\n    align-content: flex-start !important;\n  }\n  .align-content-xl-end {\n    align-content: flex-end !important;\n  }\n  .align-content-xl-center {\n    align-content: center !important;\n  }\n  .align-content-xl-between {\n    align-content: space-between !important;\n  }\n  .align-content-xl-around {\n    align-content: space-around !important;\n  }\n  .align-content-xl-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-xl-auto {\n    align-self: auto !important;\n  }\n  .align-self-xl-start {\n    align-self: flex-start !important;\n  }\n  .align-self-xl-end {\n    align-self: flex-end !important;\n  }\n  .align-self-xl-center {\n    align-self: center !important;\n  }\n  .align-self-xl-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-xl-stretch {\n    align-self: stretch !important;\n  }\n}\n.float-left {\n  float: left !important;\n}\n\n.float-right {\n  float: right !important;\n}\n\n.float-none {\n  float: none !important;\n}\n\n@media (min-width: 576px) {\n  .float-sm-left {\n    float: left !important;\n  }\n  .float-sm-right {\n    float: right !important;\n  }\n  .float-sm-none {\n    float: none !important;\n  }\n}\n@media (min-width: 768px) {\n  .float-md-left {\n    float: left !important;\n  }\n  .float-md-right {\n    float: right !important;\n  }\n  .float-md-none {\n    float: none !important;\n  }\n}\n@media (min-width: 992px) {\n  .float-lg-left {\n    float: left !important;\n  }\n  .float-lg-right {\n    float: right !important;\n  }\n  .float-lg-none {\n    float: none !important;\n  }\n}\n@media (min-width: 1200px) {\n  .float-xl-left {\n    float: left !important;\n  }\n  .float-xl-right {\n    float: right !important;\n  }\n  .float-xl-none {\n    float: none !important;\n  }\n}\n.overflow-auto {\n  overflow: auto !important;\n}\n\n.overflow-hidden {\n  overflow: hidden !important;\n}\n\n.position-static {\n  position: static !important;\n}\n\n.position-relative {\n  position: relative !important;\n}\n\n.position-absolute {\n  position: absolute !important;\n}\n\n.position-fixed {\n  position: fixed !important;\n}\n\n.position-sticky {\n  position: sticky !important;\n}\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1030;\n}\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1030;\n}\n\n@supports (position: sticky) {\n  .sticky-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n}\n\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n}\n\n.sr-only-focusable:active, .sr-only-focusable:focus {\n  position: static;\n  width: auto;\n  height: auto;\n  overflow: visible;\n  clip: auto;\n  white-space: normal;\n}\n\n.shadow-sm {\n  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;\n}\n\n.shadow {\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;\n}\n\n.shadow-lg {\n  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;\n}\n\n.shadow-none {\n  box-shadow: none !important;\n}\n\n.w-25 {\n  width: 25% !important;\n}\n\n.w-50 {\n  width: 50% !important;\n}\n\n.w-75 {\n  width: 75% !important;\n}\n\n.w-100 {\n  width: 100% !important;\n}\n\n.w-auto {\n  width: auto !important;\n}\n\n.h-25 {\n  height: 25% !important;\n}\n\n.h-50 {\n  height: 50% !important;\n}\n\n.h-75 {\n  height: 75% !important;\n}\n\n.h-100 {\n  height: 100% !important;\n}\n\n.h-auto {\n  height: auto !important;\n}\n\n.mw-100 {\n  max-width: 100% !important;\n}\n\n.mh-100 {\n  max-height: 100% !important;\n}\n\n.min-vw-100 {\n  min-width: 100vw !important;\n}\n\n.min-vh-100 {\n  min-height: 100vh !important;\n}\n\n.vw-100 {\n  width: 100vw !important;\n}\n\n.vh-100 {\n  height: 100vh !important;\n}\n\n.stretched-link::after {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1;\n  pointer-events: auto;\n  content: \"\";\n  background-color: rgba(0, 0, 0, 0);\n}\n\n.m-0 {\n  margin: 0 !important;\n}\n\n.mt-0,\n.my-0 {\n  margin-top: 0 !important;\n}\n\n.mr-0,\n.mx-0 {\n  margin-right: 0 !important;\n}\n\n.mb-0,\n.my-0 {\n  margin-bottom: 0 !important;\n}\n\n.ml-0,\n.mx-0 {\n  margin-left: 0 !important;\n}\n\n.m-1 {\n  margin: 0.25rem !important;\n}\n\n.mt-1,\n.my-1 {\n  margin-top: 0.25rem !important;\n}\n\n.mr-1,\n.mx-1 {\n  margin-right: 0.25rem !important;\n}\n\n.mb-1,\n.my-1 {\n  margin-bottom: 0.25rem !important;\n}\n\n.ml-1,\n.mx-1 {\n  margin-left: 0.25rem !important;\n}\n\n.m-2 {\n  margin: 0.5rem !important;\n}\n\n.mt-2,\n.my-2 {\n  margin-top: 0.5rem !important;\n}\n\n.mr-2,\n.mx-2 {\n  margin-right: 0.5rem !important;\n}\n\n.mb-2,\n.my-2 {\n  margin-bottom: 0.5rem !important;\n}\n\n.ml-2,\n.mx-2 {\n  margin-left: 0.5rem !important;\n}\n\n.m-3 {\n  margin: 1rem !important;\n}\n\n.mt-3,\n.my-3 {\n  margin-top: 1rem !important;\n}\n\n.mr-3,\n.mx-3 {\n  margin-right: 1rem !important;\n}\n\n.mb-3,\n.my-3 {\n  margin-bottom: 1rem !important;\n}\n\n.ml-3,\n.mx-3 {\n  margin-left: 1rem !important;\n}\n\n.m-4 {\n  margin: 1.5rem !important;\n}\n\n.mt-4,\n.my-4 {\n  margin-top: 1.5rem !important;\n}\n\n.mr-4,\n.mx-4 {\n  margin-right: 1.5rem !important;\n}\n\n.mb-4,\n.my-4 {\n  margin-bottom: 1.5rem !important;\n}\n\n.ml-4,\n.mx-4 {\n  margin-left: 1.5rem !important;\n}\n\n.m-5 {\n  margin: 3rem !important;\n}\n\n.mt-5,\n.my-5 {\n  margin-top: 3rem !important;\n}\n\n.mr-5,\n.mx-5 {\n  margin-right: 3rem !important;\n}\n\n.mb-5,\n.my-5 {\n  margin-bottom: 3rem !important;\n}\n\n.ml-5,\n.mx-5 {\n  margin-left: 3rem !important;\n}\n\n.p-0 {\n  padding: 0 !important;\n}\n\n.pt-0,\n.py-0 {\n  padding-top: 0 !important;\n}\n\n.pr-0,\n.px-0 {\n  padding-right: 0 !important;\n}\n\n.pb-0,\n.py-0 {\n  padding-bottom: 0 !important;\n}\n\n.pl-0,\n.px-0 {\n  padding-left: 0 !important;\n}\n\n.p-1 {\n  padding: 0.25rem !important;\n}\n\n.pt-1,\n.py-1 {\n  padding-top: 0.25rem !important;\n}\n\n.pr-1,\n.px-1 {\n  padding-right: 0.25rem !important;\n}\n\n.pb-1,\n.py-1 {\n  padding-bottom: 0.25rem !important;\n}\n\n.pl-1,\n.px-1 {\n  padding-left: 0.25rem !important;\n}\n\n.p-2 {\n  padding: 0.5rem !important;\n}\n\n.pt-2,\n.py-2 {\n  padding-top: 0.5rem !important;\n}\n\n.pr-2,\n.px-2 {\n  padding-right: 0.5rem !important;\n}\n\n.pb-2,\n.py-2 {\n  padding-bottom: 0.5rem !important;\n}\n\n.pl-2,\n.px-2 {\n  padding-left: 0.5rem !important;\n}\n\n.p-3 {\n  padding: 1rem !important;\n}\n\n.pt-3,\n.py-3 {\n  padding-top: 1rem !important;\n}\n\n.pr-3,\n.px-3 {\n  padding-right: 1rem !important;\n}\n\n.pb-3,\n.py-3 {\n  padding-bottom: 1rem !important;\n}\n\n.pl-3,\n.px-3 {\n  padding-left: 1rem !important;\n}\n\n.p-4 {\n  padding: 1.5rem !important;\n}\n\n.pt-4,\n.py-4 {\n  padding-top: 1.5rem !important;\n}\n\n.pr-4,\n.px-4 {\n  padding-right: 1.5rem !important;\n}\n\n.pb-4,\n.py-4 {\n  padding-bottom: 1.5rem !important;\n}\n\n.pl-4,\n.px-4 {\n  padding-left: 1.5rem !important;\n}\n\n.p-5 {\n  padding: 3rem !important;\n}\n\n.pt-5,\n.py-5 {\n  padding-top: 3rem !important;\n}\n\n.pr-5,\n.px-5 {\n  padding-right: 3rem !important;\n}\n\n.pb-5,\n.py-5 {\n  padding-bottom: 3rem !important;\n}\n\n.pl-5,\n.px-5 {\n  padding-left: 3rem !important;\n}\n\n.m-n1 {\n  margin: -0.25rem !important;\n}\n\n.mt-n1,\n.my-n1 {\n  margin-top: -0.25rem !important;\n}\n\n.mr-n1,\n.mx-n1 {\n  margin-right: -0.25rem !important;\n}\n\n.mb-n1,\n.my-n1 {\n  margin-bottom: -0.25rem !important;\n}\n\n.ml-n1,\n.mx-n1 {\n  margin-left: -0.25rem !important;\n}\n\n.m-n2 {\n  margin: -0.5rem !important;\n}\n\n.mt-n2,\n.my-n2 {\n  margin-top: -0.5rem !important;\n}\n\n.mr-n2,\n.mx-n2 {\n  margin-right: -0.5rem !important;\n}\n\n.mb-n2,\n.my-n2 {\n  margin-bottom: -0.5rem !important;\n}\n\n.ml-n2,\n.mx-n2 {\n  margin-left: -0.5rem !important;\n}\n\n.m-n3 {\n  margin: -1rem !important;\n}\n\n.mt-n3,\n.my-n3 {\n  margin-top: -1rem !important;\n}\n\n.mr-n3,\n.mx-n3 {\n  margin-right: -1rem !important;\n}\n\n.mb-n3,\n.my-n3 {\n  margin-bottom: -1rem !important;\n}\n\n.ml-n3,\n.mx-n3 {\n  margin-left: -1rem !important;\n}\n\n.m-n4 {\n  margin: -1.5rem !important;\n}\n\n.mt-n4,\n.my-n4 {\n  margin-top: -1.5rem !important;\n}\n\n.mr-n4,\n.mx-n4 {\n  margin-right: -1.5rem !important;\n}\n\n.mb-n4,\n.my-n4 {\n  margin-bottom: -1.5rem !important;\n}\n\n.ml-n4,\n.mx-n4 {\n  margin-left: -1.5rem !important;\n}\n\n.m-n5 {\n  margin: -3rem !important;\n}\n\n.mt-n5,\n.my-n5 {\n  margin-top: -3rem !important;\n}\n\n.mr-n5,\n.mx-n5 {\n  margin-right: -3rem !important;\n}\n\n.mb-n5,\n.my-n5 {\n  margin-bottom: -3rem !important;\n}\n\n.ml-n5,\n.mx-n5 {\n  margin-left: -3rem !important;\n}\n\n.m-auto {\n  margin: auto !important;\n}\n\n.mt-auto,\n.my-auto {\n  margin-top: auto !important;\n}\n\n.mr-auto,\n.mx-auto {\n  margin-right: auto !important;\n}\n\n.mb-auto,\n.my-auto {\n  margin-bottom: auto !important;\n}\n\n.ml-auto,\n.mx-auto {\n  margin-left: auto !important;\n}\n\n@media (min-width: 576px) {\n  .m-sm-0 {\n    margin: 0 !important;\n  }\n  .mt-sm-0,\n  .my-sm-0 {\n    margin-top: 0 !important;\n  }\n  .mr-sm-0,\n  .mx-sm-0 {\n    margin-right: 0 !important;\n  }\n  .mb-sm-0,\n  .my-sm-0 {\n    margin-bottom: 0 !important;\n  }\n  .ml-sm-0,\n  .mx-sm-0 {\n    margin-left: 0 !important;\n  }\n  .m-sm-1 {\n    margin: 0.25rem !important;\n  }\n  .mt-sm-1,\n  .my-sm-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mr-sm-1,\n  .mx-sm-1 {\n    margin-right: 0.25rem !important;\n  }\n  .mb-sm-1,\n  .my-sm-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .ml-sm-1,\n  .mx-sm-1 {\n    margin-left: 0.25rem !important;\n  }\n  .m-sm-2 {\n    margin: 0.5rem !important;\n  }\n  .mt-sm-2,\n  .my-sm-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mr-sm-2,\n  .mx-sm-2 {\n    margin-right: 0.5rem !important;\n  }\n  .mb-sm-2,\n  .my-sm-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .ml-sm-2,\n  .mx-sm-2 {\n    margin-left: 0.5rem !important;\n  }\n  .m-sm-3 {\n    margin: 1rem !important;\n  }\n  .mt-sm-3,\n  .my-sm-3 {\n    margin-top: 1rem !important;\n  }\n  .mr-sm-3,\n  .mx-sm-3 {\n    margin-right: 1rem !important;\n  }\n  .mb-sm-3,\n  .my-sm-3 {\n    margin-bottom: 1rem !important;\n  }\n  .ml-sm-3,\n  .mx-sm-3 {\n    margin-left: 1rem !important;\n  }\n  .m-sm-4 {\n    margin: 1.5rem !important;\n  }\n  .mt-sm-4,\n  .my-sm-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mr-sm-4,\n  .mx-sm-4 {\n    margin-right: 1.5rem !important;\n  }\n  .mb-sm-4,\n  .my-sm-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .ml-sm-4,\n  .mx-sm-4 {\n    margin-left: 1.5rem !important;\n  }\n  .m-sm-5 {\n    margin: 3rem !important;\n  }\n  .mt-sm-5,\n  .my-sm-5 {\n    margin-top: 3rem !important;\n  }\n  .mr-sm-5,\n  .mx-sm-5 {\n    margin-right: 3rem !important;\n  }\n  .mb-sm-5,\n  .my-sm-5 {\n    margin-bottom: 3rem !important;\n  }\n  .ml-sm-5,\n  .mx-sm-5 {\n    margin-left: 3rem !important;\n  }\n  .p-sm-0 {\n    padding: 0 !important;\n  }\n  .pt-sm-0,\n  .py-sm-0 {\n    padding-top: 0 !important;\n  }\n  .pr-sm-0,\n  .px-sm-0 {\n    padding-right: 0 !important;\n  }\n  .pb-sm-0,\n  .py-sm-0 {\n    padding-bottom: 0 !important;\n  }\n  .pl-sm-0,\n  .px-sm-0 {\n    padding-left: 0 !important;\n  }\n  .p-sm-1 {\n    padding: 0.25rem !important;\n  }\n  .pt-sm-1,\n  .py-sm-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pr-sm-1,\n  .px-sm-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pb-sm-1,\n  .py-sm-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pl-sm-1,\n  .px-sm-1 {\n    padding-left: 0.25rem !important;\n  }\n  .p-sm-2 {\n    padding: 0.5rem !important;\n  }\n  .pt-sm-2,\n  .py-sm-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pr-sm-2,\n  .px-sm-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pb-sm-2,\n  .py-sm-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pl-sm-2,\n  .px-sm-2 {\n    padding-left: 0.5rem !important;\n  }\n  .p-sm-3 {\n    padding: 1rem !important;\n  }\n  .pt-sm-3,\n  .py-sm-3 {\n    padding-top: 1rem !important;\n  }\n  .pr-sm-3,\n  .px-sm-3 {\n    padding-right: 1rem !important;\n  }\n  .pb-sm-3,\n  .py-sm-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pl-sm-3,\n  .px-sm-3 {\n    padding-left: 1rem !important;\n  }\n  .p-sm-4 {\n    padding: 1.5rem !important;\n  }\n  .pt-sm-4,\n  .py-sm-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pr-sm-4,\n  .px-sm-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pb-sm-4,\n  .py-sm-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pl-sm-4,\n  .px-sm-4 {\n    padding-left: 1.5rem !important;\n  }\n  .p-sm-5 {\n    padding: 3rem !important;\n  }\n  .pt-sm-5,\n  .py-sm-5 {\n    padding-top: 3rem !important;\n  }\n  .pr-sm-5,\n  .px-sm-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-sm-5,\n  .py-sm-5 {\n    padding-bottom: 3rem !important;\n  }\n  .pl-sm-5,\n  .px-sm-5 {\n    padding-left: 3rem !important;\n  }\n  .m-sm-n1 {\n    margin: -0.25rem !important;\n  }\n  .mt-sm-n1,\n  .my-sm-n1 {\n    margin-top: -0.25rem !important;\n  }\n  .mr-sm-n1,\n  .mx-sm-n1 {\n    margin-right: -0.25rem !important;\n  }\n  .mb-sm-n1,\n  .my-sm-n1 {\n    margin-bottom: -0.25rem !important;\n  }\n  .ml-sm-n1,\n  .mx-sm-n1 {\n    margin-left: -0.25rem !important;\n  }\n  .m-sm-n2 {\n    margin: -0.5rem !important;\n  }\n  .mt-sm-n2,\n  .my-sm-n2 {\n    margin-top: -0.5rem !important;\n  }\n  .mr-sm-n2,\n  .mx-sm-n2 {\n    margin-right: -0.5rem !important;\n  }\n  .mb-sm-n2,\n  .my-sm-n2 {\n    margin-bottom: -0.5rem !important;\n  }\n  .ml-sm-n2,\n  .mx-sm-n2 {\n    margin-left: -0.5rem !important;\n  }\n  .m-sm-n3 {\n    margin: -1rem !important;\n  }\n  .mt-sm-n3,\n  .my-sm-n3 {\n    margin-top: -1rem !important;\n  }\n  .mr-sm-n3,\n  .mx-sm-n3 {\n    margin-right: -1rem !important;\n  }\n  .mb-sm-n3,\n  .my-sm-n3 {\n    margin-bottom: -1rem !important;\n  }\n  .ml-sm-n3,\n  .mx-sm-n3 {\n    margin-left: -1rem !important;\n  }\n  .m-sm-n4 {\n    margin: -1.5rem !important;\n  }\n  .mt-sm-n4,\n  .my-sm-n4 {\n    margin-top: -1.5rem !important;\n  }\n  .mr-sm-n4,\n  .mx-sm-n4 {\n    margin-right: -1.5rem !important;\n  }\n  .mb-sm-n4,\n  .my-sm-n4 {\n    margin-bottom: -1.5rem !important;\n  }\n  .ml-sm-n4,\n  .mx-sm-n4 {\n    margin-left: -1.5rem !important;\n  }\n  .m-sm-n5 {\n    margin: -3rem !important;\n  }\n  .mt-sm-n5,\n  .my-sm-n5 {\n    margin-top: -3rem !important;\n  }\n  .mr-sm-n5,\n  .mx-sm-n5 {\n    margin-right: -3rem !important;\n  }\n  .mb-sm-n5,\n  .my-sm-n5 {\n    margin-bottom: -3rem !important;\n  }\n  .ml-sm-n5,\n  .mx-sm-n5 {\n    margin-left: -3rem !important;\n  }\n  .m-sm-auto {\n    margin: auto !important;\n  }\n  .mt-sm-auto,\n  .my-sm-auto {\n    margin-top: auto !important;\n  }\n  .mr-sm-auto,\n  .mx-sm-auto {\n    margin-right: auto !important;\n  }\n  .mb-sm-auto,\n  .my-sm-auto {\n    margin-bottom: auto !important;\n  }\n  .ml-sm-auto,\n  .mx-sm-auto {\n    margin-left: auto !important;\n  }\n}\n@media (min-width: 768px) {\n  .m-md-0 {\n    margin: 0 !important;\n  }\n  .mt-md-0,\n  .my-md-0 {\n    margin-top: 0 !important;\n  }\n  .mr-md-0,\n  .mx-md-0 {\n    margin-right: 0 !important;\n  }\n  .mb-md-0,\n  .my-md-0 {\n    margin-bottom: 0 !important;\n  }\n  .ml-md-0,\n  .mx-md-0 {\n    margin-left: 0 !important;\n  }\n  .m-md-1 {\n    margin: 0.25rem !important;\n  }\n  .mt-md-1,\n  .my-md-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mr-md-1,\n  .mx-md-1 {\n    margin-right: 0.25rem !important;\n  }\n  .mb-md-1,\n  .my-md-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .ml-md-1,\n  .mx-md-1 {\n    margin-left: 0.25rem !important;\n  }\n  .m-md-2 {\n    margin: 0.5rem !important;\n  }\n  .mt-md-2,\n  .my-md-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mr-md-2,\n  .mx-md-2 {\n    margin-right: 0.5rem !important;\n  }\n  .mb-md-2,\n  .my-md-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .ml-md-2,\n  .mx-md-2 {\n    margin-left: 0.5rem !important;\n  }\n  .m-md-3 {\n    margin: 1rem !important;\n  }\n  .mt-md-3,\n  .my-md-3 {\n    margin-top: 1rem !important;\n  }\n  .mr-md-3,\n  .mx-md-3 {\n    margin-right: 1rem !important;\n  }\n  .mb-md-3,\n  .my-md-3 {\n    margin-bottom: 1rem !important;\n  }\n  .ml-md-3,\n  .mx-md-3 {\n    margin-left: 1rem !important;\n  }\n  .m-md-4 {\n    margin: 1.5rem !important;\n  }\n  .mt-md-4,\n  .my-md-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mr-md-4,\n  .mx-md-4 {\n    margin-right: 1.5rem !important;\n  }\n  .mb-md-4,\n  .my-md-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .ml-md-4,\n  .mx-md-4 {\n    margin-left: 1.5rem !important;\n  }\n  .m-md-5 {\n    margin: 3rem !important;\n  }\n  .mt-md-5,\n  .my-md-5 {\n    margin-top: 3rem !important;\n  }\n  .mr-md-5,\n  .mx-md-5 {\n    margin-right: 3rem !important;\n  }\n  .mb-md-5,\n  .my-md-5 {\n    margin-bottom: 3rem !important;\n  }\n  .ml-md-5,\n  .mx-md-5 {\n    margin-left: 3rem !important;\n  }\n  .p-md-0 {\n    padding: 0 !important;\n  }\n  .pt-md-0,\n  .py-md-0 {\n    padding-top: 0 !important;\n  }\n  .pr-md-0,\n  .px-md-0 {\n    padding-right: 0 !important;\n  }\n  .pb-md-0,\n  .py-md-0 {\n    padding-bottom: 0 !important;\n  }\n  .pl-md-0,\n  .px-md-0 {\n    padding-left: 0 !important;\n  }\n  .p-md-1 {\n    padding: 0.25rem !important;\n  }\n  .pt-md-1,\n  .py-md-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pr-md-1,\n  .px-md-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pb-md-1,\n  .py-md-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pl-md-1,\n  .px-md-1 {\n    padding-left: 0.25rem !important;\n  }\n  .p-md-2 {\n    padding: 0.5rem !important;\n  }\n  .pt-md-2,\n  .py-md-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pr-md-2,\n  .px-md-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pb-md-2,\n  .py-md-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pl-md-2,\n  .px-md-2 {\n    padding-left: 0.5rem !important;\n  }\n  .p-md-3 {\n    padding: 1rem !important;\n  }\n  .pt-md-3,\n  .py-md-3 {\n    padding-top: 1rem !important;\n  }\n  .pr-md-3,\n  .px-md-3 {\n    padding-right: 1rem !important;\n  }\n  .pb-md-3,\n  .py-md-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pl-md-3,\n  .px-md-3 {\n    padding-left: 1rem !important;\n  }\n  .p-md-4 {\n    padding: 1.5rem !important;\n  }\n  .pt-md-4,\n  .py-md-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pr-md-4,\n  .px-md-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pb-md-4,\n  .py-md-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pl-md-4,\n  .px-md-4 {\n    padding-left: 1.5rem !important;\n  }\n  .p-md-5 {\n    padding: 3rem !important;\n  }\n  .pt-md-5,\n  .py-md-5 {\n    padding-top: 3rem !important;\n  }\n  .pr-md-5,\n  .px-md-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-md-5,\n  .py-md-5 {\n    padding-bottom: 3rem !important;\n  }\n  .pl-md-5,\n  .px-md-5 {\n    padding-left: 3rem !important;\n  }\n  .m-md-n1 {\n    margin: -0.25rem !important;\n  }\n  .mt-md-n1,\n  .my-md-n1 {\n    margin-top: -0.25rem !important;\n  }\n  .mr-md-n1,\n  .mx-md-n1 {\n    margin-right: -0.25rem !important;\n  }\n  .mb-md-n1,\n  .my-md-n1 {\n    margin-bottom: -0.25rem !important;\n  }\n  .ml-md-n1,\n  .mx-md-n1 {\n    margin-left: -0.25rem !important;\n  }\n  .m-md-n2 {\n    margin: -0.5rem !important;\n  }\n  .mt-md-n2,\n  .my-md-n2 {\n    margin-top: -0.5rem !important;\n  }\n  .mr-md-n2,\n  .mx-md-n2 {\n    margin-right: -0.5rem !important;\n  }\n  .mb-md-n2,\n  .my-md-n2 {\n    margin-bottom: -0.5rem !important;\n  }\n  .ml-md-n2,\n  .mx-md-n2 {\n    margin-left: -0.5rem !important;\n  }\n  .m-md-n3 {\n    margin: -1rem !important;\n  }\n  .mt-md-n3,\n  .my-md-n3 {\n    margin-top: -1rem !important;\n  }\n  .mr-md-n3,\n  .mx-md-n3 {\n    margin-right: -1rem !important;\n  }\n  .mb-md-n3,\n  .my-md-n3 {\n    margin-bottom: -1rem !important;\n  }\n  .ml-md-n3,\n  .mx-md-n3 {\n    margin-left: -1rem !important;\n  }\n  .m-md-n4 {\n    margin: -1.5rem !important;\n  }\n  .mt-md-n4,\n  .my-md-n4 {\n    margin-top: -1.5rem !important;\n  }\n  .mr-md-n4,\n  .mx-md-n4 {\n    margin-right: -1.5rem !important;\n  }\n  .mb-md-n4,\n  .my-md-n4 {\n    margin-bottom: -1.5rem !important;\n  }\n  .ml-md-n4,\n  .mx-md-n4 {\n    margin-left: -1.5rem !important;\n  }\n  .m-md-n5 {\n    margin: -3rem !important;\n  }\n  .mt-md-n5,\n  .my-md-n5 {\n    margin-top: -3rem !important;\n  }\n  .mr-md-n5,\n  .mx-md-n5 {\n    margin-right: -3rem !important;\n  }\n  .mb-md-n5,\n  .my-md-n5 {\n    margin-bottom: -3rem !important;\n  }\n  .ml-md-n5,\n  .mx-md-n5 {\n    margin-left: -3rem !important;\n  }\n  .m-md-auto {\n    margin: auto !important;\n  }\n  .mt-md-auto,\n  .my-md-auto {\n    margin-top: auto !important;\n  }\n  .mr-md-auto,\n  .mx-md-auto {\n    margin-right: auto !important;\n  }\n  .mb-md-auto,\n  .my-md-auto {\n    margin-bottom: auto !important;\n  }\n  .ml-md-auto,\n  .mx-md-auto {\n    margin-left: auto !important;\n  }\n}\n@media (min-width: 992px) {\n  .m-lg-0 {\n    margin: 0 !important;\n  }\n  .mt-lg-0,\n  .my-lg-0 {\n    margin-top: 0 !important;\n  }\n  .mr-lg-0,\n  .mx-lg-0 {\n    margin-right: 0 !important;\n  }\n  .mb-lg-0,\n  .my-lg-0 {\n    margin-bottom: 0 !important;\n  }\n  .ml-lg-0,\n  .mx-lg-0 {\n    margin-left: 0 !important;\n  }\n  .m-lg-1 {\n    margin: 0.25rem !important;\n  }\n  .mt-lg-1,\n  .my-lg-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mr-lg-1,\n  .mx-lg-1 {\n    margin-right: 0.25rem !important;\n  }\n  .mb-lg-1,\n  .my-lg-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .ml-lg-1,\n  .mx-lg-1 {\n    margin-left: 0.25rem !important;\n  }\n  .m-lg-2 {\n    margin: 0.5rem !important;\n  }\n  .mt-lg-2,\n  .my-lg-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mr-lg-2,\n  .mx-lg-2 {\n    margin-right: 0.5rem !important;\n  }\n  .mb-lg-2,\n  .my-lg-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .ml-lg-2,\n  .mx-lg-2 {\n    margin-left: 0.5rem !important;\n  }\n  .m-lg-3 {\n    margin: 1rem !important;\n  }\n  .mt-lg-3,\n  .my-lg-3 {\n    margin-top: 1rem !important;\n  }\n  .mr-lg-3,\n  .mx-lg-3 {\n    margin-right: 1rem !important;\n  }\n  .mb-lg-3,\n  .my-lg-3 {\n    margin-bottom: 1rem !important;\n  }\n  .ml-lg-3,\n  .mx-lg-3 {\n    margin-left: 1rem !important;\n  }\n  .m-lg-4 {\n    margin: 1.5rem !important;\n  }\n  .mt-lg-4,\n  .my-lg-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mr-lg-4,\n  .mx-lg-4 {\n    margin-right: 1.5rem !important;\n  }\n  .mb-lg-4,\n  .my-lg-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .ml-lg-4,\n  .mx-lg-4 {\n    margin-left: 1.5rem !important;\n  }\n  .m-lg-5 {\n    margin: 3rem !important;\n  }\n  .mt-lg-5,\n  .my-lg-5 {\n    margin-top: 3rem !important;\n  }\n  .mr-lg-5,\n  .mx-lg-5 {\n    margin-right: 3rem !important;\n  }\n  .mb-lg-5,\n  .my-lg-5 {\n    margin-bottom: 3rem !important;\n  }\n  .ml-lg-5,\n  .mx-lg-5 {\n    margin-left: 3rem !important;\n  }\n  .p-lg-0 {\n    padding: 0 !important;\n  }\n  .pt-lg-0,\n  .py-lg-0 {\n    padding-top: 0 !important;\n  }\n  .pr-lg-0,\n  .px-lg-0 {\n    padding-right: 0 !important;\n  }\n  .pb-lg-0,\n  .py-lg-0 {\n    padding-bottom: 0 !important;\n  }\n  .pl-lg-0,\n  .px-lg-0 {\n    padding-left: 0 !important;\n  }\n  .p-lg-1 {\n    padding: 0.25rem !important;\n  }\n  .pt-lg-1,\n  .py-lg-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pr-lg-1,\n  .px-lg-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pb-lg-1,\n  .py-lg-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pl-lg-1,\n  .px-lg-1 {\n    padding-left: 0.25rem !important;\n  }\n  .p-lg-2 {\n    padding: 0.5rem !important;\n  }\n  .pt-lg-2,\n  .py-lg-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pr-lg-2,\n  .px-lg-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pb-lg-2,\n  .py-lg-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pl-lg-2,\n  .px-lg-2 {\n    padding-left: 0.5rem !important;\n  }\n  .p-lg-3 {\n    padding: 1rem !important;\n  }\n  .pt-lg-3,\n  .py-lg-3 {\n    padding-top: 1rem !important;\n  }\n  .pr-lg-3,\n  .px-lg-3 {\n    padding-right: 1rem !important;\n  }\n  .pb-lg-3,\n  .py-lg-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pl-lg-3,\n  .px-lg-3 {\n    padding-left: 1rem !important;\n  }\n  .p-lg-4 {\n    padding: 1.5rem !important;\n  }\n  .pt-lg-4,\n  .py-lg-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pr-lg-4,\n  .px-lg-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pb-lg-4,\n  .py-lg-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pl-lg-4,\n  .px-lg-4 {\n    padding-left: 1.5rem !important;\n  }\n  .p-lg-5 {\n    padding: 3rem !important;\n  }\n  .pt-lg-5,\n  .py-lg-5 {\n    padding-top: 3rem !important;\n  }\n  .pr-lg-5,\n  .px-lg-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-lg-5,\n  .py-lg-5 {\n    padding-bottom: 3rem !important;\n  }\n  .pl-lg-5,\n  .px-lg-5 {\n    padding-left: 3rem !important;\n  }\n  .m-lg-n1 {\n    margin: -0.25rem !important;\n  }\n  .mt-lg-n1,\n  .my-lg-n1 {\n    margin-top: -0.25rem !important;\n  }\n  .mr-lg-n1,\n  .mx-lg-n1 {\n    margin-right: -0.25rem !important;\n  }\n  .mb-lg-n1,\n  .my-lg-n1 {\n    margin-bottom: -0.25rem !important;\n  }\n  .ml-lg-n1,\n  .mx-lg-n1 {\n    margin-left: -0.25rem !important;\n  }\n  .m-lg-n2 {\n    margin: -0.5rem !important;\n  }\n  .mt-lg-n2,\n  .my-lg-n2 {\n    margin-top: -0.5rem !important;\n  }\n  .mr-lg-n2,\n  .mx-lg-n2 {\n    margin-right: -0.5rem !important;\n  }\n  .mb-lg-n2,\n  .my-lg-n2 {\n    margin-bottom: -0.5rem !important;\n  }\n  .ml-lg-n2,\n  .mx-lg-n2 {\n    margin-left: -0.5rem !important;\n  }\n  .m-lg-n3 {\n    margin: -1rem !important;\n  }\n  .mt-lg-n3,\n  .my-lg-n3 {\n    margin-top: -1rem !important;\n  }\n  .mr-lg-n3,\n  .mx-lg-n3 {\n    margin-right: -1rem !important;\n  }\n  .mb-lg-n3,\n  .my-lg-n3 {\n    margin-bottom: -1rem !important;\n  }\n  .ml-lg-n3,\n  .mx-lg-n3 {\n    margin-left: -1rem !important;\n  }\n  .m-lg-n4 {\n    margin: -1.5rem !important;\n  }\n  .mt-lg-n4,\n  .my-lg-n4 {\n    margin-top: -1.5rem !important;\n  }\n  .mr-lg-n4,\n  .mx-lg-n4 {\n    margin-right: -1.5rem !important;\n  }\n  .mb-lg-n4,\n  .my-lg-n4 {\n    margin-bottom: -1.5rem !important;\n  }\n  .ml-lg-n4,\n  .mx-lg-n4 {\n    margin-left: -1.5rem !important;\n  }\n  .m-lg-n5 {\n    margin: -3rem !important;\n  }\n  .mt-lg-n5,\n  .my-lg-n5 {\n    margin-top: -3rem !important;\n  }\n  .mr-lg-n5,\n  .mx-lg-n5 {\n    margin-right: -3rem !important;\n  }\n  .mb-lg-n5,\n  .my-lg-n5 {\n    margin-bottom: -3rem !important;\n  }\n  .ml-lg-n5,\n  .mx-lg-n5 {\n    margin-left: -3rem !important;\n  }\n  .m-lg-auto {\n    margin: auto !important;\n  }\n  .mt-lg-auto,\n  .my-lg-auto {\n    margin-top: auto !important;\n  }\n  .mr-lg-auto,\n  .mx-lg-auto {\n    margin-right: auto !important;\n  }\n  .mb-lg-auto,\n  .my-lg-auto {\n    margin-bottom: auto !important;\n  }\n  .ml-lg-auto,\n  .mx-lg-auto {\n    margin-left: auto !important;\n  }\n}\n@media (min-width: 1200px) {\n  .m-xl-0 {\n    margin: 0 !important;\n  }\n  .mt-xl-0,\n  .my-xl-0 {\n    margin-top: 0 !important;\n  }\n  .mr-xl-0,\n  .mx-xl-0 {\n    margin-right: 0 !important;\n  }\n  .mb-xl-0,\n  .my-xl-0 {\n    margin-bottom: 0 !important;\n  }\n  .ml-xl-0,\n  .mx-xl-0 {\n    margin-left: 0 !important;\n  }\n  .m-xl-1 {\n    margin: 0.25rem !important;\n  }\n  .mt-xl-1,\n  .my-xl-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mr-xl-1,\n  .mx-xl-1 {\n    margin-right: 0.25rem !important;\n  }\n  .mb-xl-1,\n  .my-xl-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .ml-xl-1,\n  .mx-xl-1 {\n    margin-left: 0.25rem !important;\n  }\n  .m-xl-2 {\n    margin: 0.5rem !important;\n  }\n  .mt-xl-2,\n  .my-xl-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mr-xl-2,\n  .mx-xl-2 {\n    margin-right: 0.5rem !important;\n  }\n  .mb-xl-2,\n  .my-xl-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .ml-xl-2,\n  .mx-xl-2 {\n    margin-left: 0.5rem !important;\n  }\n  .m-xl-3 {\n    margin: 1rem !important;\n  }\n  .mt-xl-3,\n  .my-xl-3 {\n    margin-top: 1rem !important;\n  }\n  .mr-xl-3,\n  .mx-xl-3 {\n    margin-right: 1rem !important;\n  }\n  .mb-xl-3,\n  .my-xl-3 {\n    margin-bottom: 1rem !important;\n  }\n  .ml-xl-3,\n  .mx-xl-3 {\n    margin-left: 1rem !important;\n  }\n  .m-xl-4 {\n    margin: 1.5rem !important;\n  }\n  .mt-xl-4,\n  .my-xl-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mr-xl-4,\n  .mx-xl-4 {\n    margin-right: 1.5rem !important;\n  }\n  .mb-xl-4,\n  .my-xl-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .ml-xl-4,\n  .mx-xl-4 {\n    margin-left: 1.5rem !important;\n  }\n  .m-xl-5 {\n    margin: 3rem !important;\n  }\n  .mt-xl-5,\n  .my-xl-5 {\n    margin-top: 3rem !important;\n  }\n  .mr-xl-5,\n  .mx-xl-5 {\n    margin-right: 3rem !important;\n  }\n  .mb-xl-5,\n  .my-xl-5 {\n    margin-bottom: 3rem !important;\n  }\n  .ml-xl-5,\n  .mx-xl-5 {\n    margin-left: 3rem !important;\n  }\n  .p-xl-0 {\n    padding: 0 !important;\n  }\n  .pt-xl-0,\n  .py-xl-0 {\n    padding-top: 0 !important;\n  }\n  .pr-xl-0,\n  .px-xl-0 {\n    padding-right: 0 !important;\n  }\n  .pb-xl-0,\n  .py-xl-0 {\n    padding-bottom: 0 !important;\n  }\n  .pl-xl-0,\n  .px-xl-0 {\n    padding-left: 0 !important;\n  }\n  .p-xl-1 {\n    padding: 0.25rem !important;\n  }\n  .pt-xl-1,\n  .py-xl-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pr-xl-1,\n  .px-xl-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pb-xl-1,\n  .py-xl-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pl-xl-1,\n  .px-xl-1 {\n    padding-left: 0.25rem !important;\n  }\n  .p-xl-2 {\n    padding: 0.5rem !important;\n  }\n  .pt-xl-2,\n  .py-xl-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pr-xl-2,\n  .px-xl-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pb-xl-2,\n  .py-xl-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pl-xl-2,\n  .px-xl-2 {\n    padding-left: 0.5rem !important;\n  }\n  .p-xl-3 {\n    padding: 1rem !important;\n  }\n  .pt-xl-3,\n  .py-xl-3 {\n    padding-top: 1rem !important;\n  }\n  .pr-xl-3,\n  .px-xl-3 {\n    padding-right: 1rem !important;\n  }\n  .pb-xl-3,\n  .py-xl-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pl-xl-3,\n  .px-xl-3 {\n    padding-left: 1rem !important;\n  }\n  .p-xl-4 {\n    padding: 1.5rem !important;\n  }\n  .pt-xl-4,\n  .py-xl-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pr-xl-4,\n  .px-xl-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pb-xl-4,\n  .py-xl-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pl-xl-4,\n  .px-xl-4 {\n    padding-left: 1.5rem !important;\n  }\n  .p-xl-5 {\n    padding: 3rem !important;\n  }\n  .pt-xl-5,\n  .py-xl-5 {\n    padding-top: 3rem !important;\n  }\n  .pr-xl-5,\n  .px-xl-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-xl-5,\n  .py-xl-5 {\n    padding-bottom: 3rem !important;\n  }\n  .pl-xl-5,\n  .px-xl-5 {\n    padding-left: 3rem !important;\n  }\n  .m-xl-n1 {\n    margin: -0.25rem !important;\n  }\n  .mt-xl-n1,\n  .my-xl-n1 {\n    margin-top: -0.25rem !important;\n  }\n  .mr-xl-n1,\n  .mx-xl-n1 {\n    margin-right: -0.25rem !important;\n  }\n  .mb-xl-n1,\n  .my-xl-n1 {\n    margin-bottom: -0.25rem !important;\n  }\n  .ml-xl-n1,\n  .mx-xl-n1 {\n    margin-left: -0.25rem !important;\n  }\n  .m-xl-n2 {\n    margin: -0.5rem !important;\n  }\n  .mt-xl-n2,\n  .my-xl-n2 {\n    margin-top: -0.5rem !important;\n  }\n  .mr-xl-n2,\n  .mx-xl-n2 {\n    margin-right: -0.5rem !important;\n  }\n  .mb-xl-n2,\n  .my-xl-n2 {\n    margin-bottom: -0.5rem !important;\n  }\n  .ml-xl-n2,\n  .mx-xl-n2 {\n    margin-left: -0.5rem !important;\n  }\n  .m-xl-n3 {\n    margin: -1rem !important;\n  }\n  .mt-xl-n3,\n  .my-xl-n3 {\n    margin-top: -1rem !important;\n  }\n  .mr-xl-n3,\n  .mx-xl-n3 {\n    margin-right: -1rem !important;\n  }\n  .mb-xl-n3,\n  .my-xl-n3 {\n    margin-bottom: -1rem !important;\n  }\n  .ml-xl-n3,\n  .mx-xl-n3 {\n    margin-left: -1rem !important;\n  }\n  .m-xl-n4 {\n    margin: -1.5rem !important;\n  }\n  .mt-xl-n4,\n  .my-xl-n4 {\n    margin-top: -1.5rem !important;\n  }\n  .mr-xl-n4,\n  .mx-xl-n4 {\n    margin-right: -1.5rem !important;\n  }\n  .mb-xl-n4,\n  .my-xl-n4 {\n    margin-bottom: -1.5rem !important;\n  }\n  .ml-xl-n4,\n  .mx-xl-n4 {\n    margin-left: -1.5rem !important;\n  }\n  .m-xl-n5 {\n    margin: -3rem !important;\n  }\n  .mt-xl-n5,\n  .my-xl-n5 {\n    margin-top: -3rem !important;\n  }\n  .mr-xl-n5,\n  .mx-xl-n5 {\n    margin-right: -3rem !important;\n  }\n  .mb-xl-n5,\n  .my-xl-n5 {\n    margin-bottom: -3rem !important;\n  }\n  .ml-xl-n5,\n  .mx-xl-n5 {\n    margin-left: -3rem !important;\n  }\n  .m-xl-auto {\n    margin: auto !important;\n  }\n  .mt-xl-auto,\n  .my-xl-auto {\n    margin-top: auto !important;\n  }\n  .mr-xl-auto,\n  .mx-xl-auto {\n    margin-right: auto !important;\n  }\n  .mb-xl-auto,\n  .my-xl-auto {\n    margin-bottom: auto !important;\n  }\n  .ml-xl-auto,\n  .mx-xl-auto {\n    margin-left: auto !important;\n  }\n}\n.text-monospace {\n  font-family: SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !important;\n}\n\n.text-justify {\n  text-align: justify !important;\n}\n\n.text-wrap {\n  white-space: normal !important;\n}\n\n.text-nowrap {\n  white-space: nowrap !important;\n}\n\n.text-truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.text-left {\n  text-align: left !important;\n}\n\n.text-right {\n  text-align: right !important;\n}\n\n.text-center {\n  text-align: center !important;\n}\n\n@media (min-width: 576px) {\n  .text-sm-left {\n    text-align: left !important;\n  }\n  .text-sm-right {\n    text-align: right !important;\n  }\n  .text-sm-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 768px) {\n  .text-md-left {\n    text-align: left !important;\n  }\n  .text-md-right {\n    text-align: right !important;\n  }\n  .text-md-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 992px) {\n  .text-lg-left {\n    text-align: left !important;\n  }\n  .text-lg-right {\n    text-align: right !important;\n  }\n  .text-lg-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 1200px) {\n  .text-xl-left {\n    text-align: left !important;\n  }\n  .text-xl-right {\n    text-align: right !important;\n  }\n  .text-xl-center {\n    text-align: center !important;\n  }\n}\n.text-lowercase {\n  text-transform: lowercase !important;\n}\n\n.text-uppercase {\n  text-transform: uppercase !important;\n}\n\n.text-capitalize {\n  text-transform: capitalize !important;\n}\n\n.font-weight-light {\n  font-weight: 300 !important;\n}\n\n.font-weight-lighter {\n  font-weight: lighter !important;\n}\n\n.font-weight-normal {\n  font-weight: 400 !important;\n}\n\n.font-weight-bold {\n  font-weight: 700 !important;\n}\n\n.font-weight-bolder {\n  font-weight: bolder !important;\n}\n\n.font-italic {\n  font-style: italic !important;\n}\n\n.text-white {\n  color: #fff !important;\n}\n\n.text-primary {\n  color: #007bff !important;\n}\n\na.text-primary:hover, a.text-primary:focus {\n  color: #0056b3 !important;\n}\n\n.text-secondary {\n  color: #6c757d !important;\n}\n\na.text-secondary:hover, a.text-secondary:focus {\n  color: #494f54 !important;\n}\n\n.text-success {\n  color: #28a745 !important;\n}\n\na.text-success:hover, a.text-success:focus {\n  color: #19692c !important;\n}\n\n.text-info {\n  color: #17a2b8 !important;\n}\n\na.text-info:hover, a.text-info:focus {\n  color: #0f6674 !important;\n}\n\n.text-warning {\n  color: #ffc107 !important;\n}\n\na.text-warning:hover, a.text-warning:focus {\n  color: #ba8b00 !important;\n}\n\n.text-danger {\n  color: #dc3545 !important;\n}\n\na.text-danger:hover, a.text-danger:focus {\n  color: #a71d2a !important;\n}\n\n.text-light {\n  color: #f8f9fa !important;\n}\n\na.text-light:hover, a.text-light:focus {\n  color: #cbd3da !important;\n}\n\n.text-dark {\n  color: #343a40 !important;\n}\n\na.text-dark:hover, a.text-dark:focus {\n  color: #121416 !important;\n}\n\n.text-body {\n  color: #212529 !important;\n}\n\n.text-muted {\n  color: #6c757d !important;\n}\n\n.text-black-50 {\n  color: rgba(0, 0, 0, 0.5) !important;\n}\n\n.text-white-50 {\n  color: rgba(255, 255, 255, 0.5) !important;\n}\n\n.text-hide {\n  font: 0/0 a;\n  color: transparent;\n  text-shadow: none;\n  background-color: transparent;\n  border: 0;\n}\n\n.text-decoration-none {\n  text-decoration: none !important;\n}\n\n.text-break {\n  word-break: break-word !important;\n  overflow-wrap: break-word !important;\n}\n\n.text-reset {\n  color: inherit !important;\n}\n\n.visible {\n  visibility: visible !important;\n}\n\n.invisible {\n  visibility: hidden !important;\n}\n\n@media print {\n  *,\n  *::before,\n  *::after {\n    text-shadow: none !important;\n    box-shadow: none !important;\n  }\n  a:not(.btn) {\n    text-decoration: underline;\n  }\n  abbr[title]::after {\n    content: \" (\" attr(title) \")\";\n  }\n  pre {\n    white-space: pre-wrap !important;\n  }\n  pre,\n  blockquote {\n    border: 1px solid #adb5bd;\n    page-break-inside: avoid;\n  }\n  thead {\n    display: table-header-group;\n  }\n  tr,\n  img {\n    page-break-inside: avoid;\n  }\n  p,\n  h2,\n  h3 {\n    orphans: 3;\n    widows: 3;\n  }\n  h2,\n  h3 {\n    page-break-after: avoid;\n  }\n  @page {\n    size: a3;\n  }\n  body {\n    min-width: 992px !important;\n  }\n  .container {\n    min-width: 992px !important;\n  }\n  .navbar {\n    display: none;\n  }\n  .badge {\n    border: 1px solid #000;\n  }\n  .table {\n    border-collapse: collapse !important;\n  }\n  .table td,\n  .table th {\n    background-color: #fff !important;\n  }\n  .table-bordered th,\n  .table-bordered td {\n    border: 1px solid #dee2e6 !important;\n  }\n  .table-dark {\n    color: inherit;\n  }\n  .table-dark th,\n  .table-dark td,\n  .table-dark thead th,\n  .table-dark tbody + tbody {\n    border-color: #dee2e6;\n  }\n  .table .thead-dark th {\n    color: inherit;\n    border-color: #dee2e6;\n  }\n}\nhtml {\n  box-sizing: border-box;\n}\n\n*,\n*:before,\n*:after {\n  box-sizing: inherit;\n}\n\n.custom-row::before, .custom-row::after {\n  display: table;\n  content: \" \";\n  clear: both;\n}\n\n.employee-info {\n  margin-bottom: 30px;\n}\n\n.employee-info .column {\n  width: 33.33%;\n  float: left;\n}\n\n.basic-salary-info .column,\n.benificiary-info .column,\n.salary-details-info .column,\n.total-benificiary-info .column,\n.net-salary-info .column,\n.earning-deduction-info .column {\n  width: 50%;\n  float: left;\n}\n\n.earnings-info table,\n.deductions-info table,\n.earning-deduction-info table {\n  width: 91%;\n  border-collapse: collapse;\n}\n\n.currency-symbol {\n  font-family: DejaVu Sans, sans-serif !important;\n}", "/*!\r\n * Bootstrap v4.3.1 (https://getbootstrap.com/)\r\n * Copyright 2011-2019 The Bootstrap Authors\r\n * Copyright 2011-2019 Twitter, Inc.\r\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\r\n */\r\n\r\n@import \"functions\";\r\n@import \"variables\";\r\n@import \"mixins\";\r\n@import \"root\";\r\n@import \"reboot\";\r\n@import \"type\";\r\n@import \"images\";\r\n@import \"code\";\r\n@import \"grid\";\r\n@import \"tables\";\r\n@import \"forms\";\r\n@import \"buttons\";\r\n@import \"transitions\";\r\n@import \"dropdown\";\r\n@import \"button-group\";\r\n@import \"input-group\";\r\n@import \"custom-forms\";\r\n@import \"nav\";\r\n@import \"navbar\";\r\n@import \"card\";\r\n@import \"breadcrumb\";\r\n@import \"pagination\";\r\n@import \"badge\";\r\n@import \"jumbotron\";\r\n@import \"alert\";\r\n@import \"progress\";\r\n@import \"media\";\r\n@import \"list-group\";\r\n@import \"close\";\r\n@import \"toasts\";\r\n@import \"modal\";\r\n@import \"tooltip\";\r\n@import \"popover\";\r\n@import \"carousel\";\r\n@import \"spinners\";\r\n@import \"utilities\";\r\n@import \"print\";\r\n", ":root {\r\n  // Custom variable values only support SassScript inside `#{}`.\r\n  @each $color, $value in $colors {\r\n    --#{$color}: #{$value};\r\n  }\r\n\r\n  @each $color, $value in $theme-colors {\r\n    --#{$color}: #{$value};\r\n  }\r\n\r\n  @each $bp, $value in $grid-breakpoints {\r\n    --breakpoint-#{$bp}: #{$value};\r\n  }\r\n\r\n  // Use `inspect` for lists so that quoted items keep the quotes.\r\n  // See https://github.com/sass/sass/issues/2383#issuecomment-336349172\r\n  --font-family-sans-serif: #{inspect($font-family-sans-serif)};\r\n  --font-family-monospace: #{inspect($font-family-monospace)};\r\n}\r\n", "// stylelint-disable at-rule-no-vendor-prefix, declaration-no-important, selector-no-qualifying-type, property-no-vendor-prefix\r\n\r\n// Reboot\r\n//\r\n// Normalization of HTML elements, manually forked from Normalize.css to remove\r\n// styles targeting irrelevant browsers while applying new styles.\r\n//\r\n// Normalize is licensed MIT. https://github.com/necolas/normalize.css\r\n\r\n\r\n// Document\r\n//\r\n// 1. Change from `box-sizing: content-box` so that `width` is not affected by `padding` or `border`.\r\n// 2. Change the default font family in all browsers.\r\n// 3. Correct the line height in all browsers.\r\n// 4. Prevent adjustments of font size after orientation changes in IE on Windows Phone and in iOS.\r\n// 5. Change the default tap highlight to be completely transparent in iOS.\r\n\r\n*,\r\n*::before,\r\n*::after {\r\n  box-sizing: border-box; // 1\r\n}\r\n\r\nhtml {\r\n  font-family: sans-serif; // 2\r\n  line-height: 1.15; // 3\r\n  -webkit-text-size-adjust: 100%; // 4\r\n  -webkit-tap-highlight-color: rgba($black, 0); // 5\r\n}\r\n\r\n// Shim for \"new\" HTML5 structural elements to display correctly (IE10, older browsers)\r\n// TODO: remove in v5\r\n// stylelint-disable-next-line selector-list-comma-newline-after\r\narticle, aside, figcaption, figure, footer, header, hgroup, main, nav, section {\r\n  display: block;\r\n}\r\n\r\n// Body\r\n//\r\n// 1. Remove the margin in all browsers.\r\n// 2. As a best practice, apply a default `background-color`.\r\n// 3. Set an explicit initial text-align value so that we can later use\r\n//    the `inherit` value on things like `<th>` elements.\r\n\r\nbody {\r\n  margin: 0; // 1\r\n  font-family: $font-family-base;\r\n  @include font-size($font-size-base);\r\n  font-weight: $font-weight-base;\r\n  line-height: $line-height-base;\r\n  color: $body-color;\r\n  text-align: left; // 3\r\n  background-color: $body-bg; // 2\r\n}\r\n\r\n// Suppress the focus outline on elements that cannot be accessed via keyboard.\r\n// This prevents an unwanted focus outline from appearing around elements that\r\n// might still respond to pointer events.\r\n//\r\n// Credit: https://github.com/suitcss/base\r\n[tabindex=\"-1\"]:focus {\r\n  outline: 0 !important;\r\n}\r\n\r\n\r\n// Content grouping\r\n//\r\n// 1. Add the correct box sizing in Firefox.\r\n// 2. Show the overflow in Edge and IE.\r\n\r\nhr {\r\n  box-sizing: content-box; // 1\r\n  height: 0; // 1\r\n  overflow: visible; // 2\r\n}\r\n\r\n\r\n//\r\n// Typography\r\n//\r\n\r\n// Remove top margins from headings\r\n//\r\n// By default, `<h1>`-`<h6>` all receive top and bottom margins. We nuke the top\r\n// margin for easier control within type scales as it avoids margin collapsing.\r\n// stylelint-disable-next-line selector-list-comma-newline-after\r\nh1, h2, h3, h4, h5, h6 {\r\n  margin-top: 0;\r\n  margin-bottom: $headings-margin-bottom;\r\n}\r\n\r\n// Reset margins on paragraphs\r\n//\r\n// Similarly, the top margin on `<p>`s get reset. However, we also reset the\r\n// bottom margin to use `rem` units instead of `em`.\r\np {\r\n  margin-top: 0;\r\n  margin-bottom: $paragraph-margin-bottom;\r\n}\r\n\r\n// Abbreviations\r\n//\r\n// 1. Duplicate behavior to the data-* attribute for our tooltip plugin\r\n// 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.\r\n// 3. Add explicit cursor to indicate changed behavior.\r\n// 4. Remove the bottom border in Firefox 39-.\r\n// 5. Prevent the text-decoration to be skipped.\r\n\r\nabbr[title],\r\nabbr[data-original-title] { // 1\r\n  text-decoration: underline; // 2\r\n  text-decoration: underline dotted; // 2\r\n  cursor: help; // 3\r\n  border-bottom: 0; // 4\r\n  text-decoration-skip-ink: none; // 5\r\n}\r\n\r\naddress {\r\n  margin-bottom: 1rem;\r\n  font-style: normal;\r\n  line-height: inherit;\r\n}\r\n\r\nol,\r\nul,\r\ndl {\r\n  margin-top: 0;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\nol ol,\r\nul ul,\r\nol ul,\r\nul ol {\r\n  margin-bottom: 0;\r\n}\r\n\r\ndt {\r\n  font-weight: $dt-font-weight;\r\n}\r\n\r\ndd {\r\n  margin-bottom: .5rem;\r\n  margin-left: 0; // Undo browser default\r\n}\r\n\r\nblockquote {\r\n  margin: 0 0 1rem;\r\n}\r\n\r\nb,\r\nstrong {\r\n  font-weight: $font-weight-bolder; // Add the correct font weight in Chrome, Edge, and Safari\r\n}\r\n\r\nsmall {\r\n  @include font-size(80%); // Add the correct font size in all browsers\r\n}\r\n\r\n//\r\n// Prevent `sub` and `sup` elements from affecting the line height in\r\n// all browsers.\r\n//\r\n\r\nsub,\r\nsup {\r\n  position: relative;\r\n  @include font-size(75%);\r\n  line-height: 0;\r\n  vertical-align: baseline;\r\n}\r\n\r\nsub { bottom: -.25em; }\r\nsup { top: -.5em; }\r\n\r\n\r\n//\r\n// Links\r\n//\r\n\r\na {\r\n  color: $link-color;\r\n  text-decoration: $link-decoration;\r\n  background-color: transparent; // Remove the gray background on active links in IE 10.\r\n\r\n  @include hover {\r\n    color: $link-hover-color;\r\n    text-decoration: $link-hover-decoration;\r\n  }\r\n}\r\n\r\n// And undo these styles for placeholder links/named anchors (without href)\r\n// which have not been made explicitly keyboard-focusable (without tabindex).\r\n// It would be more straightforward to just use a[href] in previous block, but that\r\n// causes specificity issues in many other styles that are too complex to fix.\r\n// See https://github.com/twbs/bootstrap/issues/19402\r\n\r\na:not([href]):not([tabindex]) {\r\n  color: inherit;\r\n  text-decoration: none;\r\n\r\n  @include hover-focus {\r\n    color: inherit;\r\n    text-decoration: none;\r\n  }\r\n\r\n  &:focus {\r\n    outline: 0;\r\n  }\r\n}\r\n\r\n\r\n//\r\n// Code\r\n//\r\n\r\npre,\r\ncode,\r\nkbd,\r\nsamp {\r\n  font-family: $font-family-monospace;\r\n  @include font-size(1em); // Correct the odd `em` font sizing in all browsers.\r\n}\r\n\r\npre {\r\n  // Remove browser default top margin\r\n  margin-top: 0;\r\n  // Reset browser default of `1em` to use `rem`s\r\n  margin-bottom: 1rem;\r\n  // Don't allow content to break outside\r\n  overflow: auto;\r\n}\r\n\r\n\r\n//\r\n// Figures\r\n//\r\n\r\nfigure {\r\n  // Apply a consistent margin strategy (matches our type styles).\r\n  margin: 0 0 1rem;\r\n}\r\n\r\n\r\n//\r\n// Images and content\r\n//\r\n\r\nimg {\r\n  vertical-align: middle;\r\n  border-style: none; // Remove the border on images inside links in IE 10-.\r\n}\r\n\r\nsvg {\r\n  // Workaround for the SVG overflow bug in IE10/11 is still required.\r\n  // See https://github.com/twbs/bootstrap/issues/26878\r\n  overflow: hidden;\r\n  vertical-align: middle;\r\n}\r\n\r\n\r\n//\r\n// Tables\r\n//\r\n\r\ntable {\r\n  border-collapse: collapse; // Prevent double borders\r\n}\r\n\r\ncaption {\r\n  padding-top: $table-cell-padding;\r\n  padding-bottom: $table-cell-padding;\r\n  color: $table-caption-color;\r\n  text-align: left;\r\n  caption-side: bottom;\r\n}\r\n\r\nth {\r\n  // Matches default `<td>` alignment by inheriting from the `<body>`, or the\r\n  // closest parent with a set `text-align`.\r\n  text-align: inherit;\r\n}\r\n\r\n\r\n//\r\n// Forms\r\n//\r\n\r\nlabel {\r\n  // Allow labels to use `margin` for spacing.\r\n  display: inline-block;\r\n  margin-bottom: $label-margin-bottom;\r\n}\r\n\r\n// Remove the default `border-radius` that macOS Chrome adds.\r\n//\r\n// Details at https://github.com/twbs/bootstrap/issues/24093\r\nbutton {\r\n  // stylelint-disable-next-line property-blacklist\r\n  border-radius: 0;\r\n}\r\n\r\n// Work around a Firefox/IE bug where the transparent `button` background\r\n// results in a loss of the default `button` focus styles.\r\n//\r\n// Credit: https://github.com/suitcss/base/\r\nbutton:focus {\r\n  outline: 1px dotted;\r\n  outline: 5px auto -webkit-focus-ring-color;\r\n}\r\n\r\ninput,\r\nbutton,\r\nselect,\r\noptgroup,\r\ntextarea {\r\n  margin: 0; // Remove the margin in Firefox and Safari\r\n  font-family: inherit;\r\n  @include font-size(inherit);\r\n  line-height: inherit;\r\n}\r\n\r\nbutton,\r\ninput {\r\n  overflow: visible; // Show the overflow in Edge\r\n}\r\n\r\nbutton,\r\nselect {\r\n  text-transform: none; // Remove the inheritance of text transform in Firefox\r\n}\r\n\r\n// Remove the inheritance of word-wrap in Safari.\r\n//\r\n// Details at https://github.com/twbs/bootstrap/issues/24990\r\nselect {\r\n  word-wrap: normal;\r\n}\r\n\r\n\r\n// 1. Prevent a WebKit bug where (2) destroys native `audio` and `video`\r\n//    controls in Android 4.\r\n// 2. Correct the inability to style clickable types in iOS and Safari.\r\nbutton,\r\n[type=\"button\"], // 1\r\n[type=\"reset\"],\r\n[type=\"submit\"] {\r\n  -webkit-appearance: button; // 2\r\n}\r\n\r\n// Opinionated: add \"hand\" cursor to non-disabled button elements.\r\n@if $enable-pointer-cursor-for-buttons {\r\n  button,\r\n  [type=\"button\"],\r\n  [type=\"reset\"],\r\n  [type=\"submit\"] {\r\n    &:not(:disabled) {\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n// Remove inner border and padding from Firefox, but don't restore the outline like Normalize.\r\nbutton::-moz-focus-inner,\r\n[type=\"button\"]::-moz-focus-inner,\r\n[type=\"reset\"]::-moz-focus-inner,\r\n[type=\"submit\"]::-moz-focus-inner {\r\n  padding: 0;\r\n  border-style: none;\r\n}\r\n\r\ninput[type=\"radio\"],\r\ninput[type=\"checkbox\"] {\r\n  box-sizing: border-box; // 1. Add the correct box sizing in IE 10-\r\n  padding: 0; // 2. Remove the padding in IE 10-\r\n}\r\n\r\n\r\ninput[type=\"date\"],\r\ninput[type=\"time\"],\r\ninput[type=\"datetime-local\"],\r\ninput[type=\"month\"] {\r\n  // Remove the default appearance of temporal inputs to avoid a Mobile Safari\r\n  // bug where setting a custom line-height prevents text from being vertically\r\n  // centered within the input.\r\n  // See https://bugs.webkit.org/show_bug.cgi?id=139848\r\n  // and https://github.com/twbs/bootstrap/issues/11266\r\n  -webkit-appearance: listbox;\r\n}\r\n\r\ntextarea {\r\n  overflow: auto; // Remove the default vertical scrollbar in IE.\r\n  // Textareas should really only resize vertically so they don't break their (horizontal) containers.\r\n  resize: vertical;\r\n}\r\n\r\nfieldset {\r\n  // Browsers set a default `min-width: min-content;` on fieldsets,\r\n  // unlike e.g. `<div>`s, which have `min-width: 0;` by default.\r\n  // So we reset that to ensure fieldsets behave more like a standard block element.\r\n  // See https://github.com/twbs/bootstrap/issues/12359\r\n  // and https://html.spec.whatwg.org/multipage/#the-fieldset-and-legend-elements\r\n  min-width: 0;\r\n  // Reset the default outline behavior of fieldsets so they don't affect page layout.\r\n  padding: 0;\r\n  margin: 0;\r\n  border: 0;\r\n}\r\n\r\n// 1. Correct the text wrapping in Edge and IE.\r\n// 2. Correct the color inheritance from `fieldset` elements in IE.\r\nlegend {\r\n  display: block;\r\n  width: 100%;\r\n  max-width: 100%; // 1\r\n  padding: 0;\r\n  margin-bottom: .5rem;\r\n  @include font-size(1.5rem);\r\n  line-height: inherit;\r\n  color: inherit; // 2\r\n  white-space: normal; // 1\r\n}\r\n\r\nprogress {\r\n  vertical-align: baseline; // Add the correct vertical alignment in Chrome, Firefox, and Opera.\r\n}\r\n\r\n// Correct the cursor style of increment and decrement buttons in Chrome.\r\n[type=\"number\"]::-webkit-inner-spin-button,\r\n[type=\"number\"]::-webkit-outer-spin-button {\r\n  height: auto;\r\n}\r\n\r\n[type=\"search\"] {\r\n  // This overrides the extra rounded corners on search inputs in iOS so that our\r\n  // `.form-control` class can properly style them. Note that this cannot simply\r\n  // be added to `.form-control` as it's not specific enough. For details, see\r\n  // https://github.com/twbs/bootstrap/issues/11586.\r\n  outline-offset: -2px; // 2. Correct the outline style in Safari.\r\n  -webkit-appearance: none;\r\n}\r\n\r\n//\r\n// Remove the inner padding in Chrome and Safari on macOS.\r\n//\r\n\r\n[type=\"search\"]::-webkit-search-decoration {\r\n  -webkit-appearance: none;\r\n}\r\n\r\n//\r\n// 1. Correct the inability to style clickable types in iOS and Safari.\r\n// 2. Change font properties to `inherit` in Safari.\r\n//\r\n\r\n::-webkit-file-upload-button {\r\n  font: inherit; // 2\r\n  -webkit-appearance: button; // 1\r\n}\r\n\r\n//\r\n// Correct element displays\r\n//\r\n\r\noutput {\r\n  display: inline-block;\r\n}\r\n\r\nsummary {\r\n  display: list-item; // Add the correct display in all browsers\r\n  cursor: pointer;\r\n}\r\n\r\ntemplate {\r\n  display: none; // Add the correct display in IE\r\n}\r\n\r\n// Always hide an element with the `hidden` HTML attribute (from PureCSS).\r\n// Needed for proper display in IE 10-.\r\n[hidden] {\r\n  display: none !important;\r\n}\r\n", "// Variables\r\n//\r\n// Variables should follow the `$component-state-property-size` formula for\r\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\r\n\r\n// Color system\r\n\r\n$white:    #fff !default;\r\n$gray-100: #f8f9fa !default;\r\n$gray-200: #e9ecef !default;\r\n$gray-300: #dee2e6 !default;\r\n$gray-400: #ced4da !default;\r\n$gray-500: #adb5bd !default;\r\n$gray-600: #6c757d !default;\r\n$gray-700: #495057 !default;\r\n$gray-800: #343a40 !default;\r\n$gray-900: #212529 !default;\r\n$black:    #000 !default;\r\n\r\n$grays: () !default;\r\n// stylelint-disable-next-line scss/dollar-variable-default\r\n$grays: map-merge(\r\n  (\r\n    \"100\": $gray-100,\r\n    \"200\": $gray-200,\r\n    \"300\": $gray-300,\r\n    \"400\": $gray-400,\r\n    \"500\": $gray-500,\r\n    \"600\": $gray-600,\r\n    \"700\": $gray-700,\r\n    \"800\": $gray-800,\r\n    \"900\": $gray-900\r\n  ),\r\n  $grays\r\n);\r\n\r\n$blue:    #007bff !default;\r\n$indigo:  #6610f2 !default;\r\n$purple:  #6f42c1 !default;\r\n$pink:    #e83e8c !default;\r\n$red:     #dc3545 !default;\r\n$orange:  #fd7e14 !default;\r\n$yellow:  #ffc107 !default;\r\n$green:   #28a745 !default;\r\n$teal:    #20c997 !default;\r\n$cyan:    #17a2b8 !default;\r\n\r\n$colors: () !default;\r\n// stylelint-disable-next-line scss/dollar-variable-default\r\n$colors: map-merge(\r\n  (\r\n    \"blue\":       $blue,\r\n    \"indigo\":     $indigo,\r\n    \"purple\":     $purple,\r\n    \"pink\":       $pink,\r\n    \"red\":        $red,\r\n    \"orange\":     $orange,\r\n    \"yellow\":     $yellow,\r\n    \"green\":      $green,\r\n    \"teal\":       $teal,\r\n    \"cyan\":       $cyan,\r\n    \"white\":      $white,\r\n    \"gray\":       $gray-600,\r\n    \"gray-dark\":  $gray-800\r\n  ),\r\n  $colors\r\n);\r\n\r\n$primary:       $blue !default;\r\n$secondary:     $gray-600 !default;\r\n$success:       $green !default;\r\n$info:          $cyan !default;\r\n$warning:       $yellow !default;\r\n$danger:        $red !default;\r\n$light:         $gray-100 !default;\r\n$dark:          $gray-800 !default;\r\n\r\n$theme-colors: () !default;\r\n// stylelint-disable-next-line scss/dollar-variable-default\r\n$theme-colors: map-merge(\r\n  (\r\n    \"primary\":    $primary,\r\n    \"secondary\":  $secondary,\r\n    \"success\":    $success,\r\n    \"info\":       $info,\r\n    \"warning\":    $warning,\r\n    \"danger\":     $danger,\r\n    \"light\":      $light,\r\n    \"dark\":       $dark\r\n  ),\r\n  $theme-colors\r\n);\r\n\r\n// Set a specific jump point for requesting color jumps\r\n$theme-color-interval:      8% !default;\r\n\r\n// The yiq lightness value that determines when the lightness of color changes from \"dark\" to \"light\". Acceptable values are between 0 and 255.\r\n$yiq-contrasted-threshold:  150 !default;\r\n\r\n// Customize the light and dark text colors for use in our YIQ color contrast function.\r\n$yiq-text-dark:             $gray-900 !default;\r\n$yiq-text-light:            $white !default;\r\n\r\n\r\n// Options\r\n//\r\n// Quickly modify global styling by enabling or disabling optional features.\r\n\r\n$enable-caret:                                true !default;\r\n$enable-rounded:                              true !default;\r\n$enable-shadows:                              false !default;\r\n$enable-gradients:                            false !default;\r\n$enable-transitions:                          true !default;\r\n$enable-prefers-reduced-motion-media-query:   true !default;\r\n$enable-hover-media-query:                    false !default; // Deprecated, no longer affects any compiled CSS\r\n$enable-grid-classes:                         true !default;\r\n$enable-pointer-cursor-for-buttons:           true !default;\r\n$enable-print-styles:                         true !default;\r\n$enable-responsive-font-sizes:                false !default;\r\n$enable-validation-icons:                     true !default;\r\n$enable-deprecation-messages:                 true !default;\r\n\r\n\r\n// Spacing\r\n//\r\n// Control the default styling of most Bootstrap elements by modifying these\r\n// variables. Mostly focused on spacing.\r\n// You can add more entries to the $spacers map, should you need more variation.\r\n\r\n$spacer: 1rem !default;\r\n$spacers: () !default;\r\n// stylelint-disable-next-line scss/dollar-variable-default\r\n$spacers: map-merge(\r\n  (\r\n    0: 0,\r\n    1: ($spacer * .25),\r\n    2: ($spacer * .5),\r\n    3: $spacer,\r\n    4: ($spacer * 1.5),\r\n    5: ($spacer * 3)\r\n  ),\r\n  $spacers\r\n);\r\n\r\n// This variable affects the `.h-*` and `.w-*` classes.\r\n$sizes: () !default;\r\n// stylelint-disable-next-line scss/dollar-variable-default\r\n$sizes: map-merge(\r\n  (\r\n    25: 25%,\r\n    50: 50%,\r\n    75: 75%,\r\n    100: 100%,\r\n    auto: auto\r\n  ),\r\n  $sizes\r\n);\r\n\r\n\r\n// Body\r\n//\r\n// Settings for the `<body>` element.\r\n\r\n$body-bg:                   $white !default;\r\n$body-color:                $gray-900 !default;\r\n\r\n\r\n// Links\r\n//\r\n// Style anchor elements.\r\n\r\n$link-color:                              theme-color(\"primary\") !default;\r\n$link-decoration:                         none !default;\r\n$link-hover-color:                        darken($link-color, 15%) !default;\r\n$link-hover-decoration:                   underline !default;\r\n// Darken percentage for links with `.text-*` class (e.g. `.text-success`)\r\n$emphasized-link-hover-darken-percentage: 15% !default;\r\n\r\n// Paragraphs\r\n//\r\n// Style p element.\r\n\r\n$paragraph-margin-bottom:   1rem !default;\r\n\r\n\r\n// Grid breakpoints\r\n//\r\n// Define the minimum dimensions at which your layout will change,\r\n// adapting to different screen sizes, for use in media queries.\r\n\r\n$grid-breakpoints: (\r\n  xs: 0,\r\n  sm: 576px,\r\n  md: 768px,\r\n  lg: 992px,\r\n  xl: 1200px\r\n) !default;\r\n\r\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\r\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\r\n\r\n\r\n// Grid containers\r\n//\r\n// Define the maximum width of `.container` for different screen sizes.\r\n\r\n$container-max-widths: (\r\n  sm: 540px,\r\n  md: 720px,\r\n  lg: 960px,\r\n  xl: 1140px\r\n) !default;\r\n\r\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\r\n\r\n\r\n// Grid columns\r\n//\r\n// Set the number of columns and specify the width of the gutters.\r\n\r\n$grid-columns:                12 !default;\r\n$grid-gutter-width:           30px !default;\r\n\r\n\r\n// Components\r\n//\r\n// Define common padding and border radius sizes and more.\r\n\r\n$line-height-lg:              1.5 !default;\r\n$line-height-sm:              1.5 !default;\r\n\r\n$border-width:                1px !default;\r\n$border-color:                $gray-300 !default;\r\n\r\n$border-radius:               .25rem !default;\r\n$border-radius-lg:            .3rem !default;\r\n$border-radius-sm:            .2rem !default;\r\n\r\n$rounded-pill:                50rem !default;\r\n\r\n$box-shadow-sm:               0 .125rem .25rem rgba($black, .075) !default;\r\n$box-shadow:                  0 .5rem 1rem rgba($black, .15) !default;\r\n$box-shadow-lg:               0 1rem 3rem rgba($black, .175) !default;\r\n\r\n$component-active-color:      $white !default;\r\n$component-active-bg:         theme-color(\"primary\") !default;\r\n\r\n$caret-width:                 .3em !default;\r\n$caret-vertical-align:        $caret-width * .85 !default;\r\n$caret-spacing:               $caret-width * .85 !default;\r\n\r\n$transition-base:             all .2s ease-in-out !default;\r\n$transition-fade:             opacity .15s linear !default;\r\n$transition-collapse:         height .35s ease !default;\r\n\r\n$embed-responsive-aspect-ratios: () !default;\r\n// stylelint-disable-next-line scss/dollar-variable-default\r\n$embed-responsive-aspect-ratios: join(\r\n  (\r\n    (21 9),\r\n    (16 9),\r\n    (4 3),\r\n    (1 1),\r\n  ),\r\n  $embed-responsive-aspect-ratios\r\n);\r\n\r\n// Typography\r\n//\r\n// Font, line-height, and color for body text, headings, and more.\r\n\r\n// stylelint-disable value-keyword-case\r\n$font-family-sans-serif:      -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\r\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\r\n$font-family-base:            $font-family-sans-serif !default;\r\n// stylelint-enable value-keyword-case\r\n\r\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\r\n$font-size-lg:                $font-size-base * 1.25 !default;\r\n$font-size-sm:                $font-size-base * .875 !default;\r\n\r\n$font-weight-lighter:         lighter !default;\r\n$font-weight-light:           300 !default;\r\n$font-weight-normal:          400 !default;\r\n$font-weight-bold:            700 !default;\r\n$font-weight-bolder:          bolder !default;\r\n\r\n$font-weight-base:            $font-weight-normal !default;\r\n$line-height-base:            1.5 !default;\r\n\r\n$h1-font-size:                $font-size-base * 2.5 !default;\r\n$h2-font-size:                $font-size-base * 2 !default;\r\n$h3-font-size:                $font-size-base * 1.75 !default;\r\n$h4-font-size:                $font-size-base * 1.5 !default;\r\n$h5-font-size:                $font-size-base * 1.25 !default;\r\n$h6-font-size:                $font-size-base !default;\r\n\r\n$headings-margin-bottom:      $spacer / 2 !default;\r\n$headings-font-family:        null !default;\r\n$headings-font-weight:        500 !default;\r\n$headings-line-height:        1.2 !default;\r\n$headings-color:              null !default;\r\n\r\n$display1-size:               6rem !default;\r\n$display2-size:               5.5rem !default;\r\n$display3-size:               4.5rem !default;\r\n$display4-size:               3.5rem !default;\r\n\r\n$display1-weight:             300 !default;\r\n$display2-weight:             300 !default;\r\n$display3-weight:             300 !default;\r\n$display4-weight:             300 !default;\r\n$display-line-height:         $headings-line-height !default;\r\n\r\n$lead-font-size:              $font-size-base * 1.25 !default;\r\n$lead-font-weight:            300 !default;\r\n\r\n$small-font-size:             80% !default;\r\n\r\n$text-muted:                  $gray-600 !default;\r\n\r\n$blockquote-small-color:      $gray-600 !default;\r\n$blockquote-small-font-size:  $small-font-size !default;\r\n$blockquote-font-size:        $font-size-base * 1.25 !default;\r\n\r\n$hr-border-color:             rgba($black, .1) !default;\r\n$hr-border-width:             $border-width !default;\r\n\r\n$mark-padding:                .2em !default;\r\n\r\n$dt-font-weight:              $font-weight-bold !default;\r\n\r\n$kbd-box-shadow:              inset 0 -.1rem 0 rgba($black, .25) !default;\r\n$nested-kbd-font-weight:      $font-weight-bold !default;\r\n\r\n$list-inline-padding:         .5rem !default;\r\n\r\n$mark-bg:                     #fcf8e3 !default;\r\n\r\n$hr-margin-y:                 $spacer !default;\r\n\r\n\r\n// Tables\r\n//\r\n// Customizes the `.table` component with basic values, each used across all table variations.\r\n\r\n$table-cell-padding:          .75rem !default;\r\n$table-cell-padding-sm:       .3rem !default;\r\n\r\n$table-color:                 $body-color !default;\r\n$table-bg:                    null !default;\r\n$table-accent-bg:             rgba($black, .05) !default;\r\n$table-hover-color:           $table-color !default;\r\n$table-hover-bg:              rgba($black, .075) !default;\r\n$table-active-bg:             $table-hover-bg !default;\r\n\r\n$table-border-width:          $border-width !default;\r\n$table-border-color:          $border-color !default;\r\n\r\n$table-head-bg:               $gray-200 !default;\r\n$table-head-color:            $gray-700 !default;\r\n\r\n$table-dark-color:            $white !default;\r\n$table-dark-bg:               $gray-800 !default;\r\n$table-dark-accent-bg:        rgba($white, .05) !default;\r\n$table-dark-hover-color:      $table-dark-color !default;\r\n$table-dark-hover-bg:         rgba($white, .075) !default;\r\n$table-dark-border-color:     lighten($table-dark-bg, 7.5%) !default;\r\n$table-dark-color:            $white !default;\r\n\r\n$table-striped-order:         odd !default;\r\n\r\n$table-caption-color:         $text-muted !default;\r\n\r\n$table-bg-level:              -9 !default;\r\n$table-border-level:          -6 !default;\r\n\r\n\r\n// Buttons + Forms\r\n//\r\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\r\n\r\n$input-btn-padding-y:         .375rem !default;\r\n$input-btn-padding-x:         .75rem !default;\r\n$input-btn-font-family:       null !default;\r\n$input-btn-font-size:         $font-size-base !default;\r\n$input-btn-line-height:       $line-height-base !default;\r\n\r\n$input-btn-focus-width:       .2rem !default;\r\n$input-btn-focus-color:       rgba($component-active-bg, .25) !default;\r\n$input-btn-focus-box-shadow:  0 0 0 $input-btn-focus-width $input-btn-focus-color !default;\r\n\r\n$input-btn-padding-y-sm:      .25rem !default;\r\n$input-btn-padding-x-sm:      .5rem !default;\r\n$input-btn-font-size-sm:      $font-size-sm !default;\r\n$input-btn-line-height-sm:    $line-height-sm !default;\r\n\r\n$input-btn-padding-y-lg:      .5rem !default;\r\n$input-btn-padding-x-lg:      1rem !default;\r\n$input-btn-font-size-lg:      $font-size-lg !default;\r\n$input-btn-line-height-lg:    $line-height-lg !default;\r\n\r\n$input-btn-border-width:      $border-width !default;\r\n\r\n\r\n// Buttons\r\n//\r\n// For each of Bootstrap's buttons, define text, background, and border color.\r\n\r\n$btn-padding-y:               $input-btn-padding-y !default;\r\n$btn-padding-x:               $input-btn-padding-x !default;\r\n$btn-font-family:             $input-btn-font-family !default;\r\n$btn-font-size:               $input-btn-font-size !default;\r\n$btn-line-height:             $input-btn-line-height !default;\r\n\r\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\r\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\r\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\r\n$btn-line-height-sm:          $input-btn-line-height-sm !default;\r\n\r\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\r\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\r\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\r\n$btn-line-height-lg:          $input-btn-line-height-lg !default;\r\n\r\n$btn-border-width:            $input-btn-border-width !default;\r\n\r\n$btn-font-weight:             $font-weight-normal !default;\r\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075) !default;\r\n$btn-focus-width:             $input-btn-focus-width !default;\r\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\r\n$btn-disabled-opacity:        .65 !default;\r\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125) !default;\r\n\r\n$btn-link-disabled-color:     $gray-600 !default;\r\n\r\n$btn-block-spacing-y:         .5rem !default;\r\n\r\n// Allows for customizing button radius independently from global border radius\r\n$btn-border-radius:           $border-radius !default;\r\n$btn-border-radius-lg:        $border-radius-lg !default;\r\n$btn-border-radius-sm:        $border-radius-sm !default;\r\n\r\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n\r\n\r\n// Forms\r\n\r\n$label-margin-bottom:                   .5rem !default;\r\n\r\n$input-padding-y:                       $input-btn-padding-y !default;\r\n$input-padding-x:                       $input-btn-padding-x !default;\r\n$input-font-family:                     $input-btn-font-family !default;\r\n$input-font-size:                       $input-btn-font-size !default;\r\n$input-font-weight:                     $font-weight-base !default;\r\n$input-line-height:                     $input-btn-line-height !default;\r\n\r\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\r\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\r\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\r\n$input-line-height-sm:                  $input-btn-line-height-sm !default;\r\n\r\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\r\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\r\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\r\n$input-line-height-lg:                  $input-btn-line-height-lg !default;\r\n\r\n$input-bg:                              $white !default;\r\n$input-disabled-bg:                     $gray-200 !default;\r\n\r\n$input-color:                           $gray-700 !default;\r\n$input-border-color:                    $gray-400 !default;\r\n$input-border-width:                    $input-btn-border-width !default;\r\n$input-box-shadow:                      inset 0 1px 1px rgba($black, .075) !default;\r\n\r\n$input-border-radius:                   $border-radius !default;\r\n$input-border-radius-lg:                $border-radius-lg !default;\r\n$input-border-radius-sm:                $border-radius-sm !default;\r\n\r\n$input-focus-bg:                        $input-bg !default;\r\n$input-focus-border-color:              lighten($component-active-bg, 25%) !default;\r\n$input-focus-color:                     $input-color !default;\r\n$input-focus-width:                     $input-btn-focus-width !default;\r\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\r\n\r\n$input-placeholder-color:               $gray-600 !default;\r\n$input-plaintext-color:                 $body-color !default;\r\n\r\n$input-height-border:                   $input-border-width * 2 !default;\r\n\r\n$input-height-inner:                    calc(#{$input-line-height * 1em} + #{$input-padding-y * 2}) !default;\r\n$input-height-inner-half:               calc(#{$input-line-height * .5em} + #{$input-padding-y}) !default;\r\n$input-height-inner-quarter:            calc(#{$input-line-height * .25em} + #{$input-padding-y / 2}) !default;\r\n\r\n$input-height:                          calc(#{$input-line-height * 1em} + #{$input-padding-y * 2} + #{$input-height-border}) !default;\r\n$input-height-sm:                       calc(#{$input-line-height-sm * 1em} + #{$input-btn-padding-y-sm * 2} + #{$input-height-border}) !default;\r\n$input-height-lg:                       calc(#{$input-line-height-lg * 1em} + #{$input-btn-padding-y-lg * 2} + #{$input-height-border}) !default;\r\n\r\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n\r\n$form-text-margin-top:                  .25rem !default;\r\n\r\n$form-check-input-gutter:               1.25rem !default;\r\n$form-check-input-margin-y:             .3rem !default;\r\n$form-check-input-margin-x:             .25rem !default;\r\n\r\n$form-check-inline-margin-x:            .75rem !default;\r\n$form-check-inline-input-margin-x:      .3125rem !default;\r\n\r\n$form-grid-gutter-width:                10px !default;\r\n$form-group-margin-bottom:              1rem !default;\r\n\r\n$input-group-addon-color:               $input-color !default;\r\n$input-group-addon-bg:                  $gray-200 !default;\r\n$input-group-addon-border-color:        $input-border-color !default;\r\n\r\n$custom-forms-transition:               background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n\r\n$custom-control-gutter:                 .5rem !default;\r\n$custom-control-spacer-x:               1rem !default;\r\n\r\n$custom-control-indicator-size:         1rem !default;\r\n$custom-control-indicator-bg:           $input-bg !default;\r\n\r\n$custom-control-indicator-bg-size:      50% 50% !default;\r\n$custom-control-indicator-box-shadow:   $input-box-shadow !default;\r\n$custom-control-indicator-border-color: $gray-500 !default;\r\n$custom-control-indicator-border-width: $input-border-width !default;\r\n\r\n$custom-control-indicator-disabled-bg:          $input-disabled-bg !default;\r\n$custom-control-label-disabled-color:           $gray-600 !default;\r\n\r\n$custom-control-indicator-checked-color:        $component-active-color !default;\r\n$custom-control-indicator-checked-bg:           $component-active-bg !default;\r\n$custom-control-indicator-checked-disabled-bg:  rgba(theme-color(\"primary\"), .5) !default;\r\n$custom-control-indicator-checked-box-shadow:   none !default;\r\n$custom-control-indicator-checked-border-color: $custom-control-indicator-checked-bg !default;\r\n\r\n$custom-control-indicator-focus-box-shadow:     $input-focus-box-shadow !default;\r\n$custom-control-indicator-focus-border-color:   $input-focus-border-color !default;\r\n\r\n$custom-control-indicator-active-color:         $component-active-color !default;\r\n$custom-control-indicator-active-bg:            lighten($component-active-bg, 35%) !default;\r\n$custom-control-indicator-active-box-shadow:    none !default;\r\n$custom-control-indicator-active-border-color:  $custom-control-indicator-active-bg !default;\r\n\r\n$custom-checkbox-indicator-border-radius:       $border-radius !default;\r\n$custom-checkbox-indicator-icon-checked:        str-replace(url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3e%3c/svg%3e\"), \"#\", \"%23\") !default;\r\n\r\n$custom-checkbox-indicator-indeterminate-bg:           $component-active-bg !default;\r\n$custom-checkbox-indicator-indeterminate-color:        $custom-control-indicator-checked-color !default;\r\n$custom-checkbox-indicator-icon-indeterminate:         str-replace(url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3e%3cpath stroke='#{$custom-checkbox-indicator-indeterminate-color}' d='M0 2h4'/%3e%3c/svg%3e\"), \"#\", \"%23\") !default;\r\n$custom-checkbox-indicator-indeterminate-box-shadow:   none !default;\r\n$custom-checkbox-indicator-indeterminate-border-color: $custom-checkbox-indicator-indeterminate-bg !default;\r\n\r\n$custom-radio-indicator-border-radius:          50% !default;\r\n$custom-radio-indicator-icon-checked:           str-replace(url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='#{$custom-control-indicator-checked-color}'/%3e%3c/svg%3e\"), \"#\", \"%23\") !default;\r\n\r\n$custom-switch-width:                           $custom-control-indicator-size * 1.75 !default;\r\n$custom-switch-indicator-border-radius:         $custom-control-indicator-size / 2 !default;\r\n$custom-switch-indicator-size:                  calc(#{$custom-control-indicator-size} - #{$custom-control-indicator-border-width * 4}) !default;\r\n\r\n$custom-select-padding-y:           $input-padding-y !default;\r\n$custom-select-padding-x:           $input-padding-x !default;\r\n$custom-select-font-family:         $input-font-family !default;\r\n$custom-select-font-size:           $input-font-size !default;\r\n$custom-select-height:              $input-height !default;\r\n$custom-select-indicator-padding:   1rem !default; // Extra padding to account for the presence of the background-image based indicator\r\n$custom-select-font-weight:         $input-font-weight !default;\r\n$custom-select-line-height:         $input-line-height !default;\r\n$custom-select-color:               $input-color !default;\r\n$custom-select-disabled-color:      $gray-600 !default;\r\n$custom-select-bg:                  $input-bg !default;\r\n$custom-select-disabled-bg:         $gray-200 !default;\r\n$custom-select-bg-size:             8px 10px !default; // In pixels because image dimensions\r\n$custom-select-indicator-color:     $gray-800 !default;\r\n$custom-select-indicator:           str-replace(url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='#{$custom-select-indicator-color}' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e\"), \"#\", \"%23\") !default;\r\n$custom-select-background:          $custom-select-indicator no-repeat right $custom-select-padding-x center / $custom-select-bg-size !default; // Used so we can have multiple background elements (e.g., arrow and feedback icon)\r\n\r\n$custom-select-feedback-icon-padding-right: calc((1em + #{2 * $custom-select-padding-y}) * 3 / 4 + #{$custom-select-padding-x + $custom-select-indicator-padding}) !default;\r\n$custom-select-feedback-icon-position:      center right ($custom-select-padding-x + $custom-select-indicator-padding) !default;\r\n$custom-select-feedback-icon-size:          $input-height-inner-half $input-height-inner-half !default;\r\n\r\n$custom-select-border-width:        $input-border-width !default;\r\n$custom-select-border-color:        $input-border-color !default;\r\n$custom-select-border-radius:       $border-radius !default;\r\n$custom-select-box-shadow:          inset 0 1px 2px rgba($black, .075) !default;\r\n\r\n$custom-select-focus-border-color:  $input-focus-border-color !default;\r\n$custom-select-focus-width:         $input-focus-width !default;\r\n$custom-select-focus-box-shadow:    0 0 0 $custom-select-focus-width $input-btn-focus-color !default;\r\n\r\n$custom-select-padding-y-sm:        $input-padding-y-sm !default;\r\n$custom-select-padding-x-sm:        $input-padding-x-sm !default;\r\n$custom-select-font-size-sm:        $input-font-size-sm !default;\r\n$custom-select-height-sm:           $input-height-sm !default;\r\n\r\n$custom-select-padding-y-lg:        $input-padding-y-lg !default;\r\n$custom-select-padding-x-lg:        $input-padding-x-lg !default;\r\n$custom-select-font-size-lg:        $input-font-size-lg !default;\r\n$custom-select-height-lg:           $input-height-lg !default;\r\n\r\n$custom-range-track-width:          100% !default;\r\n$custom-range-track-height:         .5rem !default;\r\n$custom-range-track-cursor:         pointer !default;\r\n$custom-range-track-bg:             $gray-300 !default;\r\n$custom-range-track-border-radius:  1rem !default;\r\n$custom-range-track-box-shadow:     inset 0 .25rem .25rem rgba($black, .1) !default;\r\n\r\n$custom-range-thumb-width:                   1rem !default;\r\n$custom-range-thumb-height:                  $custom-range-thumb-width !default;\r\n$custom-range-thumb-bg:                      $component-active-bg !default;\r\n$custom-range-thumb-border:                  0 !default;\r\n$custom-range-thumb-border-radius:           1rem !default;\r\n$custom-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\r\n$custom-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\r\n$custom-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in IE/Edge\r\n$custom-range-thumb-active-bg:               lighten($component-active-bg, 35%) !default;\r\n$custom-range-thumb-disabled-bg:             $gray-500 !default;\r\n\r\n$custom-file-height:                $input-height !default;\r\n$custom-file-height-inner:          $input-height-inner !default;\r\n$custom-file-focus-border-color:    $input-focus-border-color !default;\r\n$custom-file-focus-box-shadow:      $input-focus-box-shadow !default;\r\n$custom-file-disabled-bg:           $input-disabled-bg !default;\r\n\r\n$custom-file-padding-y:             $input-padding-y !default;\r\n$custom-file-padding-x:             $input-padding-x !default;\r\n$custom-file-line-height:           $input-line-height !default;\r\n$custom-file-font-family:           $input-font-family !default;\r\n$custom-file-font-weight:           $input-font-weight !default;\r\n$custom-file-color:                 $input-color !default;\r\n$custom-file-bg:                    $input-bg !default;\r\n$custom-file-border-width:          $input-border-width !default;\r\n$custom-file-border-color:          $input-border-color !default;\r\n$custom-file-border-radius:         $input-border-radius !default;\r\n$custom-file-box-shadow:            $input-box-shadow !default;\r\n$custom-file-button-color:          $custom-file-color !default;\r\n$custom-file-button-bg:             $input-group-addon-bg !default;\r\n$custom-file-text: (\r\n  en: \"Browse\"\r\n) !default;\r\n\r\n\r\n// Form validation\r\n\r\n$form-feedback-margin-top:          $form-text-margin-top !default;\r\n$form-feedback-font-size:           $small-font-size !default;\r\n$form-feedback-valid-color:         theme-color(\"success\") !default;\r\n$form-feedback-invalid-color:       theme-color(\"danger\") !default;\r\n\r\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\r\n$form-feedback-icon-valid:          str-replace(url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e\"), \"#\", \"%23\") !default;\r\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\r\n$form-feedback-icon-invalid:        str-replace(url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='#{$form-feedback-icon-invalid-color}' viewBox='-2 -2 7 7'%3e%3cpath stroke='#{$form-feedback-icon-invalid-color}' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E\"), \"#\", \"%23\") !default;\r\n\r\n$form-validation-states: () !default;\r\n// stylelint-disable-next-line scss/dollar-variable-default\r\n$form-validation-states: map-merge(\r\n  (\r\n    \"valid\": (\r\n      \"color\": $form-feedback-valid-color,\r\n      \"icon\": $form-feedback-icon-valid\r\n    ),\r\n    \"invalid\": (\r\n      \"color\": $form-feedback-invalid-color,\r\n      \"icon\": $form-feedback-icon-invalid\r\n    ),\r\n  ),\r\n  $form-validation-states\r\n);\r\n\r\n// Z-index master list\r\n//\r\n// Warning: Avoid customizing these values. They're used for a bird's eye view\r\n// of components dependent on the z-axis and are designed to all work together.\r\n\r\n$zindex-dropdown:                   1000 !default;\r\n$zindex-sticky:                     1020 !default;\r\n$zindex-fixed:                      1030 !default;\r\n$zindex-modal-backdrop:             1040 !default;\r\n$zindex-modal:                      1050 !default;\r\n$zindex-popover:                    1060 !default;\r\n$zindex-tooltip:                    1070 !default;\r\n\r\n\r\n// Navs\r\n\r\n$nav-link-padding-y:                .5rem !default;\r\n$nav-link-padding-x:                1rem !default;\r\n$nav-link-disabled-color:           $gray-600 !default;\r\n\r\n$nav-tabs-border-color:             $gray-300 !default;\r\n$nav-tabs-border-width:             $border-width !default;\r\n$nav-tabs-border-radius:            $border-radius !default;\r\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\r\n$nav-tabs-link-active-color:        $gray-700 !default;\r\n$nav-tabs-link-active-bg:           $body-bg !default;\r\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\r\n\r\n$nav-pills-border-radius:           $border-radius !default;\r\n$nav-pills-link-active-color:       $component-active-color !default;\r\n$nav-pills-link-active-bg:          $component-active-bg !default;\r\n\r\n$nav-divider-color:                 $gray-200 !default;\r\n$nav-divider-margin-y:              $spacer / 2 !default;\r\n\r\n\r\n// Navbar\r\n\r\n$navbar-padding-y:                  $spacer / 2 !default;\r\n$navbar-padding-x:                  $spacer !default;\r\n\r\n$navbar-nav-link-padding-x:         .5rem !default;\r\n\r\n$navbar-brand-font-size:            $font-size-lg !default;\r\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\r\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\r\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\r\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) / 2 !default;\r\n\r\n$navbar-toggler-padding-y:          .25rem !default;\r\n$navbar-toggler-padding-x:          .75rem !default;\r\n$navbar-toggler-font-size:          $font-size-lg !default;\r\n$navbar-toggler-border-radius:      $btn-border-radius !default;\r\n\r\n$navbar-dark-color:                 rgba($white, .5) !default;\r\n$navbar-dark-hover-color:           rgba($white, .75) !default;\r\n$navbar-dark-active-color:          $white !default;\r\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\r\n$navbar-dark-toggler-icon-bg:       str-replace(url(\"data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='#{$navbar-dark-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e\"), \"#\", \"%23\") !default;\r\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\r\n\r\n$navbar-light-color:                rgba($black, .5) !default;\r\n$navbar-light-hover-color:          rgba($black, .7) !default;\r\n$navbar-light-active-color:         rgba($black, .9) !default;\r\n$navbar-light-disabled-color:       rgba($black, .3) !default;\r\n$navbar-light-toggler-icon-bg:      str-replace(url(\"data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='#{$navbar-light-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e\"), \"#\", \"%23\") !default;\r\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\r\n\r\n$navbar-light-brand-color:                $navbar-light-active-color !default;\r\n$navbar-light-brand-hover-color:          $navbar-light-active-color !default;\r\n$navbar-dark-brand-color:                 $navbar-dark-active-color !default;\r\n$navbar-dark-brand-hover-color:           $navbar-dark-active-color !default;\r\n\r\n\r\n// Dropdowns\r\n//\r\n// Dropdown menu container and contents.\r\n\r\n$dropdown-min-width:                10rem !default;\r\n$dropdown-padding-y:                .5rem !default;\r\n$dropdown-spacer:                   .125rem !default;\r\n$dropdown-font-size:                $font-size-base !default;\r\n$dropdown-color:                    $body-color !default;\r\n$dropdown-bg:                       $white !default;\r\n$dropdown-border-color:             rgba($black, .15) !default;\r\n$dropdown-border-radius:            $border-radius !default;\r\n$dropdown-border-width:             $border-width !default;\r\n$dropdown-inner-border-radius:      calc(#{$dropdown-border-radius} - #{$dropdown-border-width}) !default;\r\n$dropdown-divider-bg:               $gray-200 !default;\r\n$dropdown-divider-margin-y:         $nav-divider-margin-y !default;\r\n$dropdown-box-shadow:               0 .5rem 1rem rgba($black, .175) !default;\r\n\r\n$dropdown-link-color:               $gray-900 !default;\r\n$dropdown-link-hover-color:         darken($gray-900, 5%) !default;\r\n$dropdown-link-hover-bg:            $gray-100 !default;\r\n\r\n$dropdown-link-active-color:        $component-active-color !default;\r\n$dropdown-link-active-bg:           $component-active-bg !default;\r\n\r\n$dropdown-link-disabled-color:      $gray-600 !default;\r\n\r\n$dropdown-item-padding-y:           .25rem !default;\r\n$dropdown-item-padding-x:           1.5rem !default;\r\n\r\n$dropdown-header-color:             $gray-600 !default;\r\n\r\n\r\n// Pagination\r\n\r\n$pagination-padding-y:              .5rem !default;\r\n$pagination-padding-x:              .75rem !default;\r\n$pagination-padding-y-sm:           .25rem !default;\r\n$pagination-padding-x-sm:           .5rem !default;\r\n$pagination-padding-y-lg:           .75rem !default;\r\n$pagination-padding-x-lg:           1.5rem !default;\r\n$pagination-line-height:            1.25 !default;\r\n\r\n$pagination-color:                  $link-color !default;\r\n$pagination-bg:                     $white !default;\r\n$pagination-border-width:           $border-width !default;\r\n$pagination-border-color:           $gray-300 !default;\r\n\r\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\r\n$pagination-focus-outline:          0 !default;\r\n\r\n$pagination-hover-color:            $link-hover-color !default;\r\n$pagination-hover-bg:               $gray-200 !default;\r\n$pagination-hover-border-color:     $gray-300 !default;\r\n\r\n$pagination-active-color:           $component-active-color !default;\r\n$pagination-active-bg:              $component-active-bg !default;\r\n$pagination-active-border-color:    $pagination-active-bg !default;\r\n\r\n$pagination-disabled-color:         $gray-600 !default;\r\n$pagination-disabled-bg:            $white !default;\r\n$pagination-disabled-border-color:  $gray-300 !default;\r\n\r\n\r\n// Jumbotron\r\n\r\n$jumbotron-padding:                 2rem !default;\r\n$jumbotron-color:                   null !default;\r\n$jumbotron-bg:                      $gray-200 !default;\r\n\r\n\r\n// Cards\r\n\r\n$card-spacer-y:                     .75rem !default;\r\n$card-spacer-x:                     1.25rem !default;\r\n$card-border-width:                 $border-width !default;\r\n$card-border-radius:                $border-radius !default;\r\n$card-border-color:                 rgba($black, .125) !default;\r\n$card-inner-border-radius:          calc(#{$card-border-radius} - #{$card-border-width}) !default;\r\n$card-cap-bg:                       rgba($black, .03) !default;\r\n$card-cap-color:                    null !default;\r\n$card-color:                        null !default;\r\n$card-bg:                           $white !default;\r\n\r\n$card-img-overlay-padding:          1.25rem !default;\r\n\r\n$card-group-margin:                 $grid-gutter-width / 2 !default;\r\n$card-deck-margin:                  $card-group-margin !default;\r\n\r\n$card-columns-count:                3 !default;\r\n$card-columns-gap:                  1.25rem !default;\r\n$card-columns-margin:               $card-spacer-y !default;\r\n\r\n\r\n// Tooltips\r\n\r\n$tooltip-font-size:                 $font-size-sm !default;\r\n$tooltip-max-width:                 200px !default;\r\n$tooltip-color:                     $white !default;\r\n$tooltip-bg:                        $black !default;\r\n$tooltip-border-radius:             $border-radius !default;\r\n$tooltip-opacity:                   .9 !default;\r\n$tooltip-padding-y:                 .25rem !default;\r\n$tooltip-padding-x:                 .5rem !default;\r\n$tooltip-margin:                    0 !default;\r\n\r\n$tooltip-arrow-width:               .8rem !default;\r\n$tooltip-arrow-height:              .4rem !default;\r\n$tooltip-arrow-color:               $tooltip-bg !default;\r\n\r\n// Form tooltips must come after regular tooltips\r\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\r\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\r\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\r\n$form-feedback-tooltip-line-height:   $line-height-base !default;\r\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\r\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\r\n\r\n\r\n// Popovers\r\n\r\n$popover-font-size:                 $font-size-sm !default;\r\n$popover-bg:                        $white !default;\r\n$popover-max-width:                 276px !default;\r\n$popover-border-width:              $border-width !default;\r\n$popover-border-color:              rgba($black, .2) !default;\r\n$popover-border-radius:             $border-radius-lg !default;\r\n$popover-box-shadow:                0 .25rem .5rem rgba($black, .2) !default;\r\n\r\n$popover-header-bg:                 darken($popover-bg, 3%) !default;\r\n$popover-header-color:              $headings-color !default;\r\n$popover-header-padding-y:          .5rem !default;\r\n$popover-header-padding-x:          .75rem !default;\r\n\r\n$popover-body-color:                $body-color !default;\r\n$popover-body-padding-y:            $popover-header-padding-y !default;\r\n$popover-body-padding-x:            $popover-header-padding-x !default;\r\n\r\n$popover-arrow-width:               1rem !default;\r\n$popover-arrow-height:              .5rem !default;\r\n$popover-arrow-color:               $popover-bg !default;\r\n\r\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\r\n\r\n\r\n// Toasts\r\n\r\n$toast-max-width:                   350px !default;\r\n$toast-padding-x:                   .75rem !default;\r\n$toast-padding-y:                   .25rem !default;\r\n$toast-font-size:                   .875rem !default;\r\n$toast-color:                       null !default;\r\n$toast-background-color:            rgba($white, .85) !default;\r\n$toast-border-width:                1px !default;\r\n$toast-border-color:                rgba(0, 0, 0, .1) !default;\r\n$toast-border-radius:               .25rem !default;\r\n$toast-box-shadow:                  0 .25rem .75rem rgba($black, .1) !default;\r\n\r\n$toast-header-color:                $gray-600 !default;\r\n$toast-header-background-color:     rgba($white, .85) !default;\r\n$toast-header-border-color:         rgba(0, 0, 0, .05) !default;\r\n\r\n\r\n// Badges\r\n\r\n$badge-font-size:                   75% !default;\r\n$badge-font-weight:                 $font-weight-bold !default;\r\n$badge-padding-y:                   .25em !default;\r\n$badge-padding-x:                   .4em !default;\r\n$badge-border-radius:               $border-radius !default;\r\n\r\n$badge-transition:                  $btn-transition !default;\r\n$badge-focus-width:                 $input-btn-focus-width !default;\r\n\r\n$badge-pill-padding-x:              .6em !default;\r\n// Use a higher than normal value to ensure completely rounded edges when\r\n// customizing padding or font-size on labels.\r\n$badge-pill-border-radius:          10rem !default;\r\n\r\n\r\n// Modals\r\n\r\n// Padding applied to the modal body\r\n$modal-inner-padding:               1rem !default;\r\n\r\n$modal-dialog-margin:               .5rem !default;\r\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\r\n\r\n$modal-title-line-height:           $line-height-base !default;\r\n\r\n$modal-content-color:               null !default;\r\n$modal-content-bg:                  $white !default;\r\n$modal-content-border-color:        rgba($black, .2) !default;\r\n$modal-content-border-width:        $border-width !default;\r\n$modal-content-border-radius:       $border-radius-lg !default;\r\n$modal-content-box-shadow-xs:       0 .25rem .5rem rgba($black, .5) !default;\r\n$modal-content-box-shadow-sm-up:    0 .5rem 1rem rgba($black, .5) !default;\r\n\r\n$modal-backdrop-bg:                 $black !default;\r\n$modal-backdrop-opacity:            .5 !default;\r\n$modal-header-border-color:         $border-color !default;\r\n$modal-footer-border-color:         $modal-header-border-color !default;\r\n$modal-header-border-width:         $modal-content-border-width !default;\r\n$modal-footer-border-width:         $modal-header-border-width !default;\r\n$modal-header-padding-y:            1rem !default;\r\n$modal-header-padding-x:            1rem !default;\r\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\r\n\r\n$modal-xl:                          1140px !default;\r\n$modal-lg:                          800px !default;\r\n$modal-md:                          500px !default;\r\n$modal-sm:                          300px !default;\r\n\r\n$modal-fade-transform:              translate(0, -50px) !default;\r\n$modal-show-transform:              none !default;\r\n$modal-transition:                  transform .3s ease-out !default;\r\n\r\n\r\n// Alerts\r\n//\r\n// Define alert colors, border radius, and padding.\r\n\r\n$alert-padding-y:                   .75rem !default;\r\n$alert-padding-x:                   1.25rem !default;\r\n$alert-margin-bottom:               1rem !default;\r\n$alert-border-radius:               $border-radius !default;\r\n$alert-link-font-weight:            $font-weight-bold !default;\r\n$alert-border-width:                $border-width !default;\r\n\r\n$alert-bg-level:                    -10 !default;\r\n$alert-border-level:                -9 !default;\r\n$alert-color-level:                 6 !default;\r\n\r\n\r\n// Progress bars\r\n\r\n$progress-height:                   1rem !default;\r\n$progress-font-size:                $font-size-base * .75 !default;\r\n$progress-bg:                       $gray-200 !default;\r\n$progress-border-radius:            $border-radius !default;\r\n$progress-box-shadow:               inset 0 .1rem .1rem rgba($black, .1) !default;\r\n$progress-bar-color:                $white !default;\r\n$progress-bar-bg:                   theme-color(\"primary\") !default;\r\n$progress-bar-animation-timing:     1s linear infinite !default;\r\n$progress-bar-transition:           width .6s ease !default;\r\n\r\n\r\n// List group\r\n\r\n$list-group-color:                  null !default;\r\n$list-group-bg:                     $white !default;\r\n$list-group-border-color:           rgba($black, .125) !default;\r\n$list-group-border-width:           $border-width !default;\r\n$list-group-border-radius:          $border-radius !default;\r\n\r\n$list-group-item-padding-y:         .75rem !default;\r\n$list-group-item-padding-x:         1.25rem !default;\r\n\r\n$list-group-hover-bg:               $gray-100 !default;\r\n$list-group-active-color:           $component-active-color !default;\r\n$list-group-active-bg:              $component-active-bg !default;\r\n$list-group-active-border-color:    $list-group-active-bg !default;\r\n\r\n$list-group-disabled-color:         $gray-600 !default;\r\n$list-group-disabled-bg:            $list-group-bg !default;\r\n\r\n$list-group-action-color:           $gray-700 !default;\r\n$list-group-action-hover-color:     $list-group-action-color !default;\r\n\r\n$list-group-action-active-color:    $body-color !default;\r\n$list-group-action-active-bg:       $gray-200 !default;\r\n\r\n\r\n// Image thumbnails\r\n\r\n$thumbnail-padding:                 .25rem !default;\r\n$thumbnail-bg:                      $body-bg !default;\r\n$thumbnail-border-width:            $border-width !default;\r\n$thumbnail-border-color:            $gray-300 !default;\r\n$thumbnail-border-radius:           $border-radius !default;\r\n$thumbnail-box-shadow:              0 1px 2px rgba($black, .075) !default;\r\n\r\n\r\n// Figures\r\n\r\n$figure-caption-font-size:          90% !default;\r\n$figure-caption-color:              $gray-600 !default;\r\n\r\n\r\n// Breadcrumbs\r\n\r\n$breadcrumb-padding-y:              .75rem !default;\r\n$breadcrumb-padding-x:              1rem !default;\r\n$breadcrumb-item-padding:           .5rem !default;\r\n\r\n$breadcrumb-margin-bottom:          1rem !default;\r\n\r\n$breadcrumb-bg:                     $gray-200 !default;\r\n$breadcrumb-divider-color:          $gray-600 !default;\r\n$breadcrumb-active-color:           $gray-600 !default;\r\n$breadcrumb-divider:                quote(\"/\") !default;\r\n\r\n$breadcrumb-border-radius:          $border-radius !default;\r\n\r\n\r\n// Carousel\r\n\r\n$carousel-control-color:             $white !default;\r\n$carousel-control-width:             15% !default;\r\n$carousel-control-opacity:           .5 !default;\r\n$carousel-control-hover-opacity:     .9 !default;\r\n$carousel-control-transition:        opacity .15s ease !default;\r\n\r\n$carousel-indicator-width:           30px !default;\r\n$carousel-indicator-height:          3px !default;\r\n$carousel-indicator-hit-area-height: 10px !default;\r\n$carousel-indicator-spacer:          3px !default;\r\n$carousel-indicator-active-bg:       $white !default;\r\n$carousel-indicator-transition:      opacity .6s ease !default;\r\n\r\n$carousel-caption-width:             70% !default;\r\n$carousel-caption-color:             $white !default;\r\n\r\n$carousel-control-icon-width:        20px !default;\r\n\r\n$carousel-control-prev-icon-bg:      str-replace(url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3e%3c/svg%3e\"), \"#\", \"%23\") !default;\r\n$carousel-control-next-icon-bg:      str-replace(url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3e%3c/svg%3e\"), \"#\", \"%23\") !default;\r\n\r\n$carousel-transition-duration:       .6s !default;\r\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\r\n\r\n\r\n// Spinners\r\n\r\n$spinner-width:         2rem !default;\r\n$spinner-height:        $spinner-width !default;\r\n$spinner-border-width:  .25em !default;\r\n\r\n$spinner-width-sm:        1rem !default;\r\n$spinner-height-sm:       $spinner-width-sm !default;\r\n$spinner-border-width-sm: .2em !default;\r\n\r\n\r\n// Close\r\n\r\n$close-font-size:                   $font-size-base * 1.5 !default;\r\n$close-font-weight:                 $font-weight-bold !default;\r\n$close-color:                       $black !default;\r\n$close-text-shadow:                 0 1px 0 $white !default;\r\n\r\n\r\n// Code\r\n\r\n$code-font-size:                    87.5% !default;\r\n$code-color:                        $pink !default;\r\n\r\n$kbd-padding-y:                     .2rem !default;\r\n$kbd-padding-x:                     .4rem !default;\r\n$kbd-font-size:                     $code-font-size !default;\r\n$kbd-color:                         $white !default;\r\n$kbd-bg:                            $gray-900 !default;\r\n\r\n$pre-color:                         $gray-900 !default;\r\n$pre-scrollable-max-height:         340px !default;\r\n\r\n\r\n// Utilities\r\n\r\n$displays: none, inline, inline-block, block, table, table-row, table-cell, flex, inline-flex !default;\r\n$overflows: auto, hidden !default;\r\n$positions: static, relative, absolute, fixed, sticky !default;\r\n\r\n\r\n// Printing\r\n\r\n$print-page-size:                   a3 !default;\r\n$print-body-min-width:              map-get($grid-breakpoints, \"lg\") !default;\r\n", "// stylelint-disable property-blacklist, scss/dollar-variable-default\r\n\r\n// SCSS RFS mixin\r\n//\r\n// Automated font-resizing\r\n//\r\n// See https://github.com/twbs/rfs\r\n\r\n// Configuration\r\n\r\n// Base font size\r\n$rfs-base-font-size: 1.25rem !default;\r\n$rfs-font-size-unit: rem !default;\r\n\r\n// Breakpoint at where font-size starts decreasing if screen width is smaller\r\n$rfs-breakpoint: 1200px !default;\r\n$rfs-breakpoint-unit: px !default;\r\n\r\n// Resize font-size based on screen height and width\r\n$rfs-two-dimensional: false !default;\r\n\r\n// Factor of decrease\r\n$rfs-factor: 10 !default;\r\n\r\n@if type-of($rfs-factor) != \"number\" or $rfs-factor <= 1 {\r\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\r\n}\r\n\r\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\r\n$rfs-class: false !default;\r\n\r\n// 1 rem = $rfs-rem-value px\r\n$rfs-rem-value: 16 !default;\r\n\r\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\r\n$rfs-safari-iframe-resize-bug-fix: false !default;\r\n\r\n// Disable RFS by setting $enable-responsive-font-sizes to false\r\n$enable-responsive-font-sizes: true !default;\r\n\r\n// Cache $rfs-base-font-size unit\r\n$rfs-base-font-size-unit: unit($rfs-base-font-size);\r\n\r\n// Remove px-unit from $rfs-base-font-size for calculations\r\n@if $rfs-base-font-size-unit == \"px\" {\r\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1);\r\n}\r\n@else if $rfs-base-font-size-unit == \"rem\" {\r\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1 / $rfs-rem-value);\r\n}\r\n\r\n// Cache $rfs-breakpoint unit to prevent multiple calls\r\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\r\n\r\n// Remove unit from $rfs-breakpoint for calculations\r\n@if $rfs-breakpoint-unit-cache == \"px\" {\r\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1);\r\n}\r\n@else if $rfs-breakpoint-unit-cache == \"rem\" or $rfs-breakpoint-unit-cache == \"em\" {\r\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1 / $rfs-rem-value);\r\n}\r\n\r\n// Responsive font-size mixin\r\n@mixin rfs($fs, $important: false) {\r\n  // Cache $fs unit\r\n  $fs-unit: if(type-of($fs) == \"number\", unit($fs), false);\r\n\r\n  // Add !important suffix if needed\r\n  $rfs-suffix: if($important, \" !important\", \"\");\r\n\r\n  // If $fs isn't a number (like inherit) or $fs has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\r\n  @if not $fs-unit or $fs-unit != \"\" and $fs-unit != \"px\" and $fs-unit != \"rem\" or $fs == 0 {\r\n    font-size: #{$fs}#{$rfs-suffix};\r\n  }\r\n  @else {\r\n    // Variables for storing static and fluid rescaling\r\n    $rfs-static: null;\r\n    $rfs-fluid: null;\r\n\r\n    // Remove px-unit from $fs for calculations\r\n    @if $fs-unit == \"px\" {\r\n      $fs: $fs / ($fs * 0 + 1);\r\n    }\r\n    @else if $fs-unit == \"rem\" {\r\n      $fs: $fs / ($fs * 0 + 1 / $rfs-rem-value);\r\n    }\r\n\r\n    // Set default font-size\r\n    @if $rfs-font-size-unit == rem {\r\n      $rfs-static: #{$fs / $rfs-rem-value}rem#{$rfs-suffix};\r\n    }\r\n    @else if $rfs-font-size-unit == px {\r\n      $rfs-static: #{$fs}px#{$rfs-suffix};\r\n    }\r\n    @else {\r\n      @error \"`#{$rfs-font-size-unit}` is not a valid unit for $rfs-font-size-unit. Use `px` or `rem`.\";\r\n    }\r\n\r\n    // Only add media query if font-size is bigger as the minimum font-size\r\n    // If $rfs-factor == 1, no rescaling will take place\r\n    @if $fs > $rfs-base-font-size and $enable-responsive-font-sizes {\r\n      $min-width: null;\r\n      $variable-unit: null;\r\n\r\n      // Calculate minimum font-size for given font-size\r\n      $fs-min: $rfs-base-font-size + ($fs - $rfs-base-font-size) / $rfs-factor;\r\n\r\n      // Calculate difference between given font-size and minimum font-size for given font-size\r\n      $fs-diff: $fs - $fs-min;\r\n\r\n      // Base font-size formatting\r\n      // No need to check if the unit is valid, because we did that before\r\n      $min-width: if($rfs-font-size-unit == rem, #{$fs-min / $rfs-rem-value}rem, #{$fs-min}px);\r\n\r\n      // If two-dimensional, use smallest of screen width and height\r\n      $variable-unit: if($rfs-two-dimensional, vmin, vw);\r\n\r\n      // Calculate the variable width between 0 and $rfs-breakpoint\r\n      $variable-width: #{$fs-diff * 100 / $rfs-breakpoint}#{$variable-unit};\r\n\r\n      // Set the calculated font-size.\r\n      $rfs-fluid: calc(#{$min-width} + #{$variable-width}) #{$rfs-suffix};\r\n    }\r\n\r\n    // Rendering\r\n    @if $rfs-fluid == null {\r\n      // Only render static font-size if no fluid font-size is available\r\n      font-size: $rfs-static;\r\n    }\r\n    @else {\r\n      $mq-value: null;\r\n\r\n      // RFS breakpoint formatting\r\n      @if $rfs-breakpoint-unit == em or $rfs-breakpoint-unit == rem {\r\n        $mq-value: #{$rfs-breakpoint / $rfs-rem-value}#{$rfs-breakpoint-unit};\r\n      }\r\n      @else if $rfs-breakpoint-unit == px {\r\n        $mq-value: #{$rfs-breakpoint}px;\r\n      }\r\n      @else {\r\n        @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\r\n      }\r\n\r\n      @if $rfs-class == \"disable\" {\r\n        // Adding an extra class increases specificity,\r\n        // which prevents the media query to override the font size\r\n        &,\r\n        .disable-responsive-font-size &,\r\n        &.disable-responsive-font-size {\r\n          font-size: $rfs-static;\r\n        }\r\n      }\r\n      @else {\r\n        font-size: $rfs-static;\r\n      }\r\n\r\n      @if $rfs-two-dimensional {\r\n        @media (max-width: #{$mq-value}), (max-height: #{$mq-value}) {\r\n          @if $rfs-class == \"enable\" {\r\n            .enable-responsive-font-size &,\r\n            &.enable-responsive-font-size {\r\n              font-size: $rfs-fluid;\r\n            }\r\n          }\r\n          @else {\r\n            font-size: $rfs-fluid;\r\n          }\r\n\r\n          @if $rfs-safari-iframe-resize-bug-fix {\r\n            // stylelint-disable-next-line length-zero-no-unit\r\n            min-width: 0vw;\r\n          }\r\n        }\r\n      }\r\n      @else {\r\n        @media (max-width: #{$mq-value}) {\r\n          @if $rfs-class == \"enable\" {\r\n            .enable-responsive-font-size &,\r\n            &.enable-responsive-font-size {\r\n              font-size: $rfs-fluid;\r\n            }\r\n          }\r\n          @else {\r\n            font-size: $rfs-fluid;\r\n          }\r\n\r\n          @if $rfs-safari-iframe-resize-bug-fix {\r\n            // stylelint-disable-next-line length-zero-no-unit\r\n            min-width: 0vw;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// The font-size & responsive-font-size mixin uses RFS to rescale font sizes\r\n@mixin font-size($fs, $important: false) {\r\n  @include rfs($fs, $important);\r\n}\r\n\r\n@mixin responsive-font-size($fs, $important: false) {\r\n  @include rfs($fs, $important);\r\n}\r\n", "// Hover mixin and `$enable-hover-media-query` are deprecated.\r\n//\r\n// Originally added during our alphas and maintained during betas, this mixin was\r\n// designed to prevent `:hover` stickiness on iOS-an issue where hover styles\r\n// would persist after initial touch.\r\n//\r\n// For backward compatibility, we've kept these mixins and updated them to\r\n// always return their regular pseudo-classes instead of a shimmed media query.\r\n//\r\n// Issue: https://github.com/twbs/bootstrap/issues/25195\r\n\r\n@mixin hover {\r\n  &:hover { @content; }\r\n}\r\n\r\n@mixin hover-focus {\r\n  &:hover,\r\n  &:focus {\r\n    @content;\r\n  }\r\n}\r\n\r\n@mixin plain-hover-focus {\r\n  &,\r\n  &:hover,\r\n  &:focus {\r\n    @content;\r\n  }\r\n}\r\n\r\n@mixin hover-focus-active {\r\n  &:hover,\r\n  &:focus,\r\n  &:active {\r\n    @content;\r\n  }\r\n}\r\n", "// Lists\r\n\r\n// Unstyled keeps list items block level, just removes default browser padding and list-style\r\n@mixin list-unstyled {\r\n  padding-left: 0;\r\n  list-style: none;\r\n}\r\n", "// Responsive images (ensure images don't scale beyond their parents)\r\n//\r\n// This is purposefully opt-in via an explicit class rather than being the default for all `<img>`s.\r\n// We previously tried the \"images are responsive by default\" approach in Bootstrap v2,\r\n// and abandoned it in Bootstrap v3 because it breaks lots of third-party widgets (including Google Maps)\r\n// which weren't expecting the images within themselves to be involuntarily resized.\r\n// See also https://github.com/twbs/bootstrap/issues/18178\r\n.img-fluid {\r\n  @include img-fluid;\r\n}\r\n\r\n\r\n// Image thumbnails\r\n.img-thumbnail {\r\n  padding: $thumbnail-padding;\r\n  background-color: $thumbnail-bg;\r\n  border: $thumbnail-border-width solid $thumbnail-border-color;\r\n  @include border-radius($thumbnail-border-radius);\r\n  @include box-shadow($thumbnail-box-shadow);\r\n\r\n  // Keep them at most 100% wide\r\n  @include img-fluid;\r\n}\r\n\r\n//\r\n// Figures\r\n//\r\n\r\n.figure {\r\n  // Ensures the caption's text aligns with the image.\r\n  display: inline-block;\r\n}\r\n\r\n.figure-img {\r\n  margin-bottom: $spacer / 2;\r\n  line-height: 1;\r\n}\r\n\r\n.figure-caption {\r\n  @include font-size($figure-caption-font-size);\r\n  color: $figure-caption-color;\r\n}\r\n", "// Image Mixins\r\n// - Responsive image\r\n// - Retina image\r\n\r\n\r\n// Responsive image\r\n//\r\n// Keep images from scaling beyond the width of their parents.\r\n\r\n@mixin img-fluid {\r\n  // Part 1: Set a maximum relative to the parent\r\n  max-width: 100%;\r\n  // Part 2: Override the height to auto, otherwise images will be stretched\r\n  // when setting a width and height attribute on the img element.\r\n  height: auto;\r\n}\r\n\r\n\r\n// Retina image\r\n//\r\n// Short retina mixin for setting background-image and -size.\r\n\r\n@mixin img-retina($file-1x, $file-2x, $width-1x, $height-1x) {\r\n  background-image: url($file-1x);\r\n\r\n  // Autoprefixer takes care of adding -webkit-min-device-pixel-ratio and -o-min-device-pixel-ratio,\r\n  // but doesn't convert dppx=>dpi.\r\n  // There's no such thing as unprefixed min-device-pixel-ratio since it's nonstandard.\r\n  // Compatibility info: https://caniuse.com/#feat=css-media-resolution\r\n  @media only screen and (min-resolution: 192dpi), // IE9-11 don't support dppx\r\n    only screen and (min-resolution: 2dppx) { // Standardized\r\n    background-image: url($file-2x);\r\n    background-size: $width-1x $height-1x;\r\n  }\r\n  @include deprecate(\"`img-retina()`\", \"v4.3.0\", \"v5\");\r\n}\r\n", "// stylelint-disable property-blacklist\r\n// Single side border-radius\r\n\r\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\r\n  @if $enable-rounded {\r\n    border-radius: $radius;\r\n  }\r\n  @else if $fallback-border-radius != false {\r\n    border-radius: $fallback-border-radius;\r\n  }\r\n}\r\n\r\n@mixin border-top-radius($radius) {\r\n  @if $enable-rounded {\r\n    border-top-left-radius: $radius;\r\n    border-top-right-radius: $radius;\r\n  }\r\n}\r\n\r\n@mixin border-right-radius($radius) {\r\n  @if $enable-rounded {\r\n    border-top-right-radius: $radius;\r\n    border-bottom-right-radius: $radius;\r\n  }\r\n}\r\n\r\n@mixin border-bottom-radius($radius) {\r\n  @if $enable-rounded {\r\n    border-bottom-right-radius: $radius;\r\n    border-bottom-left-radius: $radius;\r\n  }\r\n}\r\n\r\n@mixin border-left-radius($radius) {\r\n  @if $enable-rounded {\r\n    border-top-left-radius: $radius;\r\n    border-bottom-left-radius: $radius;\r\n  }\r\n}\r\n\r\n@mixin border-top-left-radius($radius) {\r\n  @if $enable-rounded {\r\n    border-top-left-radius: $radius;\r\n  }\r\n}\r\n\r\n@mixin border-top-right-radius($radius) {\r\n  @if $enable-rounded {\r\n    border-top-right-radius: $radius;\r\n  }\r\n}\r\n\r\n@mixin border-bottom-right-radius($radius) {\r\n  @if $enable-rounded {\r\n    border-bottom-right-radius: $radius;\r\n  }\r\n}\r\n\r\n@mixin border-bottom-left-radius($radius) {\r\n  @if $enable-rounded {\r\n    border-bottom-left-radius: $radius;\r\n  }\r\n}\r\n", "// Inline code\r\ncode {\r\n  @include font-size($code-font-size);\r\n  color: $code-color;\r\n  word-break: break-word;\r\n\r\n  // Streamline the style when inside anchors to avoid broken underline and more\r\n  a > & {\r\n    color: inherit;\r\n  }\r\n}\r\n\r\n// User input typically entered via keyboard\r\nkbd {\r\n  padding: $kbd-padding-y $kbd-padding-x;\r\n  @include font-size($kbd-font-size);\r\n  color: $kbd-color;\r\n  background-color: $kbd-bg;\r\n  @include border-radius($border-radius-sm);\r\n  @include box-shadow($kbd-box-shadow);\r\n\r\n  kbd {\r\n    padding: 0;\r\n    @include font-size(100%);\r\n    font-weight: $nested-kbd-font-weight;\r\n    @include box-shadow(none);\r\n  }\r\n}\r\n\r\n// Blocks of code\r\npre {\r\n  display: block;\r\n  @include font-size($code-font-size);\r\n  color: $pre-color;\r\n\r\n  // Account for some code outputs that place code tags in pre tags\r\n  code {\r\n    @include font-size(inherit);\r\n    color: inherit;\r\n    word-break: normal;\r\n  }\r\n}\r\n\r\n// Enable scrollable blocks of code\r\n.pre-scrollable {\r\n  max-height: $pre-scrollable-max-height;\r\n  overflow-y: scroll;\r\n}\r\n", "// Container widths\r\n//\r\n// Set the container width, and override it for fixed navbars in media queries.\r\n\r\n@if $enable-grid-classes {\r\n  .container {\r\n    @include make-container();\r\n    @include make-container-max-widths();\r\n  }\r\n}\r\n\r\n// Fluid container\r\n//\r\n// Utilizes the mixin meant for fixed width containers, but with 100% width for\r\n// fluid, full width layouts.\r\n\r\n@if $enable-grid-classes {\r\n  .container-fluid {\r\n    @include make-container();\r\n  }\r\n}\r\n\r\n// Row\r\n//\r\n// Rows contain and clear the floats of your columns.\r\n\r\n@if $enable-grid-classes {\r\n  .row {\r\n    @include make-row();\r\n  }\r\n\r\n  // Remove the negative margin from default .row, then the horizontal padding\r\n  // from all immediate children columns (to prevent runaway style inheritance).\r\n  .no-gutters {\r\n    margin-right: 0;\r\n    margin-left: 0;\r\n\r\n    > .col,\r\n    > [class*=\"col-\"] {\r\n      padding-right: 0;\r\n      padding-left: 0;\r\n    }\r\n  }\r\n}\r\n\r\n// Columns\r\n//\r\n// Common styles for small and large grid columns\r\n\r\n@if $enable-grid-classes {\r\n  @include make-grid-columns();\r\n}\r\n", "/// Grid system\r\n//\r\n// Generate semantic grid columns with these mixins.\r\n\r\n@mixin make-container($gutter: $grid-gutter-width) {\r\n  width: 100%;\r\n  padding-right: $gutter / 2;\r\n  padding-left: $gutter / 2;\r\n  margin-right: auto;\r\n  margin-left: auto;\r\n}\r\n\r\n\r\n// For each breakpoint, define the maximum width of the container in a media query\r\n@mixin make-container-max-widths($max-widths: $container-max-widths, $breakpoints: $grid-breakpoints) {\r\n  @each $breakpoint, $container-max-width in $max-widths {\r\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\r\n      max-width: $container-max-width;\r\n    }\r\n  }\r\n}\r\n\r\n@mixin make-row($gutter: $grid-gutter-width) {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-right: -$gutter / 2;\r\n  margin-left: -$gutter / 2;\r\n}\r\n\r\n@mixin make-col-ready($gutter: $grid-gutter-width) {\r\n  position: relative;\r\n  // Prevent columns from becoming too narrow when at smaller grid tiers by\r\n  // always setting `width: 100%;`. This works because we use `flex` values\r\n  // later on to override this initial width.\r\n  width: 100%;\r\n  padding-right: $gutter / 2;\r\n  padding-left: $gutter / 2;\r\n}\r\n\r\n@mixin make-col($size, $columns: $grid-columns) {\r\n  flex: 0 0 percentage($size / $columns);\r\n  // Add a `max-width` to ensure content within each column does not blow out\r\n  // the width of the column. Applies to IE10+ and Firefox. Chrome and Safari\r\n  // do not appear to require this.\r\n  max-width: percentage($size / $columns);\r\n}\r\n\r\n@mixin make-col-offset($size, $columns: $grid-columns) {\r\n  $num: $size / $columns;\r\n  margin-left: if($num == 0, 0, percentage($num));\r\n}\r\n", "// Breakpoint viewport sizes and media queries.\r\n//\r\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\r\n//\r\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\r\n//\r\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\r\n\r\n// Name of the next breakpoint, or null for the last breakpoint.\r\n//\r\n//    >> breakpoint-next(sm)\r\n//    md\r\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\r\n//    md\r\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\r\n//    md\r\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\r\n  $n: index($breakpoint-names, $name);\r\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\r\n}\r\n\r\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\r\n//\r\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\r\n//    576px\r\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\r\n  $min: map-get($breakpoints, $name);\r\n  @return if($min != 0, $min, null);\r\n}\r\n\r\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\r\n// The maximum value is calculated as the minimum of the next one less 0.02px\r\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\r\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\r\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\r\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\r\n//\r\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\r\n//    767.98px\r\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\r\n  $next: breakpoint-next($name, $breakpoints);\r\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\r\n}\r\n\r\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\r\n// Useful for making responsive utilities.\r\n//\r\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\r\n//    \"\"  (Returns a blank string)\r\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\r\n//    \"-sm\"\r\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\r\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\r\n}\r\n\r\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\r\n// Makes the @content apply to the given breakpoint and wider.\r\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\r\n  $min: breakpoint-min($name, $breakpoints);\r\n  @if $min {\r\n    @media (min-width: $min) {\r\n      @content;\r\n    }\r\n  } @else {\r\n    @content;\r\n  }\r\n}\r\n\r\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\r\n// Makes the @content apply to the given breakpoint and narrower.\r\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\r\n  $max: breakpoint-max($name, $breakpoints);\r\n  @if $max {\r\n    @media (max-width: $max) {\r\n      @content;\r\n    }\r\n  } @else {\r\n    @content;\r\n  }\r\n}\r\n\r\n// Media that spans multiple breakpoint widths.\r\n// Makes the @content apply between the min and max breakpoints\r\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\r\n  $min: breakpoint-min($lower, $breakpoints);\r\n  $max: breakpoint-max($upper, $breakpoints);\r\n\r\n  @if $min != null and $max != null {\r\n    @media (min-width: $min) and (max-width: $max) {\r\n      @content;\r\n    }\r\n  } @else if $max == null {\r\n    @include media-breakpoint-up($lower, $breakpoints) {\r\n      @content;\r\n    }\r\n  } @else if $min == null {\r\n    @include media-breakpoint-down($upper, $breakpoints) {\r\n      @content;\r\n    }\r\n  }\r\n}\r\n\r\n// Media between the breakpoint's minimum and maximum widths.\r\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\r\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\r\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\r\n  $min: breakpoint-min($name, $breakpoints);\r\n  $max: breakpoint-max($name, $breakpoints);\r\n\r\n  @if $min != null and $max != null {\r\n    @media (min-width: $min) and (max-width: $max) {\r\n      @content;\r\n    }\r\n  } @else if $max == null {\r\n    @include media-breakpoint-up($name, $breakpoints) {\r\n      @content;\r\n    }\r\n  } @else if $min == null {\r\n    @include media-breakpoint-down($name, $breakpoints) {\r\n      @content;\r\n    }\r\n  }\r\n}\r\n", "// Framework grid generation\r\n//\r\n// Used only by <PERSON><PERSON><PERSON> to generate the correct number of grid classes given\r\n// any value of `$grid-columns`.\r\n\r\n@mixin make-grid-columns($columns: $grid-columns, $gutter: $grid-gutter-width, $breakpoints: $grid-breakpoints) {\r\n  // Common properties for all breakpoints\r\n  %grid-column {\r\n    position: relative;\r\n    width: 100%;\r\n    padding-right: $gutter / 2;\r\n    padding-left: $gutter / 2;\r\n  }\r\n\r\n  @each $breakpoint in map-keys($breakpoints) {\r\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\r\n\r\n    // Allow columns to stretch full width below their breakpoints\r\n    @for $i from 1 through $columns {\r\n      .col#{$infix}-#{$i} {\r\n        @extend %grid-column;\r\n      }\r\n    }\r\n    .col#{$infix},\r\n    .col#{$infix}-auto {\r\n      @extend %grid-column;\r\n    }\r\n\r\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\r\n      // Provide basic `.col-{bp}` classes for equal-width flexbox columns\r\n      .col#{$infix} {\r\n        flex-basis: 0;\r\n        flex-grow: 1;\r\n        max-width: 100%;\r\n      }\r\n      .col#{$infix}-auto {\r\n        flex: 0 0 auto;\r\n        width: auto;\r\n        max-width: 100%; // Reset earlier grid tiers\r\n      }\r\n\r\n      @for $i from 1 through $columns {\r\n        .col#{$infix}-#{$i} {\r\n          @include make-col($i, $columns);\r\n        }\r\n      }\r\n\r\n      .order#{$infix}-first { order: -1; }\r\n\r\n      .order#{$infix}-last { order: $columns + 1; }\r\n\r\n      @for $i from 0 through $columns {\r\n        .order#{$infix}-#{$i} { order: $i; }\r\n      }\r\n\r\n      // `$columns - 1` because offsetting by the width of an entire row isn't possible\r\n      @for $i from 0 through ($columns - 1) {\r\n        @if not ($infix == \"\" and $i == 0) { // Avoid emitting useless .offset-0\r\n          .offset#{$infix}-#{$i} {\r\n            @include make-col-offset($i, $columns);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n", "//\r\n// Basic Bootstrap table\r\n//\r\n\r\n.table {\r\n  width: 100%;\r\n  margin-bottom: $spacer;\r\n  color: $table-color;\r\n  background-color: $table-bg; // Reset for nesting within parents with `background-color`.\r\n\r\n  th,\r\n  td {\r\n    padding: $table-cell-padding;\r\n    vertical-align: top;\r\n    border-top: $table-border-width solid $table-border-color;\r\n  }\r\n\r\n  thead th {\r\n    vertical-align: bottom;\r\n    border-bottom: (2 * $table-border-width) solid $table-border-color;\r\n  }\r\n\r\n  tbody + tbody {\r\n    border-top: (2 * $table-border-width) solid $table-border-color;\r\n  }\r\n}\r\n\r\n\r\n//\r\n// Condensed table w/ half padding\r\n//\r\n\r\n.table-sm {\r\n  th,\r\n  td {\r\n    padding: $table-cell-padding-sm;\r\n  }\r\n}\r\n\r\n\r\n// Border versions\r\n//\r\n// Add or remove borders all around the table and between all the columns.\r\n\r\n.table-bordered {\r\n  border: $table-border-width solid $table-border-color;\r\n\r\n  th,\r\n  td {\r\n    border: $table-border-width solid $table-border-color;\r\n  }\r\n\r\n  thead {\r\n    th,\r\n    td {\r\n      border-bottom-width: 2 * $table-border-width;\r\n    }\r\n  }\r\n}\r\n\r\n.table-borderless {\r\n  th,\r\n  td,\r\n  thead th,\r\n  tbody + tbody {\r\n    border: 0;\r\n  }\r\n}\r\n\r\n// Zebra-striping\r\n//\r\n// Default zebra-stripe styles (alternating gray and transparent backgrounds)\r\n\r\n.table-striped {\r\n  tbody tr:nth-of-type(#{$table-striped-order}) {\r\n    background-color: $table-accent-bg;\r\n  }\r\n}\r\n\r\n\r\n// Hover effect\r\n//\r\n// Placed here since it has to come after the potential zebra striping\r\n\r\n.table-hover {\r\n  tbody tr {\r\n    @include hover {\r\n      color: $table-hover-color;\r\n      background-color: $table-hover-bg;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Table backgrounds\r\n//\r\n// Exact selectors below required to override `.table-striped` and prevent\r\n// inheritance to nested tables.\r\n\r\n@each $color, $value in $theme-colors {\r\n  @include table-row-variant($color, theme-color-level($color, $table-bg-level), theme-color-level($color, $table-border-level));\r\n}\r\n\r\n@include table-row-variant(active, $table-active-bg);\r\n\r\n\r\n// Dark styles\r\n//\r\n// Same table markup, but inverted color scheme: dark background and light text.\r\n\r\n// stylelint-disable-next-line no-duplicate-selectors\r\n.table {\r\n  .thead-dark {\r\n    th {\r\n      color: $table-dark-color;\r\n      background-color: $table-dark-bg;\r\n      border-color: $table-dark-border-color;\r\n    }\r\n  }\r\n\r\n  .thead-light {\r\n    th {\r\n      color: $table-head-color;\r\n      background-color: $table-head-bg;\r\n      border-color: $table-border-color;\r\n    }\r\n  }\r\n}\r\n\r\n.table-dark {\r\n  color: $table-dark-color;\r\n  background-color: $table-dark-bg;\r\n\r\n  th,\r\n  td,\r\n  thead th {\r\n    border-color: $table-dark-border-color;\r\n  }\r\n\r\n  &.table-bordered {\r\n    border: 0;\r\n  }\r\n\r\n  &.table-striped {\r\n    tbody tr:nth-of-type(odd) {\r\n      background-color: $table-dark-accent-bg;\r\n    }\r\n  }\r\n\r\n  &.table-hover {\r\n    tbody tr {\r\n      @include hover {\r\n        color: $table-dark-hover-color;\r\n        background-color: $table-dark-hover-bg;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Responsive tables\r\n//\r\n// Generate series of `.table-responsive-*` classes for configuring the screen\r\n// size of where your table will overflow.\r\n\r\n.table-responsive {\r\n  @each $breakpoint in map-keys($grid-breakpoints) {\r\n    $next: breakpoint-next($breakpoint, $grid-breakpoints);\r\n    $infix: breakpoint-infix($next, $grid-breakpoints);\r\n\r\n    &#{$infix} {\r\n      @include media-breakpoint-down($breakpoint) {\r\n        display: block;\r\n        width: 100%;\r\n        overflow-x: auto;\r\n        -webkit-overflow-scrolling: touch;\r\n\r\n        // Prevent double border on horizontal scroll due to use of `display: block;`\r\n        > .table-bordered {\r\n          border: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n", "// Tables\r\n\r\n@mixin table-row-variant($state, $background, $border: null) {\r\n  // Exact selectors below required to override `.table-striped` and prevent\r\n  // inheritance to nested tables.\r\n  .table-#{$state} {\r\n    &,\r\n    > th,\r\n    > td {\r\n      background-color: $background;\r\n    }\r\n\r\n    @if $border != null {\r\n      th,\r\n      td,\r\n      thead th,\r\n      tbody + tbody {\r\n        border-color: $border;\r\n      }\r\n    }\r\n  }\r\n\r\n  // Hover states for `.table-hover`\r\n  // Note: this is not available for cells or rows within `thead` or `tfoot`.\r\n  .table-hover {\r\n    $hover-background: darken($background, 5%);\r\n\r\n    .table-#{$state} {\r\n      @include hover {\r\n        background-color: $hover-background;\r\n\r\n        > td,\r\n        > th {\r\n          background-color: $hover-background;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n", "// stylelint-disable selector-no-qualifying-type\r\n\r\n//\r\n// Textual form controls\r\n//\r\n\r\n.form-control {\r\n  display: block;\r\n  width: 100%;\r\n  height: $input-height;\r\n  padding: $input-padding-y $input-padding-x;\r\n  font-family: $input-font-family;\r\n  @include font-size($input-font-size);\r\n  font-weight: $input-font-weight;\r\n  line-height: $input-line-height;\r\n  color: $input-color;\r\n  background-color: $input-bg;\r\n  background-clip: padding-box;\r\n  border: $input-border-width solid $input-border-color;\r\n\r\n  // Note: This has no effect on <select>s in some browsers, due to the limited stylability of `<select>`s in CSS.\r\n  @include border-radius($input-border-radius, 0);\r\n\r\n  @include box-shadow($input-box-shadow);\r\n  @include transition($input-transition);\r\n\r\n  // Unstyle the caret on `<select>`s in IE10+.\r\n  &::-ms-expand {\r\n    background-color: transparent;\r\n    border: 0;\r\n  }\r\n\r\n  // Customize the `:focus` state to imitate native WebKit styles.\r\n  @include form-control-focus();\r\n\r\n  // Placeholder\r\n  &::placeholder {\r\n    color: $input-placeholder-color;\r\n    // Override Firefox's unusual default opacity; see https://github.com/twbs/bootstrap/pull/11526.\r\n    opacity: 1;\r\n  }\r\n\r\n  // Disabled and read-only inputs\r\n  //\r\n  // HTML5 says that controls under a fieldset > legend:first-child won't be\r\n  // disabled if the fieldset is disabled. Due to implementation difficulty, we\r\n  // don't honor that edge case; we style them as disabled anyway.\r\n  &:disabled,\r\n  &[readonly] {\r\n    background-color: $input-disabled-bg;\r\n    // iOS fix for unreadable disabled content; see https://github.com/twbs/bootstrap/issues/11655.\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\nselect.form-control {\r\n  &:focus::-ms-value {\r\n    // Suppress the nested default white text on blue background highlight given to\r\n    // the selected option text when the (still closed) <select> receives focus\r\n    // in IE and (under certain conditions) Edge, as it looks bad and cannot be made to\r\n    // match the appearance of the native widget.\r\n    // See https://github.com/twbs/bootstrap/issues/19398.\r\n    color: $input-color;\r\n    background-color: $input-bg;\r\n  }\r\n}\r\n\r\n// Make file inputs better match text inputs by forcing them to new lines.\r\n.form-control-file,\r\n.form-control-range {\r\n  display: block;\r\n  width: 100%;\r\n}\r\n\r\n\r\n//\r\n// Labels\r\n//\r\n\r\n// For use with horizontal and inline forms, when you need the label (or legend)\r\n// text to align with the form controls.\r\n.col-form-label {\r\n  padding-top: calc(#{$input-padding-y} + #{$input-border-width});\r\n  padding-bottom: calc(#{$input-padding-y} + #{$input-border-width});\r\n  margin-bottom: 0; // Override the `<label>/<legend>` default\r\n  @include font-size(inherit); // Override the `<legend>` default\r\n  line-height: $input-line-height;\r\n}\r\n\r\n.col-form-label-lg {\r\n  padding-top: calc(#{$input-padding-y-lg} + #{$input-border-width});\r\n  padding-bottom: calc(#{$input-padding-y-lg} + #{$input-border-width});\r\n  @include font-size($input-font-size-lg);\r\n  line-height: $input-line-height-lg;\r\n}\r\n\r\n.col-form-label-sm {\r\n  padding-top: calc(#{$input-padding-y-sm} + #{$input-border-width});\r\n  padding-bottom: calc(#{$input-padding-y-sm} + #{$input-border-width});\r\n  @include font-size($input-font-size-sm);\r\n  line-height: $input-line-height-sm;\r\n}\r\n\r\n\r\n// Readonly controls as plain text\r\n//\r\n// Apply class to a readonly input to make it appear like regular plain\r\n// text (without any border, background color, focus indicator)\r\n\r\n.form-control-plaintext {\r\n  display: block;\r\n  width: 100%;\r\n  padding-top: $input-padding-y;\r\n  padding-bottom: $input-padding-y;\r\n  margin-bottom: 0; // match inputs if this class comes on inputs with default margins\r\n  line-height: $input-line-height;\r\n  color: $input-plaintext-color;\r\n  background-color: transparent;\r\n  border: solid transparent;\r\n  border-width: $input-border-width 0;\r\n\r\n  &.form-control-sm,\r\n  &.form-control-lg {\r\n    padding-right: 0;\r\n    padding-left: 0;\r\n  }\r\n}\r\n\r\n\r\n// Form control sizing\r\n//\r\n// Build on `.form-control` with modifier classes to decrease or increase the\r\n// height and font-size of form controls.\r\n//\r\n// Repeated in `_input_group.scss` to avoid Sass extend issues.\r\n\r\n.form-control-sm {\r\n  height: $input-height-sm;\r\n  padding: $input-padding-y-sm $input-padding-x-sm;\r\n  @include font-size($input-font-size-sm);\r\n  line-height: $input-line-height-sm;\r\n  @include border-radius($input-border-radius-sm);\r\n}\r\n\r\n.form-control-lg {\r\n  height: $input-height-lg;\r\n  padding: $input-padding-y-lg $input-padding-x-lg;\r\n  @include font-size($input-font-size-lg);\r\n  line-height: $input-line-height-lg;\r\n  @include border-radius($input-border-radius-lg);\r\n}\r\n\r\n// stylelint-disable-next-line no-duplicate-selectors\r\nselect.form-control {\r\n  &[size],\r\n  &[multiple] {\r\n    height: auto;\r\n  }\r\n}\r\n\r\ntextarea.form-control {\r\n  height: auto;\r\n}\r\n\r\n// Form groups\r\n//\r\n// Designed to help with the organization and spacing of vertical forms. For\r\n// horizontal forms, use the predefined grid classes.\r\n\r\n.form-group {\r\n  margin-bottom: $form-group-margin-bottom;\r\n}\r\n\r\n.form-text {\r\n  display: block;\r\n  margin-top: $form-text-margin-top;\r\n}\r\n\r\n\r\n// Form grid\r\n//\r\n// Special replacement for our grid system's `.row` for tighter form layouts.\r\n\r\n.form-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-right: -$form-grid-gutter-width / 2;\r\n  margin-left: -$form-grid-gutter-width / 2;\r\n\r\n  > .col,\r\n  > [class*=\"col-\"] {\r\n    padding-right: $form-grid-gutter-width / 2;\r\n    padding-left: $form-grid-gutter-width / 2;\r\n  }\r\n}\r\n\r\n\r\n// Checkboxes and radios\r\n//\r\n// Indent the labels to position radios/checkboxes as hanging controls.\r\n\r\n.form-check {\r\n  position: relative;\r\n  display: block;\r\n  padding-left: $form-check-input-gutter;\r\n}\r\n\r\n.form-check-input {\r\n  position: absolute;\r\n  margin-top: $form-check-input-margin-y;\r\n  margin-left: -$form-check-input-gutter;\r\n\r\n  &:disabled ~ .form-check-label {\r\n    color: $text-muted;\r\n  }\r\n}\r\n\r\n.form-check-label {\r\n  margin-bottom: 0; // Override default `<label>` bottom margin\r\n}\r\n\r\n.form-check-inline {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  padding-left: 0; // Override base .form-check\r\n  margin-right: $form-check-inline-margin-x;\r\n\r\n  // Undo .form-check-input defaults and add some `margin-right`.\r\n  .form-check-input {\r\n    position: static;\r\n    margin-top: 0;\r\n    margin-right: $form-check-inline-input-margin-x;\r\n    margin-left: 0;\r\n  }\r\n}\r\n\r\n\r\n// Form validation\r\n//\r\n// Provide feedback to users when form field values are valid or invalid. Works\r\n// primarily for client-side validation via scoped `:invalid` and `:valid`\r\n// pseudo-classes but also includes `.is-invalid` and `.is-valid` classes for\r\n// server side validation.\r\n\r\n@each $state, $data in $form-validation-states {\r\n  @include form-validation-state($state, map-get($data, color), map-get($data, icon));\r\n}\r\n\r\n// Inline forms\r\n//\r\n// Make forms appear inline(-block) by adding the `.form-inline` class. Inline\r\n// forms begin stacked on extra small (mobile) devices and then go inline when\r\n// viewports reach <768px.\r\n//\r\n// Requires wrapping inputs and labels with `.form-group` for proper display of\r\n// default HTML form controls and our custom form controls (e.g., input groups).\r\n\r\n.form-inline {\r\n  display: flex;\r\n  flex-flow: row wrap;\r\n  align-items: center; // Prevent shorter elements from growing to same height as others (e.g., small buttons growing to normal sized button height)\r\n\r\n  // Because we use flex, the initial sizing of checkboxes is collapsed and\r\n  // doesn't occupy the full-width (which is what we want for xs grid tier),\r\n  // so we force that here.\r\n  .form-check {\r\n    width: 100%;\r\n  }\r\n\r\n  // Kick in the inline\r\n  @include media-breakpoint-up(sm) {\r\n    label {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-bottom: 0;\r\n    }\r\n\r\n    // Inline-block all the things for \"inline\"\r\n    .form-group {\r\n      display: flex;\r\n      flex: 0 0 auto;\r\n      flex-flow: row wrap;\r\n      align-items: center;\r\n      margin-bottom: 0;\r\n    }\r\n\r\n    // Allow folks to *not* use `.form-group`\r\n    .form-control {\r\n      display: inline-block;\r\n      width: auto; // Prevent labels from stacking above inputs in `.form-group`\r\n      vertical-align: middle;\r\n    }\r\n\r\n    // Make static controls behave like regular ones\r\n    .form-control-plaintext {\r\n      display: inline-block;\r\n    }\r\n\r\n    .input-group,\r\n    .custom-select {\r\n      width: auto;\r\n    }\r\n\r\n    // Remove default margin on radios/checkboxes that were used for stacking, and\r\n    // then undo the floating of radios and checkboxes to match.\r\n    .form-check {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      width: auto;\r\n      padding-left: 0;\r\n    }\r\n    .form-check-input {\r\n      position: relative;\r\n      flex-shrink: 0;\r\n      margin-top: 0;\r\n      margin-right: $form-check-input-margin-x;\r\n      margin-left: 0;\r\n    }\r\n\r\n    .custom-control {\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n    .custom-control-label {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n", "// stylelint-disable property-blacklist\r\n@mixin transition($transition...) {\r\n  @if $enable-transitions {\r\n    @if length($transition) == 0 {\r\n      transition: $transition-base;\r\n    } @else {\r\n      transition: $transition;\r\n    }\r\n  }\r\n\r\n  @if $enable-prefers-reduced-motion-media-query {\r\n    @media (prefers-reduced-motion: reduce) {\r\n      transition: none;\r\n    }\r\n  }\r\n}\r\n", "// Form control focus state\r\n//\r\n// Generate a customized focus state and for any input with the specified color,\r\n// which defaults to the `$input-focus-border-color` variable.\r\n//\r\n// We highly encourage you to not customize the default value, but instead use\r\n// this to tweak colors on an as-needed basis. This aesthetic change is based on\r\n// WebKit's default styles, but applicable to a wider range of browsers. Its\r\n// usability and accessibility should be taken into account with any change.\r\n//\r\n// Example usage: change the default blue border and shadow to white for better\r\n// contrast against a dark gray background.\r\n@mixin form-control-focus() {\r\n  &:focus {\r\n    color: $input-focus-color;\r\n    background-color: $input-focus-bg;\r\n    border-color: $input-focus-border-color;\r\n    outline: 0;\r\n    // Avoid using mixin so we can pass custom focus shadow properly\r\n    @if $enable-shadows {\r\n      box-shadow: $input-box-shadow, $input-focus-box-shadow;\r\n    } @else {\r\n      box-shadow: $input-focus-box-shadow;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n@mixin form-validation-state($state, $color, $icon) {\r\n  .#{$state}-feedback {\r\n    display: none;\r\n    width: 100%;\r\n    margin-top: $form-feedback-margin-top;\r\n    @include font-size($form-feedback-font-size);\r\n    color: $color;\r\n  }\r\n\r\n  .#{$state}-tooltip {\r\n    position: absolute;\r\n    top: 100%;\r\n    z-index: 5;\r\n    display: none;\r\n    max-width: 100%; // Contain to parent when possible\r\n    padding: $form-feedback-tooltip-padding-y $form-feedback-tooltip-padding-x;\r\n    margin-top: .1rem;\r\n    @include font-size($form-feedback-tooltip-font-size);\r\n    line-height: $form-feedback-tooltip-line-height;\r\n    color: color-yiq($color);\r\n    background-color: rgba($color, $form-feedback-tooltip-opacity);\r\n    @include border-radius($form-feedback-tooltip-border-radius);\r\n  }\r\n\r\n  .form-control {\r\n    .was-validated &:#{$state},\r\n    &.is-#{$state} {\r\n      border-color: $color;\r\n\r\n      @if $enable-validation-icons {\r\n        padding-right: $input-height-inner;\r\n        background-image: $icon;\r\n        background-repeat: no-repeat;\r\n        background-position: center right $input-height-inner-quarter;\r\n        background-size: $input-height-inner-half $input-height-inner-half;\r\n      }\r\n\r\n      &:focus {\r\n        border-color: $color;\r\n        box-shadow: 0 0 0 $input-focus-width rgba($color, .25);\r\n      }\r\n\r\n      ~ .#{$state}-feedback,\r\n      ~ .#{$state}-tooltip {\r\n        display: block;\r\n      }\r\n    }\r\n  }\r\n\r\n  // stylelint-disable-next-line selector-no-qualifying-type\r\n  textarea.form-control {\r\n    .was-validated &:#{$state},\r\n    &.is-#{$state} {\r\n      @if $enable-validation-icons {\r\n        padding-right: $input-height-inner;\r\n        background-position: top $input-height-inner-quarter right $input-height-inner-quarter;\r\n      }\r\n    }\r\n  }\r\n\r\n  .custom-select {\r\n    .was-validated &:#{$state},\r\n    &.is-#{$state} {\r\n      border-color: $color;\r\n\r\n      @if $enable-validation-icons {\r\n        padding-right: $custom-select-feedback-icon-padding-right;\r\n        background: $custom-select-background, $icon $custom-select-bg no-repeat $custom-select-feedback-icon-position / $custom-select-feedback-icon-size;\r\n      }\r\n\r\n      &:focus {\r\n        border-color: $color;\r\n        box-shadow: 0 0 0 $input-focus-width rgba($color, .25);\r\n      }\r\n\r\n      ~ .#{$state}-feedback,\r\n      ~ .#{$state}-tooltip {\r\n        display: block;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .form-control-file {\r\n    .was-validated &:#{$state},\r\n    &.is-#{$state} {\r\n      ~ .#{$state}-feedback,\r\n      ~ .#{$state}-tooltip {\r\n        display: block;\r\n      }\r\n    }\r\n  }\r\n\r\n  .form-check-input {\r\n    .was-validated &:#{$state},\r\n    &.is-#{$state} {\r\n      ~ .form-check-label {\r\n        color: $color;\r\n      }\r\n\r\n      ~ .#{$state}-feedback,\r\n      ~ .#{$state}-tooltip {\r\n        display: block;\r\n      }\r\n    }\r\n  }\r\n\r\n  .custom-control-input {\r\n    .was-validated &:#{$state},\r\n    &.is-#{$state} {\r\n      ~ .custom-control-label {\r\n        color: $color;\r\n\r\n        &::before {\r\n          border-color: $color;\r\n        }\r\n      }\r\n\r\n      ~ .#{$state}-feedback,\r\n      ~ .#{$state}-tooltip {\r\n        display: block;\r\n      }\r\n\r\n      &:checked {\r\n        ~ .custom-control-label::before {\r\n          border-color: lighten($color, 10%);\r\n          @include gradient-bg(lighten($color, 10%));\r\n        }\r\n      }\r\n\r\n      &:focus {\r\n        ~ .custom-control-label::before {\r\n          box-shadow: 0 0 0 $input-focus-width rgba($color, .25);\r\n        }\r\n\r\n        &:not(:checked) ~ .custom-control-label::before {\r\n          border-color: $color;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // custom file\r\n  .custom-file-input {\r\n    .was-validated &:#{$state},\r\n    &.is-#{$state} {\r\n      ~ .custom-file-label {\r\n        border-color: $color;\r\n      }\r\n\r\n      ~ .#{$state}-feedback,\r\n      ~ .#{$state}-tooltip {\r\n        display: block;\r\n      }\r\n\r\n      &:focus {\r\n        ~ .custom-file-label {\r\n          border-color: $color;\r\n          box-shadow: 0 0 0 $input-focus-width rgba($color, .25);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n", "// Gradients\r\n\r\n@mixin gradient-bg($color) {\r\n  @if $enable-gradients {\r\n    background: $color linear-gradient(180deg, mix($body-bg, $color, 15%), $color) repeat-x;\r\n  } @else {\r\n    background-color: $color;\r\n  }\r\n}\r\n\r\n// Horizontal gradient, from left to right\r\n//\r\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\r\n@mixin gradient-x($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\r\n  background-image: linear-gradient(to right, $start-color $start-percent, $end-color $end-percent);\r\n  background-repeat: repeat-x;\r\n}\r\n\r\n// Vertical gradient, from top to bottom\r\n//\r\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\r\n@mixin gradient-y($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\r\n  background-image: linear-gradient(to bottom, $start-color $start-percent, $end-color $end-percent);\r\n  background-repeat: repeat-x;\r\n}\r\n\r\n@mixin gradient-directional($start-color: $gray-700, $end-color: $gray-800, $deg: 45deg) {\r\n  background-image: linear-gradient($deg, $start-color, $end-color);\r\n  background-repeat: repeat-x;\r\n}\r\n@mixin gradient-x-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\r\n  background-image: linear-gradient(to right, $start-color, $mid-color $color-stop, $end-color);\r\n  background-repeat: no-repeat;\r\n}\r\n@mixin gradient-y-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\r\n  background-image: linear-gradient($start-color, $mid-color $color-stop, $end-color);\r\n  background-repeat: no-repeat;\r\n}\r\n@mixin gradient-radial($inner-color: $gray-700, $outer-color: $gray-800) {\r\n  background-image: radial-gradient(circle, $inner-color, $outer-color);\r\n  background-repeat: no-repeat;\r\n}\r\n@mixin gradient-striped($color: rgba($white, .15), $angle: 45deg) {\r\n  background-image: linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);\r\n}\r\n", "// stylelint-disable selector-no-qualifying-type\r\n\r\n//\r\n// Base styles\r\n//\r\n\r\n.btn {\r\n  display: inline-block;\r\n  font-family: $btn-font-family;\r\n  font-weight: $btn-font-weight;\r\n  color: $body-color;\r\n  text-align: center;\r\n  vertical-align: middle;\r\n  user-select: none;\r\n  background-color: transparent;\r\n  border: $btn-border-width solid transparent;\r\n  @include button-size($btn-padding-y, $btn-padding-x, $btn-font-size, $btn-line-height, $btn-border-radius);\r\n  @include transition($btn-transition);\r\n\r\n  @include hover {\r\n    color: $body-color;\r\n    text-decoration: none;\r\n  }\r\n\r\n  &:focus,\r\n  &.focus {\r\n    outline: 0;\r\n    box-shadow: $btn-focus-box-shadow;\r\n  }\r\n\r\n  // Disabled comes first so active can properly restyle\r\n  &.disabled,\r\n  &:disabled {\r\n    opacity: $btn-disabled-opacity;\r\n    @include box-shadow(none);\r\n  }\r\n\r\n  &:not(:disabled):not(.disabled):active,\r\n  &:not(:disabled):not(.disabled).active {\r\n    @include box-shadow($btn-active-box-shadow);\r\n\r\n    &:focus {\r\n      @include box-shadow($btn-focus-box-shadow, $btn-active-box-shadow);\r\n    }\r\n  }\r\n}\r\n\r\n// Future-proof disabling of clicks on `<a>` elements\r\na.btn.disabled,\r\nfieldset:disabled a.btn {\r\n  pointer-events: none;\r\n}\r\n\r\n\r\n//\r\n// Alternate buttons\r\n//\r\n\r\n@each $color, $value in $theme-colors {\r\n  .btn-#{$color} {\r\n    @include button-variant($value, $value);\r\n  }\r\n}\r\n\r\n@each $color, $value in $theme-colors {\r\n  .btn-outline-#{$color} {\r\n    @include button-outline-variant($value);\r\n  }\r\n}\r\n\r\n\r\n//\r\n// Link buttons\r\n//\r\n\r\n// Make a button look and behave like a link\r\n.btn-link {\r\n  font-weight: $font-weight-normal;\r\n  color: $link-color;\r\n  text-decoration: $link-decoration;\r\n\r\n  @include hover {\r\n    color: $link-hover-color;\r\n    text-decoration: $link-hover-decoration;\r\n  }\r\n\r\n  &:focus,\r\n  &.focus {\r\n    text-decoration: $link-hover-decoration;\r\n    box-shadow: none;\r\n  }\r\n\r\n  &:disabled,\r\n  &.disabled {\r\n    color: $btn-link-disabled-color;\r\n    pointer-events: none;\r\n  }\r\n\r\n  // No need for an active state here\r\n}\r\n\r\n\r\n//\r\n// Button Sizes\r\n//\r\n\r\n.btn-lg {\r\n  @include button-size($btn-padding-y-lg, $btn-padding-x-lg, $btn-font-size-lg, $btn-line-height-lg, $btn-border-radius-lg);\r\n}\r\n\r\n.btn-sm {\r\n  @include button-size($btn-padding-y-sm, $btn-padding-x-sm, $btn-font-size-sm, $btn-line-height-sm, $btn-border-radius-sm);\r\n}\r\n\r\n\r\n//\r\n// Block button\r\n//\r\n\r\n.btn-block {\r\n  display: block;\r\n  width: 100%;\r\n\r\n  // Vertically space out multiple block buttons\r\n  + .btn-block {\r\n    margin-top: $btn-block-spacing-y;\r\n  }\r\n}\r\n\r\n// Specificity overrides\r\ninput[type=\"submit\"],\r\ninput[type=\"reset\"],\r\ninput[type=\"button\"] {\r\n  &.btn-block {\r\n    width: 100%;\r\n  }\r\n}\r\n", "// Button variants\r\n//\r\n// Easily pump out default styles, as well as :hover, :focus, :active,\r\n// and disabled options for all buttons\r\n\r\n@mixin button-variant($background, $border, $hover-background: darken($background, 7.5%), $hover-border: darken($border, 10%), $active-background: darken($background, 10%), $active-border: darken($border, 12.5%)) {\r\n  color: color-yiq($background);\r\n  @include gradient-bg($background);\r\n  border-color: $border;\r\n  @include box-shadow($btn-box-shadow);\r\n\r\n  @include hover {\r\n    color: color-yiq($hover-background);\r\n    @include gradient-bg($hover-background);\r\n    border-color: $hover-border;\r\n  }\r\n\r\n  &:focus,\r\n  &.focus {\r\n    // Avoid using mixin so we can pass custom focus shadow properly\r\n    @if $enable-shadows {\r\n      box-shadow: $btn-box-shadow, 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\r\n    } @else {\r\n      box-shadow: 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\r\n    }\r\n  }\r\n\r\n  // Disabled comes first so active can properly restyle\r\n  &.disabled,\r\n  &:disabled {\r\n    color: color-yiq($background);\r\n    background-color: $background;\r\n    border-color: $border;\r\n    // Remove CSS gradients if they're enabled\r\n    @if $enable-gradients {\r\n      background-image: none;\r\n    }\r\n  }\r\n\r\n  &:not(:disabled):not(.disabled):active,\r\n  &:not(:disabled):not(.disabled).active,\r\n  .show > &.dropdown-toggle {\r\n    color: color-yiq($active-background);\r\n    background-color: $active-background;\r\n    @if $enable-gradients {\r\n      background-image: none; // Remove the gradient for the pressed/active state\r\n    }\r\n    border-color: $active-border;\r\n\r\n    &:focus {\r\n      // Avoid using mixin so we can pass custom focus shadow properly\r\n      @if $enable-shadows and $btn-active-box-shadow != none {\r\n        box-shadow: $btn-active-box-shadow, 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\r\n      } @else {\r\n        box-shadow: 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@mixin button-outline-variant($color, $color-hover: color-yiq($color), $active-background: $color, $active-border: $color) {\r\n  color: $color;\r\n  border-color: $color;\r\n\r\n  @include hover {\r\n    color: $color-hover;\r\n    background-color: $active-background;\r\n    border-color: $active-border;\r\n  }\r\n\r\n  &:focus,\r\n  &.focus {\r\n    box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\r\n  }\r\n\r\n  &.disabled,\r\n  &:disabled {\r\n    color: $color;\r\n    background-color: transparent;\r\n  }\r\n\r\n  &:not(:disabled):not(.disabled):active,\r\n  &:not(:disabled):not(.disabled).active,\r\n  .show > &.dropdown-toggle {\r\n    color: color-yiq($active-background);\r\n    background-color: $active-background;\r\n    border-color: $active-border;\r\n\r\n    &:focus {\r\n      // Avoid using mixin so we can pass custom focus shadow properly\r\n      @if $enable-shadows and $btn-active-box-shadow != none {\r\n        box-shadow: $btn-active-box-shadow, 0 0 0 $btn-focus-width rgba($color, .5);\r\n      } @else {\r\n        box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// Button sizes\r\n@mixin button-size($padding-y, $padding-x, $font-size, $line-height, $border-radius) {\r\n  padding: $padding-y $padding-x;\r\n  @include font-size($font-size);\r\n  line-height: $line-height;\r\n  // Manually declare to provide an override to the browser default\r\n  @include border-radius($border-radius, 0);\r\n}\r\n", ".fade {\r\n  @include transition($transition-fade);\r\n\r\n  &:not(.show) {\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n.collapse {\r\n  &:not(.show) {\r\n    display: none;\r\n  }\r\n}\r\n\r\n.collapsing {\r\n  position: relative;\r\n  height: 0;\r\n  overflow: hidden;\r\n  @include transition($transition-collapse);\r\n}\r\n", "// The dropdown wrapper (`<div>`)\r\n.dropup,\r\n.dropright,\r\n.dropdown,\r\n.dropleft {\r\n  position: relative;\r\n}\r\n\r\n.dropdown-toggle {\r\n  white-space: nowrap;\r\n\r\n  // Generate the caret automatically\r\n  @include caret;\r\n}\r\n\r\n// The dropdown menu\r\n.dropdown-menu {\r\n  position: absolute;\r\n  top: 100%;\r\n  left: 0;\r\n  z-index: $zindex-dropdown;\r\n  display: none; // none by default, but block on \"open\" of the menu\r\n  float: left;\r\n  min-width: $dropdown-min-width;\r\n  padding: $dropdown-padding-y 0;\r\n  margin: $dropdown-spacer 0 0; // override default ul\r\n  @include font-size($dropdown-font-size);\r\n  color: $dropdown-color;\r\n  text-align: left; // Ensures proper alignment if parent has it changed (e.g., modal footer)\r\n  list-style: none;\r\n  background-color: $dropdown-bg;\r\n  background-clip: padding-box;\r\n  border: $dropdown-border-width solid $dropdown-border-color;\r\n  @include border-radius($dropdown-border-radius);\r\n  @include box-shadow($dropdown-box-shadow);\r\n}\r\n\r\n@each $breakpoint in map-keys($grid-breakpoints) {\r\n  @include media-breakpoint-up($breakpoint) {\r\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\r\n\r\n    .dropdown-menu#{$infix}-left {\r\n      right: auto;\r\n      left: 0;\r\n    }\r\n\r\n    .dropdown-menu#{$infix}-right {\r\n      right: 0;\r\n      left: auto;\r\n    }\r\n  }\r\n}\r\n\r\n// Allow for dropdowns to go bottom up (aka, dropup-menu)\r\n// Just add .dropup after the standard .dropdown class and you're set.\r\n.dropup {\r\n  .dropdown-menu {\r\n    top: auto;\r\n    bottom: 100%;\r\n    margin-top: 0;\r\n    margin-bottom: $dropdown-spacer;\r\n  }\r\n\r\n  .dropdown-toggle {\r\n    @include caret(up);\r\n  }\r\n}\r\n\r\n.dropright {\r\n  .dropdown-menu {\r\n    top: 0;\r\n    right: auto;\r\n    left: 100%;\r\n    margin-top: 0;\r\n    margin-left: $dropdown-spacer;\r\n  }\r\n\r\n  .dropdown-toggle {\r\n    @include caret(right);\r\n    &::after {\r\n      vertical-align: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.dropleft {\r\n  .dropdown-menu {\r\n    top: 0;\r\n    right: 100%;\r\n    left: auto;\r\n    margin-top: 0;\r\n    margin-right: $dropdown-spacer;\r\n  }\r\n\r\n  .dropdown-toggle {\r\n    @include caret(left);\r\n    &::before {\r\n      vertical-align: 0;\r\n    }\r\n  }\r\n}\r\n\r\n// When enabled Popper.js, reset basic dropdown position\r\n// stylelint-disable-next-line no-duplicate-selectors\r\n.dropdown-menu {\r\n  &[x-placement^=\"top\"],\r\n  &[x-placement^=\"right\"],\r\n  &[x-placement^=\"bottom\"],\r\n  &[x-placement^=\"left\"] {\r\n    right: auto;\r\n    bottom: auto;\r\n  }\r\n}\r\n\r\n// Dividers (basically an `<hr>`) within the dropdown\r\n.dropdown-divider {\r\n  @include nav-divider($dropdown-divider-bg, $dropdown-divider-margin-y);\r\n}\r\n\r\n// Links, buttons, and more within the dropdown menu\r\n//\r\n// `<button>`-specific styles are denoted with `// For <button>s`\r\n.dropdown-item {\r\n  display: block;\r\n  width: 100%; // For `<button>`s\r\n  padding: $dropdown-item-padding-y $dropdown-item-padding-x;\r\n  clear: both;\r\n  font-weight: $font-weight-normal;\r\n  color: $dropdown-link-color;\r\n  text-align: inherit; // For `<button>`s\r\n  white-space: nowrap; // prevent links from randomly breaking onto new lines\r\n  background-color: transparent; // For `<button>`s\r\n  border: 0; // For `<button>`s\r\n\r\n  // Prevent dropdown overflow if there's no padding\r\n  // See https://github.com/twbs/bootstrap/pull/27703\r\n  @if $dropdown-padding-y == 0 {\r\n    &:first-child {\r\n      @include border-top-radius($dropdown-inner-border-radius);\r\n    }\r\n\r\n    &:last-child {\r\n      @include border-bottom-radius($dropdown-inner-border-radius);\r\n    }\r\n  }\r\n\r\n  @include hover-focus {\r\n    color: $dropdown-link-hover-color;\r\n    text-decoration: none;\r\n    @include gradient-bg($dropdown-link-hover-bg);\r\n  }\r\n\r\n  &.active,\r\n  &:active {\r\n    color: $dropdown-link-active-color;\r\n    text-decoration: none;\r\n    @include gradient-bg($dropdown-link-active-bg);\r\n  }\r\n\r\n  &.disabled,\r\n  &:disabled {\r\n    color: $dropdown-link-disabled-color;\r\n    pointer-events: none;\r\n    background-color: transparent;\r\n    // Remove CSS gradients if they're enabled\r\n    @if $enable-gradients {\r\n      background-image: none;\r\n    }\r\n  }\r\n}\r\n\r\n.dropdown-menu.show {\r\n  display: block;\r\n}\r\n\r\n// Dropdown section headers\r\n.dropdown-header {\r\n  display: block;\r\n  padding: $dropdown-padding-y $dropdown-item-padding-x;\r\n  margin-bottom: 0; // for use with heading elements\r\n  @include font-size($font-size-sm);\r\n  color: $dropdown-header-color;\r\n  white-space: nowrap; // as with > li > a\r\n}\r\n\r\n// Dropdown text\r\n.dropdown-item-text {\r\n  display: block;\r\n  padding: $dropdown-item-padding-y $dropdown-item-padding-x;\r\n  color: $dropdown-link-color;\r\n}\r\n", "@mixin caret-down {\r\n  border-top: $caret-width solid;\r\n  border-right: $caret-width solid transparent;\r\n  border-bottom: 0;\r\n  border-left: $caret-width solid transparent;\r\n}\r\n\r\n@mixin caret-up {\r\n  border-top: 0;\r\n  border-right: $caret-width solid transparent;\r\n  border-bottom: $caret-width solid;\r\n  border-left: $caret-width solid transparent;\r\n}\r\n\r\n@mixin caret-right {\r\n  border-top: $caret-width solid transparent;\r\n  border-right: 0;\r\n  border-bottom: $caret-width solid transparent;\r\n  border-left: $caret-width solid;\r\n}\r\n\r\n@mixin caret-left {\r\n  border-top: $caret-width solid transparent;\r\n  border-right: $caret-width solid;\r\n  border-bottom: $caret-width solid transparent;\r\n}\r\n\r\n@mixin caret($direction: down) {\r\n  @if $enable-caret {\r\n    &::after {\r\n      display: inline-block;\r\n      margin-left: $caret-spacing;\r\n      vertical-align: $caret-vertical-align;\r\n      content: \"\";\r\n      @if $direction == down {\r\n        @include caret-down;\r\n      } @else if $direction == up {\r\n        @include caret-up;\r\n      } @else if $direction == right {\r\n        @include caret-right;\r\n      }\r\n    }\r\n\r\n    @if $direction == left {\r\n      &::after {\r\n        display: none;\r\n      }\r\n\r\n      &::before {\r\n        display: inline-block;\r\n        margin-right: $caret-spacing;\r\n        vertical-align: $caret-vertical-align;\r\n        content: \"\";\r\n        @include caret-left;\r\n      }\r\n    }\r\n\r\n    &:empty::after {\r\n      margin-left: 0;\r\n    }\r\n  }\r\n}\r\n", "// Horizontal dividers\r\n//\r\n// Dividers (basically an hr) within dropdowns and nav lists\r\n\r\n@mixin nav-divider($color: $nav-divider-color, $margin-y: $nav-divider-margin-y) {\r\n  height: 0;\r\n  margin: $margin-y 0;\r\n  overflow: hidden;\r\n  border-top: 1px solid $color;\r\n}\r\n", "// stylelint-disable selector-no-qualifying-type\r\n\r\n// Make the div behave like a button\r\n.btn-group,\r\n.btn-group-vertical {\r\n  position: relative;\r\n  display: inline-flex;\r\n  vertical-align: middle; // match .btn alignment given font-size hack above\r\n\r\n  > .btn {\r\n    position: relative;\r\n    flex: 1 1 auto;\r\n\r\n    // Bring the hover, focused, and \"active\" buttons to the front to overlay\r\n    // the borders properly\r\n    @include hover {\r\n      z-index: 1;\r\n    }\r\n    &:focus,\r\n    &:active,\r\n    &.active {\r\n      z-index: 1;\r\n    }\r\n  }\r\n}\r\n\r\n// Optional: Group multiple button groups together for a toolbar\r\n.btn-toolbar {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: flex-start;\r\n\r\n  .input-group {\r\n    width: auto;\r\n  }\r\n}\r\n\r\n.btn-group {\r\n  // Prevent double borders when buttons are next to each other\r\n  > .btn:not(:first-child),\r\n  > .btn-group:not(:first-child) {\r\n    margin-left: -$btn-border-width;\r\n  }\r\n\r\n  // Reset rounded corners\r\n  > .btn:not(:last-child):not(.dropdown-toggle),\r\n  > .btn-group:not(:last-child) > .btn {\r\n    @include border-right-radius(0);\r\n  }\r\n\r\n  > .btn:not(:first-child),\r\n  > .btn-group:not(:first-child) > .btn {\r\n    @include border-left-radius(0);\r\n  }\r\n}\r\n\r\n// Sizing\r\n//\r\n// Remix the default button sizing classes into new ones for easier manipulation.\r\n\r\n.btn-group-sm > .btn { @extend .btn-sm; }\r\n.btn-group-lg > .btn { @extend .btn-lg; }\r\n\r\n\r\n//\r\n// Split button dropdowns\r\n//\r\n\r\n.dropdown-toggle-split {\r\n  padding-right: $btn-padding-x * .75;\r\n  padding-left: $btn-padding-x * .75;\r\n\r\n  &::after,\r\n  .dropup &::after,\r\n  .dropright &::after {\r\n    margin-left: 0;\r\n  }\r\n\r\n  .dropleft &::before {\r\n    margin-right: 0;\r\n  }\r\n}\r\n\r\n.btn-sm + .dropdown-toggle-split {\r\n  padding-right: $btn-padding-x-sm * .75;\r\n  padding-left: $btn-padding-x-sm * .75;\r\n}\r\n\r\n.btn-lg + .dropdown-toggle-split {\r\n  padding-right: $btn-padding-x-lg * .75;\r\n  padding-left: $btn-padding-x-lg * .75;\r\n}\r\n\r\n\r\n// The clickable button for toggling the menu\r\n// Set the same inset shadow as the :active state\r\n.btn-group.show .dropdown-toggle {\r\n  @include box-shadow($btn-active-box-shadow);\r\n\r\n  // Show no shadow for `.btn-link` since it has no other button styles.\r\n  &.btn-link {\r\n    @include box-shadow(none);\r\n  }\r\n}\r\n\r\n\r\n//\r\n// Vertical button groups\r\n//\r\n\r\n.btn-group-vertical {\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  justify-content: center;\r\n\r\n  > .btn,\r\n  > .btn-group {\r\n    width: 100%;\r\n  }\r\n\r\n  > .btn:not(:first-child),\r\n  > .btn-group:not(:first-child) {\r\n    margin-top: -$btn-border-width;\r\n  }\r\n\r\n  // Reset rounded corners\r\n  > .btn:not(:last-child):not(.dropdown-toggle),\r\n  > .btn-group:not(:last-child) > .btn {\r\n    @include border-bottom-radius(0);\r\n  }\r\n\r\n  > .btn:not(:first-child),\r\n  > .btn-group:not(:first-child) > .btn {\r\n    @include border-top-radius(0);\r\n  }\r\n}\r\n\r\n\r\n// Checkbox and radio options\r\n//\r\n// In order to support the browser's form validation feedback, powered by the\r\n// `required` attribute, we have to \"hide\" the inputs via `clip`. We cannot use\r\n// `display: none;` or `visibility: hidden;` as that also hides the popover.\r\n// Simply visually hiding the inputs via `opacity` would leave them clickable in\r\n// certain cases which is prevented by using `clip` and `pointer-events`.\r\n// This way, we ensure a DOM element is visible to position the popover from.\r\n//\r\n// See https://github.com/twbs/bootstrap/pull/12794 and\r\n// https://github.com/twbs/bootstrap/pull/14559 for more information.\r\n\r\n.btn-group-toggle {\r\n  > .btn,\r\n  > .btn-group > .btn {\r\n    margin-bottom: 0; // Override default `<label>` value\r\n\r\n    input[type=\"radio\"],\r\n    input[type=\"checkbox\"] {\r\n      position: absolute;\r\n      clip: rect(0, 0, 0, 0);\r\n      pointer-events: none;\r\n    }\r\n  }\r\n}\r\n", "// stylelint-disable selector-no-qualifying-type\r\n\r\n//\r\n// Base styles\r\n//\r\n\r\n.input-group {\r\n  position: relative;\r\n  display: flex;\r\n  flex-wrap: wrap; // For form validation feedback\r\n  align-items: stretch;\r\n  width: 100%;\r\n\r\n  > .form-control,\r\n  > .form-control-plaintext,\r\n  > .custom-select,\r\n  > .custom-file {\r\n    position: relative; // For focus state's z-index\r\n    flex: 1 1 auto;\r\n    // Add width 1% and flex-basis auto to ensure that button will not wrap out\r\n    // the column. Applies to IE Edge+ and Firefox. Chrome does not require this.\r\n    width: 1%;\r\n    margin-bottom: 0;\r\n\r\n    + .form-control,\r\n    + .custom-select,\r\n    + .custom-file {\r\n      margin-left: -$input-border-width;\r\n    }\r\n  }\r\n\r\n  // Bring the \"active\" form control to the top of surrounding elements\r\n  > .form-control:focus,\r\n  > .custom-select:focus,\r\n  > .custom-file .custom-file-input:focus ~ .custom-file-label {\r\n    z-index: 3;\r\n  }\r\n\r\n  // Bring the custom file input above the label\r\n  > .custom-file .custom-file-input:focus {\r\n    z-index: 4;\r\n  }\r\n\r\n  > .form-control,\r\n  > .custom-select {\r\n    &:not(:last-child) { @include border-right-radius(0); }\r\n    &:not(:first-child) { @include border-left-radius(0); }\r\n  }\r\n\r\n  // Custom file inputs have more complex markup, thus requiring different\r\n  // border-radius overrides.\r\n  > .custom-file {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    &:not(:last-child) .custom-file-label,\r\n    &:not(:last-child) .custom-file-label::after { @include border-right-radius(0); }\r\n    &:not(:first-child) .custom-file-label { @include border-left-radius(0); }\r\n  }\r\n}\r\n\r\n\r\n// Prepend and append\r\n//\r\n// While it requires one extra layer of HTML for each, dedicated prepend and\r\n// append elements allow us to 1) be less clever, 2) simplify our selectors, and\r\n// 3) support HTML5 form validation.\r\n\r\n.input-group-prepend,\r\n.input-group-append {\r\n  display: flex;\r\n\r\n  // Ensure buttons are always above inputs for more visually pleasing borders.\r\n  // This isn't needed for `.input-group-text` since it shares the same border-color\r\n  // as our inputs.\r\n  .btn {\r\n    position: relative;\r\n    z-index: 2;\r\n\r\n    &:focus {\r\n      z-index: 3;\r\n    }\r\n  }\r\n\r\n  .btn + .btn,\r\n  .btn + .input-group-text,\r\n  .input-group-text + .input-group-text,\r\n  .input-group-text + .btn {\r\n    margin-left: -$input-border-width;\r\n  }\r\n}\r\n\r\n.input-group-prepend { margin-right: -$input-border-width; }\r\n.input-group-append { margin-left: -$input-border-width; }\r\n\r\n\r\n// Textual addons\r\n//\r\n// Serves as a catch-all element for any text or radio/checkbox input you wish\r\n// to prepend or append to an input.\r\n\r\n.input-group-text {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: $input-padding-y $input-padding-x;\r\n  margin-bottom: 0; // Allow use of <label> elements by overriding our default margin-bottom\r\n  @include font-size($input-font-size); // Match inputs\r\n  font-weight: $font-weight-normal;\r\n  line-height: $input-line-height;\r\n  color: $input-group-addon-color;\r\n  text-align: center;\r\n  white-space: nowrap;\r\n  background-color: $input-group-addon-bg;\r\n  border: $input-border-width solid $input-group-addon-border-color;\r\n  @include border-radius($input-border-radius);\r\n\r\n  // Nuke default margins from checkboxes and radios to vertically center within.\r\n  input[type=\"radio\"],\r\n  input[type=\"checkbox\"] {\r\n    margin-top: 0;\r\n  }\r\n}\r\n\r\n\r\n// Sizing\r\n//\r\n// Remix the default form control sizing classes into new ones for easier\r\n// manipulation.\r\n\r\n.input-group-lg > .form-control:not(textarea),\r\n.input-group-lg > .custom-select {\r\n  height: $input-height-lg;\r\n}\r\n\r\n.input-group-lg > .form-control,\r\n.input-group-lg > .custom-select,\r\n.input-group-lg > .input-group-prepend > .input-group-text,\r\n.input-group-lg > .input-group-append > .input-group-text,\r\n.input-group-lg > .input-group-prepend > .btn,\r\n.input-group-lg > .input-group-append > .btn {\r\n  padding: $input-padding-y-lg $input-padding-x-lg;\r\n  @include font-size($input-font-size-lg);\r\n  line-height: $input-line-height-lg;\r\n  @include border-radius($input-border-radius-lg);\r\n}\r\n\r\n.input-group-sm > .form-control:not(textarea),\r\n.input-group-sm > .custom-select {\r\n  height: $input-height-sm;\r\n}\r\n\r\n.input-group-sm > .form-control,\r\n.input-group-sm > .custom-select,\r\n.input-group-sm > .input-group-prepend > .input-group-text,\r\n.input-group-sm > .input-group-append > .input-group-text,\r\n.input-group-sm > .input-group-prepend > .btn,\r\n.input-group-sm > .input-group-append > .btn {\r\n  padding: $input-padding-y-sm $input-padding-x-sm;\r\n  @include font-size($input-font-size-sm);\r\n  line-height: $input-line-height-sm;\r\n  @include border-radius($input-border-radius-sm);\r\n}\r\n\r\n.input-group-lg > .custom-select,\r\n.input-group-sm > .custom-select {\r\n  padding-right: $custom-select-padding-x + $custom-select-indicator-padding;\r\n}\r\n\r\n\r\n// Prepend and append rounded corners\r\n//\r\n// These rulesets must come after the sizing ones to properly override sm and lg\r\n// border-radius values when extending. They're more specific than we'd like\r\n// with the `.input-group >` part, but without it, we cannot override the sizing.\r\n\r\n\r\n.input-group > .input-group-prepend > .btn,\r\n.input-group > .input-group-prepend > .input-group-text,\r\n.input-group > .input-group-append:not(:last-child) > .btn,\r\n.input-group > .input-group-append:not(:last-child) > .input-group-text,\r\n.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),\r\n.input-group > .input-group-append:last-child > .input-group-text:not(:last-child) {\r\n  @include border-right-radius(0);\r\n}\r\n\r\n.input-group > .input-group-append > .btn,\r\n.input-group > .input-group-append > .input-group-text,\r\n.input-group > .input-group-prepend:not(:first-child) > .btn,\r\n.input-group > .input-group-prepend:not(:first-child) > .input-group-text,\r\n.input-group > .input-group-prepend:first-child > .btn:not(:first-child),\r\n.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child) {\r\n  @include border-left-radius(0);\r\n}\r\n", "// Embedded icons from Open Iconic.\r\n// Released under MIT and copyright 2014 Waybury.\r\n// https://useiconic.com/open\r\n\r\n\r\n// Checkboxes and radios\r\n//\r\n// Base class takes care of all the key behavioral aspects.\r\n\r\n.custom-control {\r\n  position: relative;\r\n  display: block;\r\n  min-height: $font-size-base * $line-height-base;\r\n  padding-left: $custom-control-gutter + $custom-control-indicator-size;\r\n}\r\n\r\n.custom-control-inline {\r\n  display: inline-flex;\r\n  margin-right: $custom-control-spacer-x;\r\n}\r\n\r\n.custom-control-input {\r\n  position: absolute;\r\n  z-index: -1; // Put the input behind the label so it doesn't overlay text\r\n  opacity: 0;\r\n\r\n  &:checked ~ .custom-control-label::before {\r\n    color: $custom-control-indicator-checked-color;\r\n    border-color: $custom-control-indicator-checked-border-color;\r\n    @include gradient-bg($custom-control-indicator-checked-bg);\r\n    @include box-shadow($custom-control-indicator-checked-box-shadow);\r\n  }\r\n\r\n  &:focus ~ .custom-control-label::before {\r\n    // the mixin is not used here to make sure there is feedback\r\n    @if $enable-shadows {\r\n      box-shadow: $input-box-shadow, $input-focus-box-shadow;\r\n    } @else {\r\n      box-shadow: $custom-control-indicator-focus-box-shadow;\r\n    }\r\n  }\r\n\r\n  &:focus:not(:checked) ~ .custom-control-label::before {\r\n    border-color: $custom-control-indicator-focus-border-color;\r\n  }\r\n\r\n  &:not(:disabled):active ~ .custom-control-label::before {\r\n    color: $custom-control-indicator-active-color;\r\n    background-color: $custom-control-indicator-active-bg;\r\n    border-color: $custom-control-indicator-active-border-color;\r\n    @include box-shadow($custom-control-indicator-active-box-shadow);\r\n  }\r\n\r\n  &:disabled {\r\n    ~ .custom-control-label {\r\n      color: $custom-control-label-disabled-color;\r\n\r\n      &::before {\r\n        background-color: $custom-control-indicator-disabled-bg;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// Custom control indicators\r\n//\r\n// Build the custom controls out of pseudo-elements.\r\n\r\n.custom-control-label {\r\n  position: relative;\r\n  margin-bottom: 0;\r\n  vertical-align: top;\r\n\r\n  // Background-color and (when enabled) gradient\r\n  &::before {\r\n    position: absolute;\r\n    top: ($font-size-base * $line-height-base - $custom-control-indicator-size) / 2;\r\n    left: -($custom-control-gutter + $custom-control-indicator-size);\r\n    display: block;\r\n    width: $custom-control-indicator-size;\r\n    height: $custom-control-indicator-size;\r\n    pointer-events: none;\r\n    content: \"\";\r\n    background-color: $custom-control-indicator-bg;\r\n    border: $custom-control-indicator-border-color solid $custom-control-indicator-border-width;\r\n    @include box-shadow($custom-control-indicator-box-shadow);\r\n  }\r\n\r\n  // Foreground (icon)\r\n  &::after {\r\n    position: absolute;\r\n    top: ($font-size-base * $line-height-base - $custom-control-indicator-size) / 2;\r\n    left: -($custom-control-gutter + $custom-control-indicator-size);\r\n    display: block;\r\n    width: $custom-control-indicator-size;\r\n    height: $custom-control-indicator-size;\r\n    content: \"\";\r\n    background: no-repeat 50% / #{$custom-control-indicator-bg-size};\r\n  }\r\n}\r\n\r\n\r\n// Checkboxes\r\n//\r\n// Tweak just a few things for checkboxes.\r\n\r\n.custom-checkbox {\r\n  .custom-control-label::before {\r\n    @include border-radius($custom-checkbox-indicator-border-radius);\r\n  }\r\n\r\n  .custom-control-input:checked ~ .custom-control-label {\r\n    &::after {\r\n      background-image: $custom-checkbox-indicator-icon-checked;\r\n    }\r\n  }\r\n\r\n  .custom-control-input:indeterminate ~ .custom-control-label {\r\n    &::before {\r\n      border-color: $custom-checkbox-indicator-indeterminate-border-color;\r\n      @include gradient-bg($custom-checkbox-indicator-indeterminate-bg);\r\n      @include box-shadow($custom-checkbox-indicator-indeterminate-box-shadow);\r\n    }\r\n    &::after {\r\n      background-image: $custom-checkbox-indicator-icon-indeterminate;\r\n    }\r\n  }\r\n\r\n  .custom-control-input:disabled {\r\n    &:checked ~ .custom-control-label::before {\r\n      background-color: $custom-control-indicator-checked-disabled-bg;\r\n    }\r\n    &:indeterminate ~ .custom-control-label::before {\r\n      background-color: $custom-control-indicator-checked-disabled-bg;\r\n    }\r\n  }\r\n}\r\n\r\n// Radios\r\n//\r\n// Tweak just a few things for radios.\r\n\r\n.custom-radio {\r\n  .custom-control-label::before {\r\n    // stylelint-disable-next-line property-blacklist\r\n    border-radius: $custom-radio-indicator-border-radius;\r\n  }\r\n\r\n  .custom-control-input:checked ~ .custom-control-label {\r\n    &::after {\r\n      background-image: $custom-radio-indicator-icon-checked;\r\n    }\r\n  }\r\n\r\n  .custom-control-input:disabled {\r\n    &:checked ~ .custom-control-label::before {\r\n      background-color: $custom-control-indicator-checked-disabled-bg;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// switches\r\n//\r\n// Tweak a few things for switches\r\n\r\n.custom-switch {\r\n  padding-left: $custom-switch-width + $custom-control-gutter;\r\n\r\n  .custom-control-label {\r\n    &::before {\r\n      left: -($custom-switch-width + $custom-control-gutter);\r\n      width: $custom-switch-width;\r\n      pointer-events: all;\r\n      // stylelint-disable-next-line property-blacklist\r\n      border-radius: $custom-switch-indicator-border-radius;\r\n    }\r\n\r\n    &::after {\r\n      top: calc(#{(($font-size-base * $line-height-base - $custom-control-indicator-size) / 2)} + #{$custom-control-indicator-border-width * 2});\r\n      left: calc(#{-($custom-switch-width + $custom-control-gutter)} + #{$custom-control-indicator-border-width * 2});\r\n      width: $custom-switch-indicator-size;\r\n      height: $custom-switch-indicator-size;\r\n      background-color: $custom-control-indicator-border-color;\r\n      // stylelint-disable-next-line property-blacklist\r\n      border-radius: $custom-switch-indicator-border-radius;\r\n      @include transition(transform .15s ease-in-out, $custom-forms-transition);\r\n    }\r\n  }\r\n\r\n  .custom-control-input:checked ~ .custom-control-label {\r\n    &::after {\r\n      background-color: $custom-control-indicator-bg;\r\n      transform: translateX($custom-switch-width - $custom-control-indicator-size);\r\n    }\r\n  }\r\n\r\n  .custom-control-input:disabled {\r\n    &:checked ~ .custom-control-label::before {\r\n      background-color: $custom-control-indicator-checked-disabled-bg;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Select\r\n//\r\n// Replaces the browser default select with a custom one, mostly pulled from\r\n// https://primer.github.io/.\r\n//\r\n\r\n.custom-select {\r\n  display: inline-block;\r\n  width: 100%;\r\n  height: $custom-select-height;\r\n  padding: $custom-select-padding-y ($custom-select-padding-x + $custom-select-indicator-padding) $custom-select-padding-y $custom-select-padding-x;\r\n  font-family: $custom-select-font-family;\r\n  @include font-size($custom-select-font-size);\r\n  font-weight: $custom-select-font-weight;\r\n  line-height: $custom-select-line-height;\r\n  color: $custom-select-color;\r\n  vertical-align: middle;\r\n  background: $custom-select-background;\r\n  background-color: $custom-select-bg;\r\n  border: $custom-select-border-width solid $custom-select-border-color;\r\n  @include border-radius($custom-select-border-radius, 0);\r\n  @include box-shadow($custom-select-box-shadow);\r\n  appearance: none;\r\n\r\n  &:focus {\r\n    border-color: $custom-select-focus-border-color;\r\n    outline: 0;\r\n    @if $enable-shadows {\r\n      box-shadow: $custom-select-box-shadow, $custom-select-focus-box-shadow;\r\n    } @else {\r\n      box-shadow: $custom-select-focus-box-shadow;\r\n    }\r\n\r\n    &::-ms-value {\r\n      // For visual consistency with other platforms/browsers,\r\n      // suppress the default white text on blue background highlight given to\r\n      // the selected option text when the (still closed) <select> receives focus\r\n      // in IE and (under certain conditions) Edge.\r\n      // See https://github.com/twbs/bootstrap/issues/19398.\r\n      color: $input-color;\r\n      background-color: $input-bg;\r\n    }\r\n  }\r\n\r\n  &[multiple],\r\n  &[size]:not([size=\"1\"]) {\r\n    height: auto;\r\n    padding-right: $custom-select-padding-x;\r\n    background-image: none;\r\n  }\r\n\r\n  &:disabled {\r\n    color: $custom-select-disabled-color;\r\n    background-color: $custom-select-disabled-bg;\r\n  }\r\n\r\n  // Hides the default caret in IE11\r\n  &::-ms-expand {\r\n    display: none;\r\n  }\r\n}\r\n\r\n.custom-select-sm {\r\n  height: $custom-select-height-sm;\r\n  padding-top: $custom-select-padding-y-sm;\r\n  padding-bottom: $custom-select-padding-y-sm;\r\n  padding-left: $custom-select-padding-x-sm;\r\n  @include font-size($custom-select-font-size-sm);\r\n}\r\n\r\n.custom-select-lg {\r\n  height: $custom-select-height-lg;\r\n  padding-top: $custom-select-padding-y-lg;\r\n  padding-bottom: $custom-select-padding-y-lg;\r\n  padding-left: $custom-select-padding-x-lg;\r\n  @include font-size($custom-select-font-size-lg);\r\n}\r\n\r\n\r\n// File\r\n//\r\n// Custom file input.\r\n\r\n.custom-file {\r\n  position: relative;\r\n  display: inline-block;\r\n  width: 100%;\r\n  height: $custom-file-height;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.custom-file-input {\r\n  position: relative;\r\n  z-index: 2;\r\n  width: 100%;\r\n  height: $custom-file-height;\r\n  margin: 0;\r\n  opacity: 0;\r\n\r\n  &:focus ~ .custom-file-label {\r\n    border-color: $custom-file-focus-border-color;\r\n    box-shadow: $custom-file-focus-box-shadow;\r\n  }\r\n\r\n  &:disabled ~ .custom-file-label {\r\n    background-color: $custom-file-disabled-bg;\r\n  }\r\n\r\n  @each $lang, $value in $custom-file-text {\r\n    &:lang(#{$lang}) ~ .custom-file-label::after {\r\n      content: $value;\r\n    }\r\n  }\r\n\r\n  ~ .custom-file-label[data-browse]::after {\r\n    content: attr(data-browse);\r\n  }\r\n}\r\n\r\n.custom-file-label {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  left: 0;\r\n  z-index: 1;\r\n  height: $custom-file-height;\r\n  padding: $custom-file-padding-y $custom-file-padding-x;\r\n  font-family: $custom-file-font-family;\r\n  font-weight: $custom-file-font-weight;\r\n  line-height: $custom-file-line-height;\r\n  color: $custom-file-color;\r\n  background-color: $custom-file-bg;\r\n  border: $custom-file-border-width solid $custom-file-border-color;\r\n  @include border-radius($custom-file-border-radius);\r\n  @include box-shadow($custom-file-box-shadow);\r\n\r\n  &::after {\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    z-index: 3;\r\n    display: block;\r\n    height: $custom-file-height-inner;\r\n    padding: $custom-file-padding-y $custom-file-padding-x;\r\n    line-height: $custom-file-line-height;\r\n    color: $custom-file-button-color;\r\n    content: \"Browse\";\r\n    @include gradient-bg($custom-file-button-bg);\r\n    border-left: inherit;\r\n    @include border-radius(0 $custom-file-border-radius $custom-file-border-radius 0);\r\n  }\r\n}\r\n\r\n// Range\r\n//\r\n// Style range inputs the same across browsers. Vendor-specific rules for pseudo\r\n// elements cannot be mixed. As such, there are no shared styles for focus or\r\n// active states on prefixed selectors.\r\n\r\n.custom-range {\r\n  width: 100%;\r\n  height: calc(#{$custom-range-thumb-height} + #{$custom-range-thumb-focus-box-shadow-width * 2});\r\n  padding: 0; // Need to reset padding\r\n  background-color: transparent;\r\n  appearance: none;\r\n\r\n  &:focus {\r\n    outline: none;\r\n\r\n    // Pseudo-elements must be split across multiple rulesets to have an effect.\r\n    // No box-shadow() mixin for focus accessibility.\r\n    &::-webkit-slider-thumb { box-shadow: $custom-range-thumb-focus-box-shadow; }\r\n    &::-moz-range-thumb     { box-shadow: $custom-range-thumb-focus-box-shadow; }\r\n    &::-ms-thumb            { box-shadow: $custom-range-thumb-focus-box-shadow; }\r\n  }\r\n\r\n  &::-moz-focus-outer {\r\n    border: 0;\r\n  }\r\n\r\n  &::-webkit-slider-thumb {\r\n    width: $custom-range-thumb-width;\r\n    height: $custom-range-thumb-height;\r\n    margin-top: ($custom-range-track-height - $custom-range-thumb-height) / 2; // Webkit specific\r\n    @include gradient-bg($custom-range-thumb-bg);\r\n    border: $custom-range-thumb-border;\r\n    @include border-radius($custom-range-thumb-border-radius);\r\n    @include box-shadow($custom-range-thumb-box-shadow);\r\n    @include transition($custom-forms-transition);\r\n    appearance: none;\r\n\r\n    &:active {\r\n      @include gradient-bg($custom-range-thumb-active-bg);\r\n    }\r\n  }\r\n\r\n  &::-webkit-slider-runnable-track {\r\n    width: $custom-range-track-width;\r\n    height: $custom-range-track-height;\r\n    color: transparent; // Why?\r\n    cursor: $custom-range-track-cursor;\r\n    background-color: $custom-range-track-bg;\r\n    border-color: transparent;\r\n    @include border-radius($custom-range-track-border-radius);\r\n    @include box-shadow($custom-range-track-box-shadow);\r\n  }\r\n\r\n  &::-moz-range-thumb {\r\n    width: $custom-range-thumb-width;\r\n    height: $custom-range-thumb-height;\r\n    @include gradient-bg($custom-range-thumb-bg);\r\n    border: $custom-range-thumb-border;\r\n    @include border-radius($custom-range-thumb-border-radius);\r\n    @include box-shadow($custom-range-thumb-box-shadow);\r\n    @include transition($custom-forms-transition);\r\n    appearance: none;\r\n\r\n    &:active {\r\n      @include gradient-bg($custom-range-thumb-active-bg);\r\n    }\r\n  }\r\n\r\n  &::-moz-range-track {\r\n    width: $custom-range-track-width;\r\n    height: $custom-range-track-height;\r\n    color: transparent;\r\n    cursor: $custom-range-track-cursor;\r\n    background-color: $custom-range-track-bg;\r\n    border-color: transparent; // Firefox specific?\r\n    @include border-radius($custom-range-track-border-radius);\r\n    @include box-shadow($custom-range-track-box-shadow);\r\n  }\r\n\r\n  &::-ms-thumb {\r\n    width: $custom-range-thumb-width;\r\n    height: $custom-range-thumb-height;\r\n    margin-top: 0; // Edge specific\r\n    margin-right: $custom-range-thumb-focus-box-shadow-width; // Workaround that overflowed box-shadow is hidden.\r\n    margin-left: $custom-range-thumb-focus-box-shadow-width;  // Workaround that overflowed box-shadow is hidden.\r\n    @include gradient-bg($custom-range-thumb-bg);\r\n    border: $custom-range-thumb-border;\r\n    @include border-radius($custom-range-thumb-border-radius);\r\n    @include box-shadow($custom-range-thumb-box-shadow);\r\n    @include transition($custom-forms-transition);\r\n    appearance: none;\r\n\r\n    &:active {\r\n      @include gradient-bg($custom-range-thumb-active-bg);\r\n    }\r\n  }\r\n\r\n  &::-ms-track {\r\n    width: $custom-range-track-width;\r\n    height: $custom-range-track-height;\r\n    color: transparent;\r\n    cursor: $custom-range-track-cursor;\r\n    background-color: transparent;\r\n    border-color: transparent;\r\n    border-width: $custom-range-thumb-height / 2;\r\n    @include box-shadow($custom-range-track-box-shadow);\r\n  }\r\n\r\n  &::-ms-fill-lower {\r\n    background-color: $custom-range-track-bg;\r\n    @include border-radius($custom-range-track-border-radius);\r\n  }\r\n\r\n  &::-ms-fill-upper {\r\n    margin-right: 15px; // arbitrary?\r\n    background-color: $custom-range-track-bg;\r\n    @include border-radius($custom-range-track-border-radius);\r\n  }\r\n\r\n  &:disabled {\r\n    &::-webkit-slider-thumb {\r\n      background-color: $custom-range-thumb-disabled-bg;\r\n    }\r\n\r\n    &::-webkit-slider-runnable-track {\r\n      cursor: default;\r\n    }\r\n\r\n    &::-moz-range-thumb {\r\n      background-color: $custom-range-thumb-disabled-bg;\r\n    }\r\n\r\n    &::-moz-range-track {\r\n      cursor: default;\r\n    }\r\n\r\n    &::-ms-thumb {\r\n      background-color: $custom-range-thumb-disabled-bg;\r\n    }\r\n  }\r\n}\r\n\r\n.custom-control-label::before,\r\n.custom-file-label,\r\n.custom-select {\r\n  @include transition($custom-forms-transition);\r\n}\r\n", "// Base class\r\n//\r\n// Kickstart any navigation component with a set of style resets. Works with\r\n// `<nav>`s or `<ul>`s.\r\n\r\n.nav {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  padding-left: 0;\r\n  margin-bottom: 0;\r\n  list-style: none;\r\n}\r\n\r\n.nav-link {\r\n  display: block;\r\n  padding: $nav-link-padding-y $nav-link-padding-x;\r\n\r\n  @include hover-focus {\r\n    text-decoration: none;\r\n  }\r\n\r\n  // Disabled state lightens text\r\n  &.disabled {\r\n    color: $nav-link-disabled-color;\r\n    pointer-events: none;\r\n    cursor: default;\r\n  }\r\n}\r\n\r\n//\r\n// Tabs\r\n//\r\n\r\n.nav-tabs {\r\n  border-bottom: $nav-tabs-border-width solid $nav-tabs-border-color;\r\n\r\n  .nav-item {\r\n    margin-bottom: -$nav-tabs-border-width;\r\n  }\r\n\r\n  .nav-link {\r\n    border: $nav-tabs-border-width solid transparent;\r\n    @include border-top-radius($nav-tabs-border-radius);\r\n\r\n    @include hover-focus {\r\n      border-color: $nav-tabs-link-hover-border-color;\r\n    }\r\n\r\n    &.disabled {\r\n      color: $nav-link-disabled-color;\r\n      background-color: transparent;\r\n      border-color: transparent;\r\n    }\r\n  }\r\n\r\n  .nav-link.active,\r\n  .nav-item.show .nav-link {\r\n    color: $nav-tabs-link-active-color;\r\n    background-color: $nav-tabs-link-active-bg;\r\n    border-color: $nav-tabs-link-active-border-color;\r\n  }\r\n\r\n  .dropdown-menu {\r\n    // Make dropdown border overlap tab border\r\n    margin-top: -$nav-tabs-border-width;\r\n    // Remove the top rounded corners here since there is a hard edge above the menu\r\n    @include border-top-radius(0);\r\n  }\r\n}\r\n\r\n\r\n//\r\n// Pills\r\n//\r\n\r\n.nav-pills {\r\n  .nav-link {\r\n    @include border-radius($nav-pills-border-radius);\r\n  }\r\n\r\n  .nav-link.active,\r\n  .show > .nav-link {\r\n    color: $nav-pills-link-active-color;\r\n    background-color: $nav-pills-link-active-bg;\r\n  }\r\n}\r\n\r\n\r\n//\r\n// Justified variants\r\n//\r\n\r\n.nav-fill {\r\n  .nav-item {\r\n    flex: 1 1 auto;\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n.nav-justified {\r\n  .nav-item {\r\n    flex-basis: 0;\r\n    flex-grow: 1;\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n\r\n// Tabbable tabs\r\n//\r\n// Hide tabbable panes to start, show them when `.active`\r\n\r\n.tab-content {\r\n  > .tab-pane {\r\n    display: none;\r\n  }\r\n  > .active {\r\n    display: block;\r\n  }\r\n}\r\n", "// Contents\r\n//\r\n// Navbar\r\n// Navbar brand\r\n// Navbar nav\r\n// Navbar text\r\n// Navbar divider\r\n// Responsive navbar\r\n// Navbar position\r\n// Navbar themes\r\n\r\n\r\n// Navbar\r\n//\r\n// Provide a static navbar from which we expand to create full-width, fixed, and\r\n// other navbar variations.\r\n\r\n.navbar {\r\n  position: relative;\r\n  display: flex;\r\n  flex-wrap: wrap; // allow us to do the line break for collapsing content\r\n  align-items: center;\r\n  justify-content: space-between; // space out brand from logo\r\n  padding: $navbar-padding-y $navbar-padding-x;\r\n\r\n  // Because flex properties aren't inherited, we need to redeclare these first\r\n  // few properties so that content nested within behave properly.\r\n  > .container,\r\n  > .container-fluid {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n  }\r\n}\r\n\r\n\r\n// Navbar brand\r\n//\r\n// Used for brand, project, or site names.\r\n\r\n.navbar-brand {\r\n  display: inline-block;\r\n  padding-top: $navbar-brand-padding-y;\r\n  padding-bottom: $navbar-brand-padding-y;\r\n  margin-right: $navbar-padding-x;\r\n  @include font-size($navbar-brand-font-size);\r\n  line-height: inherit;\r\n  white-space: nowrap;\r\n\r\n  @include hover-focus {\r\n    text-decoration: none;\r\n  }\r\n}\r\n\r\n\r\n// Navbar nav\r\n//\r\n// Custom navbar navigation (doesn't require `.nav`, but does make use of `.nav-link`).\r\n\r\n.navbar-nav {\r\n  display: flex;\r\n  flex-direction: column; // cannot use `inherit` to get the `.navbar`s value\r\n  padding-left: 0;\r\n  margin-bottom: 0;\r\n  list-style: none;\r\n\r\n  .nav-link {\r\n    padding-right: 0;\r\n    padding-left: 0;\r\n  }\r\n\r\n  .dropdown-menu {\r\n    position: static;\r\n    float: none;\r\n  }\r\n}\r\n\r\n\r\n// Navbar text\r\n//\r\n//\r\n\r\n.navbar-text {\r\n  display: inline-block;\r\n  padding-top: $nav-link-padding-y;\r\n  padding-bottom: $nav-link-padding-y;\r\n}\r\n\r\n\r\n// Responsive navbar\r\n//\r\n// Custom styles for responsive collapsing and toggling of navbar contents.\r\n// Powered by the collapse Bootstrap JavaScript plugin.\r\n\r\n// When collapsed, prevent the toggleable navbar contents from appearing in\r\n// the default flexbox row orientation. Requires the use of `flex-wrap: wrap`\r\n// on the `.navbar` parent.\r\n.navbar-collapse {\r\n  flex-basis: 100%;\r\n  flex-grow: 1;\r\n  // For always expanded or extra full navbars, ensure content aligns itself\r\n  // properly vertically. Can be easily overridden with flex utilities.\r\n  align-items: center;\r\n}\r\n\r\n// Button for toggling the navbar when in its collapsed state\r\n.navbar-toggler {\r\n  padding: $navbar-toggler-padding-y $navbar-toggler-padding-x;\r\n  @include font-size($navbar-toggler-font-size);\r\n  line-height: 1;\r\n  background-color: transparent; // remove default button style\r\n  border: $border-width solid transparent; // remove default button style\r\n  @include border-radius($navbar-toggler-border-radius);\r\n\r\n  @include hover-focus {\r\n    text-decoration: none;\r\n  }\r\n}\r\n\r\n// Keep as a separate element so folks can easily override it with another icon\r\n// or image file as needed.\r\n.navbar-toggler-icon {\r\n  display: inline-block;\r\n  width: 1.5em;\r\n  height: 1.5em;\r\n  vertical-align: middle;\r\n  content: \"\";\r\n  background: no-repeat center center;\r\n  background-size: 100% 100%;\r\n}\r\n\r\n// Generate series of `.navbar-expand-*` responsive classes for configuring\r\n// where your navbar collapses.\r\n.navbar-expand {\r\n  @each $breakpoint in map-keys($grid-breakpoints) {\r\n    $next: breakpoint-next($breakpoint, $grid-breakpoints);\r\n    $infix: breakpoint-infix($next, $grid-breakpoints);\r\n\r\n    &#{$infix} {\r\n      @include media-breakpoint-down($breakpoint) {\r\n        > .container,\r\n        > .container-fluid {\r\n          padding-right: 0;\r\n          padding-left: 0;\r\n        }\r\n      }\r\n\r\n      @include media-breakpoint-up($next) {\r\n        flex-flow: row nowrap;\r\n        justify-content: flex-start;\r\n\r\n        .navbar-nav {\r\n          flex-direction: row;\r\n\r\n          .dropdown-menu {\r\n            position: absolute;\r\n          }\r\n\r\n          .nav-link {\r\n            padding-right: $navbar-nav-link-padding-x;\r\n            padding-left: $navbar-nav-link-padding-x;\r\n          }\r\n        }\r\n\r\n        // For nesting containers, have to redeclare for alignment purposes\r\n        > .container,\r\n        > .container-fluid {\r\n          flex-wrap: nowrap;\r\n        }\r\n\r\n        .navbar-collapse {\r\n          display: flex !important; // stylelint-disable-line declaration-no-important\r\n\r\n          // Changes flex-bases to auto because of an IE10 bug\r\n          flex-basis: auto;\r\n        }\r\n\r\n        .navbar-toggler {\r\n          display: none;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Navbar themes\r\n//\r\n// Styles for switching between navbars with light or dark background.\r\n\r\n// Dark links against a light background\r\n.navbar-light {\r\n  .navbar-brand {\r\n    color: $navbar-light-brand-color;\r\n\r\n    @include hover-focus {\r\n      color: $navbar-light-brand-hover-color;\r\n    }\r\n  }\r\n\r\n  .navbar-nav {\r\n    .nav-link {\r\n      color: $navbar-light-color;\r\n\r\n      @include hover-focus {\r\n        color: $navbar-light-hover-color;\r\n      }\r\n\r\n      &.disabled {\r\n        color: $navbar-light-disabled-color;\r\n      }\r\n    }\r\n\r\n    .show > .nav-link,\r\n    .active > .nav-link,\r\n    .nav-link.show,\r\n    .nav-link.active {\r\n      color: $navbar-light-active-color;\r\n    }\r\n  }\r\n\r\n  .navbar-toggler {\r\n    color: $navbar-light-color;\r\n    border-color: $navbar-light-toggler-border-color;\r\n  }\r\n\r\n  .navbar-toggler-icon {\r\n    background-image: $navbar-light-toggler-icon-bg;\r\n  }\r\n\r\n  .navbar-text {\r\n    color: $navbar-light-color;\r\n    a {\r\n      color: $navbar-light-active-color;\r\n\r\n      @include hover-focus {\r\n        color: $navbar-light-active-color;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// White links against a dark background\r\n.navbar-dark {\r\n  .navbar-brand {\r\n    color: $navbar-dark-brand-color;\r\n\r\n    @include hover-focus {\r\n      color: $navbar-dark-brand-hover-color;\r\n    }\r\n  }\r\n\r\n  .navbar-nav {\r\n    .nav-link {\r\n      color: $navbar-dark-color;\r\n\r\n      @include hover-focus {\r\n        color: $navbar-dark-hover-color;\r\n      }\r\n\r\n      &.disabled {\r\n        color: $navbar-dark-disabled-color;\r\n      }\r\n    }\r\n\r\n    .show > .nav-link,\r\n    .active > .nav-link,\r\n    .nav-link.show,\r\n    .nav-link.active {\r\n      color: $navbar-dark-active-color;\r\n    }\r\n  }\r\n\r\n  .navbar-toggler {\r\n    color: $navbar-dark-color;\r\n    border-color: $navbar-dark-toggler-border-color;\r\n  }\r\n\r\n  .navbar-toggler-icon {\r\n    background-image: $navbar-dark-toggler-icon-bg;\r\n  }\r\n\r\n  .navbar-text {\r\n    color: $navbar-dark-color;\r\n    a {\r\n      color: $navbar-dark-active-color;\r\n\r\n      @include hover-focus {\r\n        color: $navbar-dark-active-color;\r\n      }\r\n    }\r\n  }\r\n}\r\n", "//\r\n// Base styles\r\n//\r\n\r\n.card {\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-width: 0; // See https://github.com/twbs/bootstrap/pull/22740#issuecomment-305868106\r\n  word-wrap: break-word;\r\n  background-color: $card-bg;\r\n  background-clip: border-box;\r\n  border: $card-border-width solid $card-border-color;\r\n  @include border-radius($card-border-radius);\r\n\r\n  > hr {\r\n    margin-right: 0;\r\n    margin-left: 0;\r\n  }\r\n\r\n  > .list-group:first-child {\r\n    .list-group-item:first-child {\r\n      @include border-top-radius($card-border-radius);\r\n    }\r\n  }\r\n\r\n  > .list-group:last-child {\r\n    .list-group-item:last-child {\r\n      @include border-bottom-radius($card-border-radius);\r\n    }\r\n  }\r\n}\r\n\r\n.card-body {\r\n  // Enable `flex-grow: 1` for decks and groups so that card blocks take up\r\n  // as much space as possible, ensuring footers are aligned to the bottom.\r\n  flex: 1 1 auto;\r\n  padding: $card-spacer-x;\r\n  color: $card-color;\r\n}\r\n\r\n.card-title {\r\n  margin-bottom: $card-spacer-y;\r\n}\r\n\r\n.card-subtitle {\r\n  margin-top: -$card-spacer-y / 2;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.card-text:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.card-link {\r\n  @include hover {\r\n    text-decoration: none;\r\n  }\r\n\r\n  + .card-link {\r\n    margin-left: $card-spacer-x;\r\n  }\r\n}\r\n\r\n//\r\n// Optional textual caps\r\n//\r\n\r\n.card-header {\r\n  padding: $card-spacer-y $card-spacer-x;\r\n  margin-bottom: 0; // Removes the default margin-bottom of <hN>\r\n  color: $card-cap-color;\r\n  background-color: $card-cap-bg;\r\n  border-bottom: $card-border-width solid $card-border-color;\r\n\r\n  &:first-child {\r\n    @include border-radius($card-inner-border-radius $card-inner-border-radius 0 0);\r\n  }\r\n\r\n  + .list-group {\r\n    .list-group-item:first-child {\r\n      border-top: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.card-footer {\r\n  padding: $card-spacer-y $card-spacer-x;\r\n  background-color: $card-cap-bg;\r\n  border-top: $card-border-width solid $card-border-color;\r\n\r\n  &:last-child {\r\n    @include border-radius(0 0 $card-inner-border-radius $card-inner-border-radius);\r\n  }\r\n}\r\n\r\n\r\n//\r\n// Header navs\r\n//\r\n\r\n.card-header-tabs {\r\n  margin-right: -$card-spacer-x / 2;\r\n  margin-bottom: -$card-spacer-y;\r\n  margin-left: -$card-spacer-x / 2;\r\n  border-bottom: 0;\r\n}\r\n\r\n.card-header-pills {\r\n  margin-right: -$card-spacer-x / 2;\r\n  margin-left: -$card-spacer-x / 2;\r\n}\r\n\r\n// Card image\r\n.card-img-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n  padding: $card-img-overlay-padding;\r\n}\r\n\r\n.card-img {\r\n  width: 100%; // Required because we use flexbox and this inherently applies align-self: stretch\r\n  @include border-radius($card-inner-border-radius);\r\n}\r\n\r\n// Card image caps\r\n.card-img-top {\r\n  width: 100%; // Required because we use flexbox and this inherently applies align-self: stretch\r\n  @include border-top-radius($card-inner-border-radius);\r\n}\r\n\r\n.card-img-bottom {\r\n  width: 100%; // Required because we use flexbox and this inherently applies align-self: stretch\r\n  @include border-bottom-radius($card-inner-border-radius);\r\n}\r\n\r\n\r\n// Card deck\r\n\r\n.card-deck {\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .card {\r\n    margin-bottom: $card-deck-margin;\r\n  }\r\n\r\n  @include media-breakpoint-up(sm) {\r\n    flex-flow: row wrap;\r\n    margin-right: -$card-deck-margin;\r\n    margin-left: -$card-deck-margin;\r\n\r\n    .card {\r\n      display: flex;\r\n      // Flexbugs #4: https://github.com/philipwalton/flexbugs#flexbug-4\r\n      flex: 1 0 0%;\r\n      flex-direction: column;\r\n      margin-right: $card-deck-margin;\r\n      margin-bottom: 0; // Override the default\r\n      margin-left: $card-deck-margin;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n//\r\n// Card groups\r\n//\r\n\r\n.card-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  // The child selector allows nested `.card` within `.card-group`\r\n  // to display properly.\r\n  > .card {\r\n    margin-bottom: $card-group-margin;\r\n  }\r\n\r\n  @include media-breakpoint-up(sm) {\r\n    flex-flow: row wrap;\r\n    // The child selector allows nested `.card` within `.card-group`\r\n    // to display properly.\r\n    > .card {\r\n      // Flexbugs #4: https://github.com/philipwalton/flexbugs#flexbug-4\r\n      flex: 1 0 0%;\r\n      margin-bottom: 0;\r\n\r\n      + .card {\r\n        margin-left: 0;\r\n        border-left: 0;\r\n      }\r\n\r\n      // Handle rounded corners\r\n      @if $enable-rounded {\r\n        &:not(:last-child) {\r\n          @include border-right-radius(0);\r\n\r\n          .card-img-top,\r\n          .card-header {\r\n            // stylelint-disable-next-line property-blacklist\r\n            border-top-right-radius: 0;\r\n          }\r\n          .card-img-bottom,\r\n          .card-footer {\r\n            // stylelint-disable-next-line property-blacklist\r\n            border-bottom-right-radius: 0;\r\n          }\r\n        }\r\n\r\n        &:not(:first-child) {\r\n          @include border-left-radius(0);\r\n\r\n          .card-img-top,\r\n          .card-header {\r\n            // stylelint-disable-next-line property-blacklist\r\n            border-top-left-radius: 0;\r\n          }\r\n          .card-img-bottom,\r\n          .card-footer {\r\n            // stylelint-disable-next-line property-blacklist\r\n            border-bottom-left-radius: 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n//\r\n// Columns\r\n//\r\n\r\n.card-columns {\r\n  .card {\r\n    margin-bottom: $card-columns-margin;\r\n  }\r\n\r\n  @include media-breakpoint-up(sm) {\r\n    column-count: $card-columns-count;\r\n    column-gap: $card-columns-gap;\r\n    orphans: 1;\r\n    widows: 1;\r\n\r\n    .card {\r\n      display: inline-block; // Don't let them vertically span multiple columns\r\n      width: 100%; // Don't let their width change\r\n    }\r\n  }\r\n}\r\n\r\n\r\n//\r\n// Accordion\r\n//\r\n\r\n.accordion {\r\n  > .card {\r\n    overflow: hidden;\r\n\r\n    &:not(:first-of-type) {\r\n      .card-header:first-child {\r\n        @include border-radius(0);\r\n      }\r\n\r\n      &:not(:last-of-type) {\r\n        border-bottom: 0;\r\n        @include border-radius(0);\r\n      }\r\n    }\r\n\r\n    &:first-of-type {\r\n      border-bottom: 0;\r\n      @include border-bottom-radius(0);\r\n    }\r\n\r\n    &:last-of-type {\r\n      @include border-top-radius(0);\r\n    }\r\n\r\n    .card-header {\r\n      margin-bottom: -$card-border-width;\r\n    }\r\n  }\r\n}\r\n", ".breadcrumb {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  padding: $breadcrumb-padding-y $breadcrumb-padding-x;\r\n  margin-bottom: $breadcrumb-margin-bottom;\r\n  list-style: none;\r\n  background-color: $breadcrumb-bg;\r\n  @include border-radius($breadcrumb-border-radius);\r\n}\r\n\r\n.breadcrumb-item {\r\n  // The separator between breadcrumbs (by default, a forward-slash: \"/\")\r\n  + .breadcrumb-item {\r\n    padding-left: $breadcrumb-item-padding;\r\n\r\n    &::before {\r\n      display: inline-block; // Suppress underlining of the separator in modern browsers\r\n      padding-right: $breadcrumb-item-padding;\r\n      color: $breadcrumb-divider-color;\r\n      content: $breadcrumb-divider;\r\n    }\r\n  }\r\n\r\n  // IE9-11 hack to properly handle hyperlink underlines for breadcrumbs built\r\n  // without `<ul>`s. The `::before` pseudo-element generates an element\r\n  // *within* the .breadcrumb-item and thereby inherits the `text-decoration`.\r\n  //\r\n  // To trick IE into suppressing the underline, we give the pseudo-element an\r\n  // underline and then immediately remove it.\r\n  + .breadcrumb-item:hover::before {\r\n    text-decoration: underline;\r\n  }\r\n  // stylelint-disable-next-line no-duplicate-selectors\r\n  + .breadcrumb-item:hover::before {\r\n    text-decoration: none;\r\n  }\r\n\r\n  &.active {\r\n    color: $breadcrumb-active-color;\r\n  }\r\n}", ".pagination {\r\n  display: flex;\r\n  @include list-unstyled();\r\n  @include border-radius();\r\n}\r\n\r\n.page-link {\r\n  position: relative;\r\n  display: block;\r\n  padding: $pagination-padding-y $pagination-padding-x;\r\n  margin-left: -$pagination-border-width;\r\n  line-height: $pagination-line-height;\r\n  color: $pagination-color;\r\n  background-color: $pagination-bg;\r\n  border: $pagination-border-width solid $pagination-border-color;\r\n\r\n  &:hover {\r\n    z-index: 2;\r\n    color: $pagination-hover-color;\r\n    text-decoration: none;\r\n    background-color: $pagination-hover-bg;\r\n    border-color: $pagination-hover-border-color;\r\n  }\r\n\r\n  &:focus {\r\n    z-index: 2;\r\n    outline: $pagination-focus-outline;\r\n    box-shadow: $pagination-focus-box-shadow;\r\n  }\r\n}\r\n\r\n.page-item {\r\n  &:first-child {\r\n    .page-link {\r\n      margin-left: 0;\r\n      @include border-left-radius($border-radius);\r\n    }\r\n  }\r\n  &:last-child {\r\n    .page-link {\r\n      @include border-right-radius($border-radius);\r\n    }\r\n  }\r\n\r\n  &.active .page-link {\r\n    z-index: 1;\r\n    color: $pagination-active-color;\r\n    background-color: $pagination-active-bg;\r\n    border-color: $pagination-active-border-color;\r\n  }\r\n\r\n  &.disabled .page-link {\r\n    color: $pagination-disabled-color;\r\n    pointer-events: none;\r\n    // Opinionated: remove the \"hand\" cursor set previously for .page-link\r\n    cursor: auto;\r\n    background-color: $pagination-disabled-bg;\r\n    border-color: $pagination-disabled-border-color;\r\n  }\r\n}\r\n\r\n\r\n//\r\n// Sizing\r\n//\r\n\r\n.pagination-lg {\r\n  @include pagination-size($pagination-padding-y-lg, $pagination-padding-x-lg, $font-size-lg, $line-height-lg, $border-radius-lg);\r\n}\r\n\r\n.pagination-sm {\r\n  @include pagination-size($pagination-padding-y-sm, $pagination-padding-x-sm, $font-size-sm, $line-height-sm, $border-radius-sm);\r\n}\r\n", "// Pagination\r\n\r\n@mixin pagination-size($padding-y, $padding-x, $font-size, $line-height, $border-radius) {\r\n  .page-link {\r\n    padding: $padding-y $padding-x;\r\n    @include font-size($font-size);\r\n    line-height: $line-height;\r\n  }\r\n\r\n  .page-item {\r\n    &:first-child {\r\n      .page-link {\r\n        @include border-left-radius($border-radius);\r\n      }\r\n    }\r\n    &:last-child {\r\n      .page-link {\r\n        @include border-right-radius($border-radius);\r\n      }\r\n    }\r\n  }\r\n}\r\n", "// Base class\r\n//\r\n// Requires one of the contextual, color modifier classes for `color` and\r\n// `background-color`.\r\n\r\n.badge {\r\n  display: inline-block;\r\n  padding: $badge-padding-y $badge-padding-x;\r\n  @include font-size($badge-font-size);\r\n  font-weight: $badge-font-weight;\r\n  line-height: 1;\r\n  text-align: center;\r\n  white-space: nowrap;\r\n  vertical-align: baseline;\r\n  @include border-radius($badge-border-radius);\r\n  @include transition($badge-transition);\r\n\r\n  @at-root a#{&} {\r\n    @include hover-focus {\r\n      text-decoration: none;\r\n    }\r\n  }\r\n\r\n  // Empty badges collapse automatically\r\n  &:empty {\r\n    display: none;\r\n  }\r\n}\r\n\r\n// Quick fix for badges in buttons\r\n.btn .badge {\r\n  position: relative;\r\n  top: -1px;\r\n}\r\n\r\n// Pill badges\r\n//\r\n// Make them extra rounded with a modifier to replace v3's badges.\r\n\r\n.badge-pill {\r\n  padding-right: $badge-pill-padding-x;\r\n  padding-left: $badge-pill-padding-x;\r\n  @include border-radius($badge-pill-border-radius);\r\n}\r\n\r\n// Colors\r\n//\r\n// Contextual variations (linked badges get darker on :hover).\r\n\r\n@each $color, $value in $theme-colors {\r\n  .badge-#{$color} {\r\n    @include badge-variant($value);\r\n  }\r\n}\r\n", "@mixin badge-variant($bg) {\r\n  color: color-yiq($bg);\r\n  background-color: $bg;\r\n\r\n  @at-root a#{&} {\r\n    @include hover-focus {\r\n      color: color-yiq($bg);\r\n      background-color: darken($bg, 10%);\r\n    }\r\n\r\n    &:focus,\r\n    &.focus {\r\n      outline: 0;\r\n      box-shadow: 0 0 0 $badge-focus-width rgba($bg, .5);\r\n    }\r\n  }\r\n}\r\n", ".jumbotron {\r\n  padding: $jumbotron-padding ($jumbotron-padding / 2);\r\n  margin-bottom: $jumbotron-padding;\r\n  color: $jumbotron-color;\r\n  background-color: $jumbotron-bg;\r\n  @include border-radius($border-radius-lg);\r\n\r\n  @include media-breakpoint-up(sm) {\r\n    padding: ($jumbotron-padding * 2) $jumbotron-padding;\r\n  }\r\n}\r\n\r\n.jumbotron-fluid {\r\n  padding-right: 0;\r\n  padding-left: 0;\r\n  @include border-radius(0);\r\n}\r\n", "//\r\n// Base styles\r\n//\r\n\r\n.alert {\r\n  position: relative;\r\n  padding: $alert-padding-y $alert-padding-x;\r\n  margin-bottom: $alert-margin-bottom;\r\n  border: $alert-border-width solid transparent;\r\n  @include border-radius($alert-border-radius);\r\n}\r\n\r\n// Headings for larger alerts\r\n.alert-heading {\r\n  // Specified to prevent conflicts of changing $headings-color\r\n  color: inherit;\r\n}\r\n\r\n// Provide class for links that match alerts\r\n.alert-link {\r\n  font-weight: $alert-link-font-weight;\r\n}\r\n\r\n\r\n// Dismissible alerts\r\n//\r\n// Expand the right padding and account for the close button's positioning.\r\n\r\n.alert-dismissible {\r\n  padding-right: $close-font-size + $alert-padding-x * 2;\r\n\r\n  // Adjust close link position\r\n  .close {\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    padding: $alert-padding-y $alert-padding-x;\r\n    color: inherit;\r\n  }\r\n}\r\n\r\n\r\n// Alternate styles\r\n//\r\n// Generate contextual modifier classes for colorizing the alert.\r\n\r\n@each $color, $value in $theme-colors {\r\n  .alert-#{$color} {\r\n    @include alert-variant(theme-color-level($color, $alert-bg-level), theme-color-level($color, $alert-border-level), theme-color-level($color, $alert-color-level));\r\n  }\r\n}\r\n", "@mixin alert-variant($background, $border, $color) {\r\n  color: $color;\r\n  @include gradient-bg($background);\r\n  border-color: $border;\r\n\r\n  hr {\r\n    border-top-color: darken($border, 5%);\r\n  }\r\n\r\n  .alert-link {\r\n    color: darken($color, 10%);\r\n  }\r\n}\r\n", "// Disable animation if transitions are disabled\r\n@if $enable-transitions {\r\n  @keyframes progress-bar-stripes {\r\n    from { background-position: $progress-height 0; }\r\n    to { background-position: 0 0; }\r\n  }\r\n}\r\n\r\n.progress {\r\n  display: flex;\r\n  height: $progress-height;\r\n  overflow: hidden; // force rounded corners by cropping it\r\n  @include font-size($progress-font-size);\r\n  background-color: $progress-bg;\r\n  @include border-radius($progress-border-radius);\r\n  @include box-shadow($progress-box-shadow);\r\n}\r\n\r\n.progress-bar {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  color: $progress-bar-color;\r\n  text-align: center;\r\n  white-space: nowrap;\r\n  background-color: $progress-bar-bg;\r\n  @include transition($progress-bar-transition);\r\n}\r\n\r\n.progress-bar-striped {\r\n  @include gradient-striped();\r\n  background-size: $progress-height $progress-height;\r\n}\r\n\r\n@if $enable-transitions {\r\n  .progress-bar-animated {\r\n    animation: progress-bar-stripes $progress-bar-animation-timing;\r\n\r\n    @media (prefers-reduced-motion: reduce) {\r\n      animation: none;\r\n    }\r\n  }\r\n}\r\n", ".media {\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.media-body {\r\n  flex: 1;\r\n}\r\n", "// Base class\r\n//\r\n// Easily usable on <ul>, <ol>, or <div>.\r\n\r\n.list-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  // No need to set list-style: none; since .list-group-item is block level\r\n  padding-left: 0; // reset padding because ul and ol\r\n  margin-bottom: 0;\r\n}\r\n\r\n\r\n// Interactive list items\r\n//\r\n// Use anchor or button elements instead of `li`s or `div`s to create interactive\r\n// list items. Includes an extra `.active` modifier class for selected items.\r\n\r\n.list-group-item-action {\r\n  width: 100%; // For `<button>`s (anchors become 100% by default though)\r\n  color: $list-group-action-color;\r\n  text-align: inherit; // For `<button>`s (anchors inherit)\r\n\r\n  // Hover state\r\n  @include hover-focus {\r\n    z-index: 1; // Place hover/focus items above their siblings for proper border styling\r\n    color: $list-group-action-hover-color;\r\n    text-decoration: none;\r\n    background-color: $list-group-hover-bg;\r\n  }\r\n\r\n  &:active {\r\n    color: $list-group-action-active-color;\r\n    background-color: $list-group-action-active-bg;\r\n  }\r\n}\r\n\r\n\r\n// Individual list items\r\n//\r\n// Use on `li`s or `div`s within the `.list-group` parent.\r\n\r\n.list-group-item {\r\n  position: relative;\r\n  display: block;\r\n  padding: $list-group-item-padding-y $list-group-item-padding-x;\r\n  // Place the border on the list items and negative margin up for better styling\r\n  margin-bottom: -$list-group-border-width;\r\n  color: $list-group-color;\r\n  background-color: $list-group-bg;\r\n  border: $list-group-border-width solid $list-group-border-color;\r\n\r\n  &:first-child {\r\n    @include border-top-radius($list-group-border-radius);\r\n  }\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n    @include border-bottom-radius($list-group-border-radius);\r\n  }\r\n\r\n  &.disabled,\r\n  &:disabled {\r\n    color: $list-group-disabled-color;\r\n    pointer-events: none;\r\n    background-color: $list-group-disabled-bg;\r\n  }\r\n\r\n  // Include both here for `<a>`s and `<button>`s\r\n  &.active {\r\n    z-index: 2; // Place active items above their siblings for proper border styling\r\n    color: $list-group-active-color;\r\n    background-color: $list-group-active-bg;\r\n    border-color: $list-group-active-border-color;\r\n  }\r\n}\r\n\r\n\r\n// Horizontal\r\n//\r\n// Change the layout of list group items from vertical (default) to horizontal.\r\n\r\n@each $breakpoint in map-keys($grid-breakpoints) {\r\n  @include media-breakpoint-up($breakpoint) {\r\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\r\n\r\n    .list-group-horizontal#{$infix} {\r\n      flex-direction: row;\r\n\r\n      .list-group-item {\r\n        margin-right: -$list-group-border-width;\r\n        margin-bottom: 0;\r\n\r\n        &:first-child {\r\n          @include border-left-radius($list-group-border-radius);\r\n          @include border-top-right-radius(0);\r\n        }\r\n\r\n        &:last-child {\r\n          margin-right: 0;\r\n          @include border-right-radius($list-group-border-radius);\r\n          @include border-bottom-left-radius(0);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Flush list items\r\n//\r\n// Remove borders and border-radius to keep list group items edge-to-edge. Most\r\n// useful within other components (e.g., cards).\r\n\r\n.list-group-flush {\r\n  .list-group-item {\r\n    border-right: 0;\r\n    border-left: 0;\r\n    @include border-radius(0);\r\n\r\n    &:last-child {\r\n      margin-bottom: -$list-group-border-width;\r\n    }\r\n  }\r\n\r\n  &:first-child {\r\n    .list-group-item:first-child {\r\n      border-top: 0;\r\n    }\r\n  }\r\n\r\n  &:last-child {\r\n    .list-group-item:last-child {\r\n      margin-bottom: 0;\r\n      border-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Contextual variants\r\n//\r\n// Add modifier classes to change text and background color on individual items.\r\n// Organizationally, this must come after the `:hover` states.\r\n\r\n@each $color, $value in $theme-colors {\r\n  @include list-group-item-variant($color, theme-color-level($color, -9), theme-color-level($color, 6));\r\n}\r\n", "// List Groups\r\n\r\n@mixin list-group-item-variant($state, $background, $color) {\r\n  .list-group-item-#{$state} {\r\n    color: $color;\r\n    background-color: $background;\r\n\r\n    &.list-group-item-action {\r\n      @include hover-focus {\r\n        color: $color;\r\n        background-color: darken($background, 5%);\r\n      }\r\n\r\n      &.active {\r\n        color: $white;\r\n        background-color: $color;\r\n        border-color: $color;\r\n      }\r\n    }\r\n  }\r\n}\r\n", ".close {\r\n  float: right;\r\n  @include font-size($close-font-size);\r\n  font-weight: $close-font-weight;\r\n  line-height: 1;\r\n  color: $close-color;\r\n  text-shadow: $close-text-shadow;\r\n  opacity: .5;\r\n\r\n  // Override <a>'s hover style\r\n  @include hover {\r\n    color: $close-color;\r\n    text-decoration: none;\r\n  }\r\n\r\n  &:not(:disabled):not(.disabled) {\r\n    @include hover-focus {\r\n      opacity: .75;\r\n    }\r\n  }\r\n}\r\n\r\n// Additional properties for button version\r\n// iOS requires the button element instead of an anchor tag.\r\n// If you want the anchor version, it requires `href=\"#\"`.\r\n// See https://developer.mozilla.org/en-US/docs/Web/Events/click#Safari_Mobile\r\n\r\n// stylelint-disable-next-line selector-no-qualifying-type\r\nbutton.close {\r\n  padding: 0;\r\n  background-color: transparent;\r\n  border: 0;\r\n  appearance: none;\r\n}\r\n\r\n// Future-proof disabling of clicks on `<a>` elements\r\n\r\n// stylelint-disable-next-line selector-no-qualifying-type\r\na.close.disabled {\r\n  pointer-events: none;\r\n}\r\n", ".toast {\r\n  max-width: $toast-max-width;\r\n  overflow: hidden; // cheap rounded corners on nested items\r\n  @include font-size($toast-font-size);\r\n  color: $toast-color;\r\n  background-color: $toast-background-color;\r\n  background-clip: padding-box;\r\n  border: $toast-border-width solid $toast-border-color;\r\n  box-shadow: $toast-box-shadow;\r\n  backdrop-filter: blur(10px);\r\n  opacity: 0;\r\n  @include border-radius($toast-border-radius);\r\n\r\n  &:not(:last-child) {\r\n    margin-bottom: $toast-padding-x;\r\n  }\r\n\r\n  &.showing {\r\n    opacity: 1;\r\n  }\r\n\r\n  &.show {\r\n    display: block;\r\n    opacity: 1;\r\n  }\r\n\r\n  &.hide {\r\n    display: none;\r\n  }\r\n}\r\n\r\n.toast-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: $toast-padding-y $toast-padding-x;\r\n  color: $toast-header-color;\r\n  background-color: $toast-header-background-color;\r\n  background-clip: padding-box;\r\n  border-bottom: $toast-border-width solid $toast-header-border-color;\r\n}\r\n\r\n.toast-body {\r\n  padding: $toast-padding-x; // apply to both vertical and horizontal\r\n}\r\n", "// .modal-open      - body class for killing the scroll\r\n// .modal           - container to scroll within\r\n// .modal-dialog    - positioning shell for the actual modal\r\n// .modal-content   - actual modal w/ bg and corners and stuff\r\n\r\n\r\n.modal-open {\r\n  // Kill the scroll on the body\r\n  overflow: hidden;\r\n\r\n  .modal {\r\n    overflow-x: hidden;\r\n    overflow-y: auto;\r\n  }\r\n}\r\n\r\n// Container that the modal scrolls within\r\n.modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  z-index: $zindex-modal;\r\n  display: none;\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: hidden;\r\n  // Prevent Chrome on Windows from adding a focus outline. For details, see\r\n  // https://github.com/twbs/bootstrap/pull/10951.\r\n  outline: 0;\r\n  // We deliberately don't use `-webkit-overflow-scrolling: touch;` due to a\r\n  // gnarly iOS Safari bug: https://bugs.webkit.org/show_bug.cgi?id=158342\r\n  // See also https://github.com/twbs/bootstrap/issues/17695\r\n}\r\n\r\n// Shell div to position the modal with bottom padding\r\n.modal-dialog {\r\n  position: relative;\r\n  width: auto;\r\n  margin: $modal-dialog-margin;\r\n  // allow clicks to pass through for custom click handling to close modal\r\n  pointer-events: none;\r\n\r\n  // When fading in the modal, animate it to slide down\r\n  .modal.fade & {\r\n    @include transition($modal-transition);\r\n    transform: $modal-fade-transform;\r\n  }\r\n  .modal.show & {\r\n    transform: $modal-show-transform;\r\n  }\r\n}\r\n\r\n.modal-dialog-scrollable {\r\n  display: flex; // IE10/11\r\n  max-height: calc(100% - #{$modal-dialog-margin * 2});\r\n\r\n  .modal-content {\r\n    max-height: calc(100vh - #{$modal-dialog-margin * 2}); // IE10/11\r\n    overflow: hidden;\r\n  }\r\n\r\n  .modal-header,\r\n  .modal-footer {\r\n    flex-shrink: 0;\r\n  }\r\n\r\n  .modal-body {\r\n    overflow-y: auto;\r\n  }\r\n}\r\n\r\n.modal-dialog-centered {\r\n  display: flex;\r\n  align-items: center;\r\n  min-height: calc(100% - #{$modal-dialog-margin * 2});\r\n\r\n  // Ensure `modal-dialog-centered` extends the full height of the view (IE10/11)\r\n  &::before {\r\n    display: block; // IE10\r\n    height: calc(100vh - #{$modal-dialog-margin * 2});\r\n    content: \"\";\r\n  }\r\n\r\n  // Ensure `.modal-body` shows scrollbar (IE10/11)\r\n  &.modal-dialog-scrollable {\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    height: 100%;\r\n\r\n    .modal-content {\r\n      max-height: none;\r\n    }\r\n\r\n    &::before {\r\n      content: none;\r\n    }\r\n  }\r\n}\r\n\r\n// Actual modal\r\n.modal-content {\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%; // Ensure `.modal-content` extends the full width of the parent `.modal-dialog`\r\n  // counteract the pointer-events: none; in the .modal-dialog\r\n  color: $modal-content-color;\r\n  pointer-events: auto;\r\n  background-color: $modal-content-bg;\r\n  background-clip: padding-box;\r\n  border: $modal-content-border-width solid $modal-content-border-color;\r\n  @include border-radius($modal-content-border-radius);\r\n  @include box-shadow($modal-content-box-shadow-xs);\r\n  // Remove focus outline from opened modal\r\n  outline: 0;\r\n}\r\n\r\n// Modal background\r\n.modal-backdrop {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  z-index: $zindex-modal-backdrop;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  background-color: $modal-backdrop-bg;\r\n\r\n  // Fade for backdrop\r\n  &.fade { opacity: 0; }\r\n  &.show { opacity: $modal-backdrop-opacity; }\r\n}\r\n\r\n// Modal header\r\n// Top section of the modal w/ title and dismiss\r\n.modal-header {\r\n  display: flex;\r\n  align-items: flex-start; // so the close btn always stays on the upper right corner\r\n  justify-content: space-between; // Put modal header elements (title and dismiss) on opposite ends\r\n  padding: $modal-header-padding;\r\n  border-bottom: $modal-header-border-width solid $modal-header-border-color;\r\n  @include border-top-radius($modal-content-border-radius);\r\n\r\n  .close {\r\n    padding: $modal-header-padding;\r\n    // auto on the left force icon to the right even when there is no .modal-title\r\n    margin: (-$modal-header-padding-y) (-$modal-header-padding-x) (-$modal-header-padding-y) auto;\r\n  }\r\n}\r\n\r\n// Title text within header\r\n.modal-title {\r\n  margin-bottom: 0;\r\n  line-height: $modal-title-line-height;\r\n}\r\n\r\n// Modal body\r\n// Where all modal content resides (sibling of .modal-header and .modal-footer)\r\n.modal-body {\r\n  position: relative;\r\n  // Enable `flex-grow: 1` so that the body take up as much space as possible\r\n  // when should there be a fixed height on `.modal-dialog`.\r\n  flex: 1 1 auto;\r\n  padding: $modal-inner-padding;\r\n}\r\n\r\n// Footer (for actions)\r\n.modal-footer {\r\n  display: flex;\r\n  align-items: center; // vertically center\r\n  justify-content: flex-end; // Right align buttons with flex property because text-align doesn't work on flex items\r\n  padding: $modal-inner-padding;\r\n  border-top: $modal-footer-border-width solid $modal-footer-border-color;\r\n  @include border-bottom-radius($modal-content-border-radius);\r\n\r\n  // Easily place margin between footer elements\r\n  > :not(:first-child) { margin-left: .25rem; }\r\n  > :not(:last-child) { margin-right: .25rem; }\r\n}\r\n\r\n// Measure scrollbar width for padding body during modal show/hide\r\n.modal-scrollbar-measure {\r\n  position: absolute;\r\n  top: -9999px;\r\n  width: 50px;\r\n  height: 50px;\r\n  overflow: scroll;\r\n}\r\n\r\n// Scale up the modal\r\n@include media-breakpoint-up(sm) {\r\n  // Automatically set modal's width for larger viewports\r\n  .modal-dialog {\r\n    max-width: $modal-md;\r\n    margin: $modal-dialog-margin-y-sm-up auto;\r\n  }\r\n\r\n  .modal-dialog-scrollable {\r\n    max-height: calc(100% - #{$modal-dialog-margin-y-sm-up * 2});\r\n\r\n    .modal-content {\r\n      max-height: calc(100vh - #{$modal-dialog-margin-y-sm-up * 2});\r\n    }\r\n  }\r\n\r\n  .modal-dialog-centered {\r\n    min-height: calc(100% - #{$modal-dialog-margin-y-sm-up * 2});\r\n\r\n    &::before {\r\n      height: calc(100vh - #{$modal-dialog-margin-y-sm-up * 2});\r\n    }\r\n  }\r\n\r\n  .modal-content {\r\n    @include box-shadow($modal-content-box-shadow-sm-up);\r\n  }\r\n\r\n  .modal-sm { max-width: $modal-sm; }\r\n}\r\n\r\n@include media-breakpoint-up(lg) {\r\n  .modal-lg,\r\n  .modal-xl {\r\n    max-width: $modal-lg;\r\n  }\r\n}\r\n\r\n@include media-breakpoint-up(xl) {\r\n  .modal-xl { max-width: $modal-xl; }\r\n}\r\n", "// Base class\r\n.tooltip {\r\n  position: absolute;\r\n  z-index: $zindex-tooltip;\r\n  display: block;\r\n  margin: $tooltip-margin;\r\n  // Our parent element can be arbitrary since tooltips are by default inserted as a sibling of their target element.\r\n  // So reset our font and text properties to avoid inheriting weird values.\r\n  @include reset-text();\r\n  @include font-size($tooltip-font-size);\r\n  // Allow breaking very long words so they don't overflow the tooltip's bounds\r\n  word-wrap: break-word;\r\n  opacity: 0;\r\n\r\n  &.show { opacity: $tooltip-opacity; }\r\n\r\n  .arrow {\r\n    position: absolute;\r\n    display: block;\r\n    width: $tooltip-arrow-width;\r\n    height: $tooltip-arrow-height;\r\n\r\n    &::before {\r\n      position: absolute;\r\n      content: \"\";\r\n      border-color: transparent;\r\n      border-style: solid;\r\n    }\r\n  }\r\n}\r\n\r\n.bs-tooltip-top {\r\n  padding: $tooltip-arrow-height 0;\r\n\r\n  .arrow {\r\n    bottom: 0;\r\n\r\n    &::before {\r\n      top: 0;\r\n      border-width: $tooltip-arrow-height ($tooltip-arrow-width / 2) 0;\r\n      border-top-color: $tooltip-arrow-color;\r\n    }\r\n  }\r\n}\r\n\r\n.bs-tooltip-right {\r\n  padding: 0 $tooltip-arrow-height;\r\n\r\n  .arrow {\r\n    left: 0;\r\n    width: $tooltip-arrow-height;\r\n    height: $tooltip-arrow-width;\r\n\r\n    &::before {\r\n      right: 0;\r\n      border-width: ($tooltip-arrow-width / 2) $tooltip-arrow-height ($tooltip-arrow-width / 2) 0;\r\n      border-right-color: $tooltip-arrow-color;\r\n    }\r\n  }\r\n}\r\n\r\n.bs-tooltip-bottom {\r\n  padding: $tooltip-arrow-height 0;\r\n\r\n  .arrow {\r\n    top: 0;\r\n\r\n    &::before {\r\n      bottom: 0;\r\n      border-width: 0 ($tooltip-arrow-width / 2) $tooltip-arrow-height;\r\n      border-bottom-color: $tooltip-arrow-color;\r\n    }\r\n  }\r\n}\r\n\r\n.bs-tooltip-left {\r\n  padding: 0 $tooltip-arrow-height;\r\n\r\n  .arrow {\r\n    right: 0;\r\n    width: $tooltip-arrow-height;\r\n    height: $tooltip-arrow-width;\r\n\r\n    &::before {\r\n      left: 0;\r\n      border-width: ($tooltip-arrow-width / 2) 0 ($tooltip-arrow-width / 2) $tooltip-arrow-height;\r\n      border-left-color: $tooltip-arrow-color;\r\n    }\r\n  }\r\n}\r\n\r\n.bs-tooltip-auto {\r\n  &[x-placement^=\"top\"] {\r\n    @extend .bs-tooltip-top;\r\n  }\r\n  &[x-placement^=\"right\"] {\r\n    @extend .bs-tooltip-right;\r\n  }\r\n  &[x-placement^=\"bottom\"] {\r\n    @extend .bs-tooltip-bottom;\r\n  }\r\n  &[x-placement^=\"left\"] {\r\n    @extend .bs-tooltip-left;\r\n  }\r\n}\r\n\r\n// Wrapper for the tooltip content\r\n.tooltip-inner {\r\n  max-width: $tooltip-max-width;\r\n  padding: $tooltip-padding-y $tooltip-padding-x;\r\n  color: $tooltip-color;\r\n  text-align: center;\r\n  background-color: $tooltip-bg;\r\n  @include border-radius($tooltip-border-radius);\r\n}\r\n", "@mixin reset-text {\r\n  font-family: $font-family-base;\r\n  // We deliberately do NOT reset font-size or word-wrap.\r\n  font-style: normal;\r\n  font-weight: $font-weight-normal;\r\n  line-height: $line-height-base;\r\n  text-align: left; // Fallback for where `start` is not supported\r\n  text-align: start;\r\n  text-decoration: none;\r\n  text-shadow: none;\r\n  text-transform: none;\r\n  letter-spacing: normal;\r\n  word-break: normal;\r\n  word-spacing: normal;\r\n  white-space: normal;\r\n  line-break: auto;\r\n}\r\n", ".popover {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  z-index: $zindex-popover;\r\n  display: block;\r\n  max-width: $popover-max-width;\r\n  // Our parent element can be arbitrary since tooltips are by default inserted as a sibling of their target element.\r\n  // So reset our font and text properties to avoid inheriting weird values.\r\n  @include reset-text();\r\n  @include font-size($popover-font-size);\r\n  // Allow breaking very long words so they don't overflow the popover's bounds\r\n  word-wrap: break-word;\r\n  background-color: $popover-bg;\r\n  background-clip: padding-box;\r\n  border: $popover-border-width solid $popover-border-color;\r\n  @include border-radius($popover-border-radius);\r\n  @include box-shadow($popover-box-shadow);\r\n\r\n  .arrow {\r\n    position: absolute;\r\n    display: block;\r\n    width: $popover-arrow-width;\r\n    height: $popover-arrow-height;\r\n    margin: 0 $border-radius-lg;\r\n\r\n    &::before,\r\n    &::after {\r\n      position: absolute;\r\n      display: block;\r\n      content: \"\";\r\n      border-color: transparent;\r\n      border-style: solid;\r\n    }\r\n  }\r\n}\r\n\r\n.bs-popover-top {\r\n  margin-bottom: $popover-arrow-height;\r\n\r\n  > .arrow {\r\n    bottom: calc((#{$popover-arrow-height} + #{$popover-border-width}) * -1);\r\n\r\n    &::before {\r\n      bottom: 0;\r\n      border-width: $popover-arrow-height ($popover-arrow-width / 2) 0;\r\n      border-top-color: $popover-arrow-outer-color;\r\n    }\r\n\r\n    &::after {\r\n      bottom: $popover-border-width;\r\n      border-width: $popover-arrow-height ($popover-arrow-width / 2) 0;\r\n      border-top-color: $popover-arrow-color;\r\n    }\r\n  }\r\n}\r\n\r\n.bs-popover-right {\r\n  margin-left: $popover-arrow-height;\r\n\r\n  > .arrow {\r\n    left: calc((#{$popover-arrow-height} + #{$popover-border-width}) * -1);\r\n    width: $popover-arrow-height;\r\n    height: $popover-arrow-width;\r\n    margin: $border-radius-lg 0; // make sure the arrow does not touch the popover's rounded corners\r\n\r\n    &::before {\r\n      left: 0;\r\n      border-width: ($popover-arrow-width / 2) $popover-arrow-height ($popover-arrow-width / 2) 0;\r\n      border-right-color: $popover-arrow-outer-color;\r\n    }\r\n\r\n    &::after {\r\n      left: $popover-border-width;\r\n      border-width: ($popover-arrow-width / 2) $popover-arrow-height ($popover-arrow-width / 2) 0;\r\n      border-right-color: $popover-arrow-color;\r\n    }\r\n  }\r\n}\r\n\r\n.bs-popover-bottom {\r\n  margin-top: $popover-arrow-height;\r\n\r\n  > .arrow {\r\n    top: calc((#{$popover-arrow-height} + #{$popover-border-width}) * -1);\r\n\r\n    &::before {\r\n      top: 0;\r\n      border-width: 0 ($popover-arrow-width / 2) $popover-arrow-height ($popover-arrow-width / 2);\r\n      border-bottom-color: $popover-arrow-outer-color;\r\n    }\r\n\r\n    &::after {\r\n      top: $popover-border-width;\r\n      border-width: 0 ($popover-arrow-width / 2) $popover-arrow-height ($popover-arrow-width / 2);\r\n      border-bottom-color: $popover-arrow-color;\r\n    }\r\n  }\r\n\r\n  // This will remove the popover-header's border just below the arrow\r\n  .popover-header::before {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 50%;\r\n    display: block;\r\n    width: $popover-arrow-width;\r\n    margin-left: -$popover-arrow-width / 2;\r\n    content: \"\";\r\n    border-bottom: $popover-border-width solid $popover-header-bg;\r\n  }\r\n}\r\n\r\n.bs-popover-left {\r\n  margin-right: $popover-arrow-height;\r\n\r\n  > .arrow {\r\n    right: calc((#{$popover-arrow-height} + #{$popover-border-width}) * -1);\r\n    width: $popover-arrow-height;\r\n    height: $popover-arrow-width;\r\n    margin: $border-radius-lg 0; // make sure the arrow does not touch the popover's rounded corners\r\n\r\n    &::before {\r\n      right: 0;\r\n      border-width: ($popover-arrow-width / 2) 0 ($popover-arrow-width / 2) $popover-arrow-height;\r\n      border-left-color: $popover-arrow-outer-color;\r\n    }\r\n\r\n    &::after {\r\n      right: $popover-border-width;\r\n      border-width: ($popover-arrow-width / 2) 0 ($popover-arrow-width / 2) $popover-arrow-height;\r\n      border-left-color: $popover-arrow-color;\r\n    }\r\n  }\r\n}\r\n\r\n.bs-popover-auto {\r\n  &[x-placement^=\"top\"] {\r\n    @extend .bs-popover-top;\r\n  }\r\n  &[x-placement^=\"right\"] {\r\n    @extend .bs-popover-right;\r\n  }\r\n  &[x-placement^=\"bottom\"] {\r\n    @extend .bs-popover-bottom;\r\n  }\r\n  &[x-placement^=\"left\"] {\r\n    @extend .bs-popover-left;\r\n  }\r\n}\r\n\r\n\r\n// Offset the popover to account for the popover arrow\r\n.popover-header {\r\n  padding: $popover-header-padding-y $popover-header-padding-x;\r\n  margin-bottom: 0; // Reset the default from Reboot\r\n  @include font-size($font-size-base);\r\n  color: $popover-header-color;\r\n  background-color: $popover-header-bg;\r\n  border-bottom: $popover-border-width solid darken($popover-header-bg, 5%);\r\n  $offset-border-width: calc(#{$border-radius-lg} - #{$popover-border-width});\r\n  @include border-top-radius($offset-border-width);\r\n\r\n  &:empty {\r\n    display: none;\r\n  }\r\n}\r\n\r\n.popover-body {\r\n  padding: $popover-body-padding-y $popover-body-padding-x;\r\n  color: $popover-body-color;\r\n}\r\n", "// Notes on the classes:\r\n//\r\n// 1. .carousel.pointer-event should ideally be pan-y (to allow for users to scroll vertically)\r\n//    even when their scroll action started on a carousel, but for compatibility (with Firefox)\r\n//    we're preventing all actions instead\r\n// 2. The .carousel-item-left and .carousel-item-right is used to indicate where\r\n//    the active slide is heading.\r\n// 3. .active.carousel-item is the current slide.\r\n// 4. .active.carousel-item-left and .active.carousel-item-right is the current\r\n//    slide in its in-transition state. Only one of these occurs at a time.\r\n// 5. .carousel-item-next.carousel-item-left and .carousel-item-prev.carousel-item-right\r\n//    is the upcoming slide in transition.\r\n\r\n.carousel {\r\n  position: relative;\r\n}\r\n\r\n.carousel.pointer-event {\r\n  touch-action: pan-y;\r\n}\r\n\r\n.carousel-inner {\r\n  position: relative;\r\n  width: 100%;\r\n  overflow: hidden;\r\n  @include clearfix();\r\n}\r\n\r\n.carousel-item {\r\n  position: relative;\r\n  display: none;\r\n  float: left;\r\n  width: 100%;\r\n  margin-right: -100%;\r\n  backface-visibility: hidden;\r\n  @include transition($carousel-transition);\r\n}\r\n\r\n.carousel-item.active,\r\n.carousel-item-next,\r\n.carousel-item-prev {\r\n  display: block;\r\n}\r\n\r\n.carousel-item-next:not(.carousel-item-left),\r\n.active.carousel-item-right {\r\n  transform: translateX(100%);\r\n}\r\n\r\n.carousel-item-prev:not(.carousel-item-right),\r\n.active.carousel-item-left {\r\n  transform: translateX(-100%);\r\n}\r\n\r\n\r\n//\r\n// Alternate transitions\r\n//\r\n\r\n.carousel-fade {\r\n  .carousel-item {\r\n    opacity: 0;\r\n    transition-property: opacity;\r\n    transform: none;\r\n  }\r\n\r\n  .carousel-item.active,\r\n  .carousel-item-next.carousel-item-left,\r\n  .carousel-item-prev.carousel-item-right {\r\n    z-index: 1;\r\n    opacity: 1;\r\n  }\r\n\r\n  .active.carousel-item-left,\r\n  .active.carousel-item-right {\r\n    z-index: 0;\r\n    opacity: 0;\r\n    @include transition(0s $carousel-transition-duration opacity);\r\n  }\r\n}\r\n\r\n\r\n//\r\n// Left/right controls for nav\r\n//\r\n\r\n.carousel-control-prev,\r\n.carousel-control-next {\r\n  position: absolute;\r\n  top: 0;\r\n  bottom: 0;\r\n  z-index: 1;\r\n  // Use flex for alignment (1-3)\r\n  display: flex; // 1. allow flex styles\r\n  align-items: center; // 2. vertically center contents\r\n  justify-content: center; // 3. horizontally center contents\r\n  width: $carousel-control-width;\r\n  color: $carousel-control-color;\r\n  text-align: center;\r\n  opacity: $carousel-control-opacity;\r\n  @include transition($carousel-control-transition);\r\n\r\n  // Hover/focus state\r\n  @include hover-focus {\r\n    color: $carousel-control-color;\r\n    text-decoration: none;\r\n    outline: 0;\r\n    opacity: $carousel-control-hover-opacity;\r\n  }\r\n}\r\n.carousel-control-prev {\r\n  left: 0;\r\n  @if $enable-gradients {\r\n    background: linear-gradient(90deg, rgba($black, .25), rgba($black, .001));\r\n  }\r\n}\r\n.carousel-control-next {\r\n  right: 0;\r\n  @if $enable-gradients {\r\n    background: linear-gradient(270deg, rgba($black, .25), rgba($black, .001));\r\n  }\r\n}\r\n\r\n// Icons for within\r\n.carousel-control-prev-icon,\r\n.carousel-control-next-icon {\r\n  display: inline-block;\r\n  width: $carousel-control-icon-width;\r\n  height: $carousel-control-icon-width;\r\n  background: no-repeat 50% / 100% 100%;\r\n}\r\n.carousel-control-prev-icon {\r\n  background-image: $carousel-control-prev-icon-bg;\r\n}\r\n.carousel-control-next-icon {\r\n  background-image: $carousel-control-next-icon-bg;\r\n}\r\n\r\n\r\n// Optional indicator pips\r\n//\r\n// Add an ordered list with the following class and add a list item for each\r\n// slide your carousel holds.\r\n\r\n.carousel-indicators {\r\n  position: absolute;\r\n  right: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n  z-index: 15;\r\n  display: flex;\r\n  justify-content: center;\r\n  padding-left: 0; // override <ol> default\r\n  // Use the .carousel-control's width as margin so we don't overlay those\r\n  margin-right: $carousel-control-width;\r\n  margin-left: $carousel-control-width;\r\n  list-style: none;\r\n\r\n  li {\r\n    box-sizing: content-box;\r\n    flex: 0 1 auto;\r\n    width: $carousel-indicator-width;\r\n    height: $carousel-indicator-height;\r\n    margin-right: $carousel-indicator-spacer;\r\n    margin-left: $carousel-indicator-spacer;\r\n    text-indent: -999px;\r\n    cursor: pointer;\r\n    background-color: $carousel-indicator-active-bg;\r\n    background-clip: padding-box;\r\n    // Use transparent borders to increase the hit area by 10px on top and bottom.\r\n    border-top: $carousel-indicator-hit-area-height solid transparent;\r\n    border-bottom: $carousel-indicator-hit-area-height solid transparent;\r\n    opacity: .5;\r\n    @include transition($carousel-indicator-transition);\r\n  }\r\n\r\n  .active {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n\r\n// Optional captions\r\n//\r\n//\r\n\r\n.carousel-caption {\r\n  position: absolute;\r\n  right: (100% - $carousel-caption-width) / 2;\r\n  bottom: 20px;\r\n  left: (100% - $carousel-caption-width) / 2;\r\n  z-index: 10;\r\n  padding-top: 20px;\r\n  padding-bottom: 20px;\r\n  color: $carousel-caption-color;\r\n  text-align: center;\r\n}\r\n", "@mixin clearfix() {\r\n  &::after {\r\n    display: block;\r\n    clear: both;\r\n    content: \"\";\r\n  }\r\n}\r\n", "//\r\n// Rotating border\r\n//\r\n\r\n@keyframes spinner-border {\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n.spinner-border {\r\n  display: inline-block;\r\n  width: $spinner-width;\r\n  height: $spinner-height;\r\n  vertical-align: text-bottom;\r\n  border: $spinner-border-width solid currentColor;\r\n  border-right-color: transparent;\r\n  // stylelint-disable-next-line property-blacklist\r\n  border-radius: 50%;\r\n  animation: spinner-border .75s linear infinite;\r\n}\r\n\r\n.spinner-border-sm {\r\n  width: $spinner-width-sm;\r\n  height: $spinner-height-sm;\r\n  border-width: $spinner-border-width-sm;\r\n}\r\n\r\n//\r\n// Growing circle\r\n//\r\n\r\n@keyframes spinner-grow {\r\n  0% {\r\n    transform: scale(0);\r\n  }\r\n  50% {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n.spinner-grow {\r\n  display: inline-block;\r\n  width: $spinner-width;\r\n  height: $spinner-height;\r\n  vertical-align: text-bottom;\r\n  background-color: currentColor;\r\n  // stylelint-disable-next-line property-blacklist\r\n  border-radius: 50%;\r\n  opacity: 0;\r\n  animation: spinner-grow .75s linear infinite;\r\n}\r\n\r\n.spinner-grow-sm {\r\n  width: $spinner-width-sm;\r\n  height: $spinner-height-sm;\r\n}\r\n", "// stylelint-disable declaration-no-important\r\n\r\n.align-baseline    { vertical-align: baseline !important; } // Browser default\r\n.align-top         { vertical-align: top !important; }\r\n.align-middle      { vertical-align: middle !important; }\r\n.align-bottom      { vertical-align: bottom !important; }\r\n.align-text-bottom { vertical-align: text-bottom !important; }\r\n.align-text-top    { vertical-align: text-top !important; }\r\n", "// stylelint-disable declaration-no-important\r\n\r\n// Contextual backgrounds\r\n\r\n@mixin bg-variant($parent, $color) {\r\n  #{$parent} {\r\n    background-color: $color !important;\r\n  }\r\n  a#{$parent},\r\n  button#{$parent} {\r\n    @include hover-focus {\r\n      background-color: darken($color, 10%) !important;\r\n    }\r\n  }\r\n}\r\n\r\n@mixin bg-gradient-variant($parent, $color) {\r\n  #{$parent} {\r\n    background: $color linear-gradient(180deg, mix($body-bg, $color, 15%), $color) repeat-x !important;\r\n  }\r\n}\r\n", "// stylelint-disable declaration-no-important\r\n\r\n@each $color, $value in $theme-colors {\r\n  @include bg-variant(\".bg-#{$color}\", $value);\r\n}\r\n\r\n@if $enable-gradients {\r\n  @each $color, $value in $theme-colors {\r\n    @include bg-gradient-variant(\".bg-gradient-#{$color}\", $value);\r\n  }\r\n}\r\n\r\n.bg-white {\r\n  background-color: $white !important;\r\n}\r\n\r\n.bg-transparent {\r\n  background-color: transparent !important;\r\n}\r\n", "// stylelint-disable property-blacklist, declaration-no-important\r\n\r\n//\r\n// Border\r\n//\r\n\r\n.border         { border: $border-width solid $border-color !important; }\r\n.border-top     { border-top: $border-width solid $border-color !important; }\r\n.border-right   { border-right: $border-width solid $border-color !important; }\r\n.border-bottom  { border-bottom: $border-width solid $border-color !important; }\r\n.border-left    { border-left: $border-width solid $border-color !important; }\r\n\r\n.border-0        { border: 0 !important; }\r\n.border-top-0    { border-top: 0 !important; }\r\n.border-right-0  { border-right: 0 !important; }\r\n.border-bottom-0 { border-bottom: 0 !important; }\r\n.border-left-0   { border-left: 0 !important; }\r\n\r\n@each $color, $value in $theme-colors {\r\n  .border-#{$color} {\r\n    border-color: $value !important;\r\n  }\r\n}\r\n\r\n.border-white {\r\n  border-color: $white !important;\r\n}\r\n\r\n//\r\n// Border-radius\r\n//\r\n\r\n.rounded-sm {\r\n  border-radius: $border-radius-sm !important;\r\n}\r\n\r\n.rounded {\r\n  border-radius: $border-radius !important;\r\n}\r\n\r\n.rounded-top {\r\n  border-top-left-radius: $border-radius !important;\r\n  border-top-right-radius: $border-radius !important;\r\n}\r\n\r\n.rounded-right {\r\n  border-top-right-radius: $border-radius !important;\r\n  border-bottom-right-radius: $border-radius !important;\r\n}\r\n\r\n.rounded-bottom {\r\n  border-bottom-right-radius: $border-radius !important;\r\n  border-bottom-left-radius: $border-radius !important;\r\n}\r\n\r\n.rounded-left {\r\n  border-top-left-radius: $border-radius !important;\r\n  border-bottom-left-radius: $border-radius !important;\r\n}\r\n\r\n.rounded-lg {\r\n  border-radius: $border-radius-lg !important;\r\n}\r\n\r\n.rounded-circle {\r\n  border-radius: 50% !important;\r\n}\r\n\r\n.rounded-pill {\r\n  border-radius: $rounded-pill !important;\r\n}\r\n\r\n.rounded-0 {\r\n  border-radius: 0 !important;\r\n}\r\n", "// stylelint-disable declaration-no-important\r\n\r\n//\r\n// Utilities for common `display` values\r\n//\r\n\r\n@each $breakpoint in map-keys($grid-breakpoints) {\r\n  @include media-breakpoint-up($breakpoint) {\r\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\r\n\r\n    @each $value in $displays {\r\n      .d#{$infix}-#{$value} { display: $value !important; }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n//\r\n// Utilities for toggling `display` in print\r\n//\r\n\r\n@media print {\r\n  @each $value in $displays {\r\n    .d-print-#{$value} { display: $value !important; }\r\n  }\r\n}\r\n", "// Credit: <PERSON> and <PERSON><PERSON><PERSON> CSS.\r\n\r\n.embed-responsive {\r\n  position: relative;\r\n  display: block;\r\n  width: 100%;\r\n  padding: 0;\r\n  overflow: hidden;\r\n\r\n  &::before {\r\n    display: block;\r\n    content: \"\";\r\n  }\r\n\r\n  .embed-responsive-item,\r\n  iframe,\r\n  embed,\r\n  object,\r\n  video {\r\n    position: absolute;\r\n    top: 0;\r\n    bottom: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    border: 0;\r\n  }\r\n}\r\n\r\n@each $embed-responsive-aspect-ratio in $embed-responsive-aspect-ratios {\r\n  $embed-responsive-aspect-ratio-x: nth($embed-responsive-aspect-ratio, 1);\r\n  $embed-responsive-aspect-ratio-y: nth($embed-responsive-aspect-ratio, 2);\r\n\r\n  .embed-responsive-#{$embed-responsive-aspect-ratio-x}by#{$embed-responsive-aspect-ratio-y} {\r\n    &::before {\r\n      padding-top: percentage($embed-responsive-aspect-ratio-y / $embed-responsive-aspect-ratio-x);\r\n    }\r\n  }\r\n}\r\n", "// stylelint-disable declaration-no-important\r\n\r\n// Flex variation\r\n//\r\n// Custom styles for additional flex alignment options.\r\n\r\n@each $breakpoint in map-keys($grid-breakpoints) {\r\n  @include media-breakpoint-up($breakpoint) {\r\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\r\n\r\n    .flex#{$infix}-row            { flex-direction: row !important; }\r\n    .flex#{$infix}-column         { flex-direction: column !important; }\r\n    .flex#{$infix}-row-reverse    { flex-direction: row-reverse !important; }\r\n    .flex#{$infix}-column-reverse { flex-direction: column-reverse !important; }\r\n\r\n    .flex#{$infix}-wrap         { flex-wrap: wrap !important; }\r\n    .flex#{$infix}-nowrap       { flex-wrap: nowrap !important; }\r\n    .flex#{$infix}-wrap-reverse { flex-wrap: wrap-reverse !important; }\r\n    .flex#{$infix}-fill         { flex: 1 1 auto !important; }\r\n    .flex#{$infix}-grow-0       { flex-grow: 0 !important; }\r\n    .flex#{$infix}-grow-1       { flex-grow: 1 !important; }\r\n    .flex#{$infix}-shrink-0     { flex-shrink: 0 !important; }\r\n    .flex#{$infix}-shrink-1     { flex-shrink: 1 !important; }\r\n\r\n    .justify-content#{$infix}-start   { justify-content: flex-start !important; }\r\n    .justify-content#{$infix}-end     { justify-content: flex-end !important; }\r\n    .justify-content#{$infix}-center  { justify-content: center !important; }\r\n    .justify-content#{$infix}-between { justify-content: space-between !important; }\r\n    .justify-content#{$infix}-around  { justify-content: space-around !important; }\r\n\r\n    .align-items#{$infix}-start    { align-items: flex-start !important; }\r\n    .align-items#{$infix}-end      { align-items: flex-end !important; }\r\n    .align-items#{$infix}-center   { align-items: center !important; }\r\n    .align-items#{$infix}-baseline { align-items: baseline !important; }\r\n    .align-items#{$infix}-stretch  { align-items: stretch !important; }\r\n\r\n    .align-content#{$infix}-start   { align-content: flex-start !important; }\r\n    .align-content#{$infix}-end     { align-content: flex-end !important; }\r\n    .align-content#{$infix}-center  { align-content: center !important; }\r\n    .align-content#{$infix}-between { align-content: space-between !important; }\r\n    .align-content#{$infix}-around  { align-content: space-around !important; }\r\n    .align-content#{$infix}-stretch { align-content: stretch !important; }\r\n\r\n    .align-self#{$infix}-auto     { align-self: auto !important; }\r\n    .align-self#{$infix}-start    { align-self: flex-start !important; }\r\n    .align-self#{$infix}-end      { align-self: flex-end !important; }\r\n    .align-self#{$infix}-center   { align-self: center !important; }\r\n    .align-self#{$infix}-baseline { align-self: baseline !important; }\r\n    .align-self#{$infix}-stretch  { align-self: stretch !important; }\r\n  }\r\n}\r\n", "// stylelint-disable declaration-no-important\r\n\r\n@each $breakpoint in map-keys($grid-breakpoints) {\r\n  @include media-breakpoint-up($breakpoint) {\r\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\r\n\r\n    .float#{$infix}-left  { float: left !important; }\r\n    .float#{$infix}-right { float: right !important; }\r\n    .float#{$infix}-none  { float: none !important; }\r\n  }\r\n}\r\n", "// stylelint-disable declaration-no-important\r\n\r\n@each $value in $overflows {\r\n  .overflow-#{$value} { overflow: $value !important; }\r\n}\r\n", "// stylelint-disable declaration-no-important\r\n\r\n// Common values\r\n@each $position in $positions {\r\n  .position-#{$position} { position: $position !important; }\r\n}\r\n\r\n// Shorthand\r\n\r\n.fixed-top {\r\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  left: 0;\r\n  z-index: $zindex-fixed;\r\n}\r\n\r\n.fixed-bottom {\r\n  position: fixed;\r\n  right: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n  z-index: $zindex-fixed;\r\n}\r\n\r\n.sticky-top {\r\n  @supports (position: sticky) {\r\n    position: sticky;\r\n    top: 0;\r\n    z-index: $zindex-sticky;\r\n  }\r\n}\r\n", "//\r\n// Screenreaders\r\n//\r\n\r\n.sr-only {\r\n  @include sr-only();\r\n}\r\n\r\n.sr-only-focusable {\r\n  @include sr-only-focusable();\r\n}\r\n", "// Only display content to screen readers\r\n//\r\n// See: https://a11yproject.com/posts/how-to-hide-content/\r\n// See: https://hugogiraudel.com/2016/10/13/css-hide-and-seek/\r\n\r\n@mixin sr-only {\r\n  position: absolute;\r\n  width: 1px;\r\n  height: 1px;\r\n  padding: 0;\r\n  overflow: hidden;\r\n  clip: rect(0, 0, 0, 0);\r\n  white-space: nowrap;\r\n  border: 0;\r\n}\r\n\r\n// Use in conjunction with .sr-only to only display content when it's focused.\r\n//\r\n// Useful for \"Skip to main content\" links; see https://www.w3.org/TR/2013/NOTE-WCAG20-TECHS-20130905/G1\r\n//\r\n// Credit: HTML5 Boilerplate\r\n\r\n@mixin sr-only-focusable {\r\n  &:active,\r\n  &:focus {\r\n    position: static;\r\n    width: auto;\r\n    height: auto;\r\n    overflow: visible;\r\n    clip: auto;\r\n    white-space: normal;\r\n  }\r\n}\r\n", "// stylelint-disable declaration-no-important\r\n\r\n.shadow-sm { box-shadow: $box-shadow-sm !important; }\r\n.shadow { box-shadow: $box-shadow !important; }\r\n.shadow-lg { box-shadow: $box-shadow-lg !important; }\r\n.shadow-none { box-shadow: none !important; }\r\n", "// stylelint-disable declaration-no-important\r\n\r\n// Width and height\r\n\r\n@each $prop, $abbrev in (width: w, height: h) {\r\n  @each $size, $length in $sizes {\r\n    .#{$abbrev}-#{$size} { #{$prop}: $length !important; }\r\n  }\r\n}\r\n\r\n.mw-100 { max-width: 100% !important; }\r\n.mh-100 { max-height: 100% !important; }\r\n\r\n// Viewport additional helpers\r\n\r\n.min-vw-100 { min-width: 100vw !important; }\r\n.min-vh-100 { min-height: 100vh !important; }\r\n\r\n.vw-100 { width: 100vw !important; }\r\n.vh-100 { height: 100vh !important; }\r\n", "//\r\n// Stretched link\r\n//\r\n\r\n.stretched-link {\r\n  &::after {\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    left: 0;\r\n    z-index: 1;\r\n    // Just in case `pointer-events: none` is set on a parent\r\n    pointer-events: auto;\r\n    content: \"\";\r\n    // IE10 bugfix, see https://stackoverflow.com/questions/16947967/ie10-hover-pseudo-class-doesnt-work-without-background-color\r\n    background-color: rgba(0, 0, 0, 0);\r\n  }\r\n}\r\n", "// stylelint-disable declaration-no-important\r\n\r\n// Mar<PERSON> and Padding\r\n\r\n@each $breakpoint in map-keys($grid-breakpoints) {\r\n  @include media-breakpoint-up($breakpoint) {\r\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\r\n\r\n    @each $prop, $abbrev in (margin: m, padding: p) {\r\n      @each $size, $length in $spacers {\r\n        .#{$abbrev}#{$infix}-#{$size} { #{$prop}: $length !important; }\r\n        .#{$abbrev}t#{$infix}-#{$size},\r\n        .#{$abbrev}y#{$infix}-#{$size} {\r\n          #{$prop}-top: $length !important;\r\n        }\r\n        .#{$abbrev}r#{$infix}-#{$size},\r\n        .#{$abbrev}x#{$infix}-#{$size} {\r\n          #{$prop}-right: $length !important;\r\n        }\r\n        .#{$abbrev}b#{$infix}-#{$size},\r\n        .#{$abbrev}y#{$infix}-#{$size} {\r\n          #{$prop}-bottom: $length !important;\r\n        }\r\n        .#{$abbrev}l#{$infix}-#{$size},\r\n        .#{$abbrev}x#{$infix}-#{$size} {\r\n          #{$prop}-left: $length !important;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Negative margins (e.g., where `.mb-n1` is negative version of `.mb-1`)\r\n    @each $size, $length in $spacers {\r\n      @if $size != 0 {\r\n        .m#{$infix}-n#{$size} { margin: -$length !important; }\r\n        .mt#{$infix}-n#{$size},\r\n        .my#{$infix}-n#{$size} {\r\n          margin-top: -$length !important;\r\n        }\r\n        .mr#{$infix}-n#{$size},\r\n        .mx#{$infix}-n#{$size} {\r\n          margin-right: -$length !important;\r\n        }\r\n        .mb#{$infix}-n#{$size},\r\n        .my#{$infix}-n#{$size} {\r\n          margin-bottom: -$length !important;\r\n        }\r\n        .ml#{$infix}-n#{$size},\r\n        .mx#{$infix}-n#{$size} {\r\n          margin-left: -$length !important;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Some special margin utils\r\n    .m#{$infix}-auto { margin: auto !important; }\r\n    .mt#{$infix}-auto,\r\n    .my#{$infix}-auto {\r\n      margin-top: auto !important;\r\n    }\r\n    .mr#{$infix}-auto,\r\n    .mx#{$infix}-auto {\r\n      margin-right: auto !important;\r\n    }\r\n    .mb#{$infix}-auto,\r\n    .my#{$infix}-auto {\r\n      margin-bottom: auto !important;\r\n    }\r\n    .ml#{$infix}-auto,\r\n    .mx#{$infix}-auto {\r\n      margin-left: auto !important;\r\n    }\r\n  }\r\n}\r\n", "// stylelint-disable declaration-no-important\r\n\r\n//\r\n// Text\r\n//\r\n\r\n.text-monospace { font-family: $font-family-monospace !important; }\r\n\r\n// Alignment\r\n\r\n.text-justify  { text-align: justify !important; }\r\n.text-wrap     { white-space: normal !important; }\r\n.text-nowrap   { white-space: nowrap !important; }\r\n.text-truncate { @include text-truncate; }\r\n\r\n// Responsive alignment\r\n\r\n@each $breakpoint in map-keys($grid-breakpoints) {\r\n  @include media-breakpoint-up($breakpoint) {\r\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\r\n\r\n    .text#{$infix}-left   { text-align: left !important; }\r\n    .text#{$infix}-right  { text-align: right !important; }\r\n    .text#{$infix}-center { text-align: center !important; }\r\n  }\r\n}\r\n\r\n// Transformation\r\n\r\n.text-lowercase  { text-transform: lowercase !important; }\r\n.text-uppercase  { text-transform: uppercase !important; }\r\n.text-capitalize { text-transform: capitalize !important; }\r\n\r\n// Weight and italics\r\n\r\n.font-weight-light   { font-weight: $font-weight-light !important; }\r\n.font-weight-lighter { font-weight: $font-weight-lighter !important; }\r\n.font-weight-normal  { font-weight: $font-weight-normal !important; }\r\n.font-weight-bold    { font-weight: $font-weight-bold !important; }\r\n.font-weight-bolder  { font-weight: $font-weight-bolder !important; }\r\n.font-italic         { font-style: italic !important; }\r\n\r\n// Contextual colors\r\n\r\n.text-white { color: $white !important; }\r\n\r\n@each $color, $value in $theme-colors {\r\n  @include text-emphasis-variant(\".text-#{$color}\", $value);\r\n}\r\n\r\n.text-body { color: $body-color !important; }\r\n.text-muted { color: $text-muted !important; }\r\n\r\n.text-black-50 { color: rgba($black, .5) !important; }\r\n.text-white-50 { color: rgba($white, .5) !important; }\r\n\r\n// Misc\r\n\r\n.text-hide {\r\n  @include text-hide($ignore-warning: true);\r\n}\r\n\r\n.text-decoration-none { text-decoration: none !important; }\r\n\r\n.text-break {\r\n  word-break: break-word !important; // IE & < Edge 18\r\n  overflow-wrap: break-word !important;\r\n}\r\n\r\n// Reset\r\n\r\n.text-reset { color: inherit !important; }\r\n", "// Text truncate\r\n// Requires inline-block or block for proper styling\r\n\r\n@mixin text-truncate() {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n", "// stylelint-disable declaration-no-important\r\n\r\n// Typography\r\n\r\n@mixin text-emphasis-variant($parent, $color) {\r\n  #{$parent} {\r\n    color: $color !important;\r\n  }\r\n  @if $emphasized-link-hover-darken-percentage != 0 {\r\n    a#{$parent} {\r\n      @include hover-focus {\r\n        color: darken($color, $emphasized-link-hover-darken-percentage) !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n", "// CSS image replacement\r\n@mixin text-hide($ignore-warning: false) {\r\n  // stylelint-disable-next-line font-family-no-missing-generic-family-keyword\r\n  font: 0/0 a;\r\n  color: transparent;\r\n  text-shadow: none;\r\n  background-color: transparent;\r\n  border: 0;\r\n\r\n  @include deprecate(\"`text-hide()`\", \"v4.1.0\", \"v5\", $ignore-warning);\r\n}\r\n", "// stylelint-disable declaration-no-important\r\n\r\n//\r\n// Visibility utilities\r\n//\r\n\r\n.visible {\r\n  visibility: visible !important;\r\n}\r\n\r\n.invisible {\r\n  visibility: hidden !important;\r\n}\r\n", "// stylelint-disable declaration-no-important, selector-no-qualifying-type\r\n\r\n// Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css\r\n\r\n// ==========================================================================\r\n// Print styles.\r\n// Inlined to avoid the additional HTTP request:\r\n// https://www.phpied.com/delay-loading-your-print-css/\r\n// ==========================================================================\r\n\r\n@if $enable-print-styles {\r\n  @media print {\r\n    *,\r\n    *::before,\r\n    *::after {\r\n      // Bootstrap specific; comment out `color` and `background`\r\n      //color: $black !important; // Black prints faster\r\n      text-shadow: none !important;\r\n      //background: transparent !important;\r\n      box-shadow: none !important;\r\n    }\r\n\r\n    a {\r\n      &:not(.btn) {\r\n        text-decoration: underline;\r\n      }\r\n    }\r\n\r\n    // Bootstrap specific; comment the following selector out\r\n    //a[href]::after {\r\n    //  content: \" (\" attr(href) \")\";\r\n    //}\r\n\r\n    abbr[title]::after {\r\n      content: \" (\" attr(title) \")\";\r\n    }\r\n\r\n    // Bootstrap specific; comment the following selector out\r\n    //\r\n    // Don't show links that are fragment identifiers,\r\n    // or use the `javascript:` pseudo protocol\r\n    //\r\n\r\n    //a[href^=\"#\"]::after,\r\n    //a[href^=\"javascript:\"]::after {\r\n    // content: \"\";\r\n    //}\r\n\r\n    pre {\r\n      white-space: pre-wrap !important;\r\n    }\r\n    pre,\r\n    blockquote {\r\n      border: $border-width solid $gray-500; // Bootstrap custom code; using `$border-width` instead of 1px\r\n      page-break-inside: avoid;\r\n    }\r\n\r\n    //\r\n    // Printing Tables:\r\n    // http://css-discuss.incutio.com/wiki/Printing_Tables\r\n    //\r\n\r\n    thead {\r\n      display: table-header-group;\r\n    }\r\n\r\n    tr,\r\n    img {\r\n      page-break-inside: avoid;\r\n    }\r\n\r\n    p,\r\n    h2,\r\n    h3 {\r\n      orphans: 3;\r\n      widows: 3;\r\n    }\r\n\r\n    h2,\r\n    h3 {\r\n      page-break-after: avoid;\r\n    }\r\n\r\n    // Bootstrap specific changes start\r\n\r\n    // Specify a size and min-width to make printing closer across browsers.\r\n    // We don't set margin here because it breaks `size` in Chrome. We also\r\n    // don't use `!important` on `size` as it breaks in Chrome.\r\n    @page {\r\n      size: $print-page-size;\r\n    }\r\n    body {\r\n      min-width: $print-body-min-width !important;\r\n    }\r\n    .container {\r\n      min-width: $print-body-min-width !important;\r\n    }\r\n\r\n    // Bootstrap components\r\n    .navbar {\r\n      display: none;\r\n    }\r\n    .badge {\r\n      border: $border-width solid $black;\r\n    }\r\n\r\n    .table {\r\n      border-collapse: collapse !important;\r\n\r\n      td,\r\n      th {\r\n        background-color: $white !important;\r\n      }\r\n    }\r\n\r\n    .table-bordered {\r\n      th,\r\n      td {\r\n        border: 1px solid $gray-300 !important;\r\n      }\r\n    }\r\n\r\n    .table-dark {\r\n      color: inherit;\r\n\r\n      th,\r\n      td,\r\n      thead th,\r\n      tbody + tbody {\r\n        border-color: $table-border-color;\r\n      }\r\n    }\r\n\r\n    .table .thead-dark th {\r\n      color: inherit;\r\n      border-color: $table-border-color;\r\n    }\r\n\r\n    // Bootstrap specific changes end\r\n  }\r\n}\r\n"], "names": [], "sourceRoot": ""}