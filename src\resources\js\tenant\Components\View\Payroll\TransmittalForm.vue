<template>
    <form @submit.prevent="submitData">
        <div class="row">
            <!-- Basic Information -->
            <div class="col-md-6">
                <app-form-group
                    :label="$t('reference_number')"
                    :placeholder="$placeholder('reference_number')"
                    v-model="formData.reference_number"
                    :required="true"
                    :error-message="$errorMessage(errors, 'reference_number')"
                />
            </div>
            <div class="col-md-6">
                <app-form-group
                    :label="$t('department')"
                    :placeholder="$placeholder('department')"
                    v-model="formData.department"
                    :error-message="$errorMessage(errors, 'department')"
                />
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <app-form-group
                    :label="$t('payroll_period_start')"
                    type="date"
                    v-model="formData.payroll_period_start"
                    :error-message="$errorMessage(errors, 'payroll_period_start')"
                />
            </div>
            <div class="col-md-6">
                <app-form-group
                    :label="$t('payroll_period_end')"
                    type="date"
                    v-model="formData.payroll_period_end"
                    :error-message="$errorMessage(errors, 'payroll_period_end')"
                />
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <app-form-group
                    :label="$t('prepared_by')"
                    :placeholder="$placeholder('prepared_by')"
                    v-model="formData.prepared_by"
                    :error-message="$errorMessage(errors, 'prepared_by')"
                />
            </div>
            <div class="col-md-6">
                <app-form-group
                    :label="$t('prepared_by_title')"
                    :placeholder="$placeholder('prepared_by_title')"
                    v-model="formData.prepared_by_title"
                    :error-message="$errorMessage(errors, 'prepared_by_title')"
                />
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <app-form-group
                    :label="$t('notes')"
                    type="textarea"
                    :placeholder="$placeholder('notes')"
                    v-model="formData.notes"
                    :error-message="$errorMessage(errors, 'notes')"
                />
            </div>
        </div>

        <!-- Payslip Selection -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">{{ $t('select_payslips') }}</h6>
                    </div>
                    <div class="card-body">
                        <!-- Payslip Filters -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <app-form-group
                                    :label="$t('search')"
                                    :placeholder="$placeholder('employee_name')"
                                    v-model="payslipFilters.search"
                                    @input="loadPayslips"
                                />
                            </div>
                            <div class="col-md-4">
                                <app-form-group
                                    :label="$t('department')"
                                    :placeholder="$placeholder('department')"
                                    v-model="payslipFilters.department"
                                    @input="loadPayslips"
                                />
                            </div>
                            <div class="col-md-4">
                                <app-form-group
                                    :label="$t('period')"
                                    :placeholder="$placeholder('period')"
                                    v-model="payslipFilters.period"
                                    @input="loadPayslips"
                                />
                            </div>
                        </div>

                        <!-- Payslip List -->
                        <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                            <table class="table table-sm">
                                <thead class="thead-light">
                                    <tr>
                                        <th>
                                            <input
                                                type="checkbox"
                                                @change="toggleAllPayslips"
                                                :checked="allPayslipsSelected"
                                            />
                                        </th>
                                        <th>{{ $t('employee') }}</th>
                                        <th>{{ $t('department') }}</th>
                                        <th>{{ $t('designation') }}</th>
                                        <th>{{ $t('net_salary') }}</th>
                                        <th>{{ $t('period') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="payslip in payslips" :key="payslip.id">
                                        <td>
                                            <input
                                                type="checkbox"
                                                :value="payslip.id"
                                                v-model="formData.payslip_ids"
                                                @change="updateTotals"
                                            />
                                        </td>
                                        <td>{{ payslip.user.full_name }}</td>
                                        <td>{{ payslip.user.department?.name || 'N/A' }}</td>
                                        <td>{{ payslip.user.designation?.name || 'N/A' }}</td>
                                        <td>{{ formatCurrency(payslip.net_salary) }}</td>
                                        <td>{{ payslip.period }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Selection Summary -->
                        <div class="mt-3 p-3 bg-light rounded">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>{{ $t('selected_employees') }}: {{ selectedCount }}</strong>
                                </div>
                                <div class="col-md-6 text-right">
                                    <strong>{{ $t('total_amount') }}: {{ formatCurrency(totalAmount) }}</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="row">
            <div class="col-12">
                <app-submit-button
                    :loading="loading"
                    :label="isEdit ? $t('update') : $t('save')"
                />
                <button
                    type="button"
                    class="btn btn-secondary ml-2"
                    @click="$emit('close-modal')"
                >
                    {{ $t('cancel') }}
                </button>
            </div>
        </div>
    </form>
</template>

<script>
import {TRANSMITTALS, TRANSMITTAL_PAYSLIPS} from "../../../Config/ApiUrl";

export default {
    name: "TransmittalForm",
    props: {
        selectedUrl: String,
        isEdit: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            loading: false,
            errors: {},
            formData: {
                reference_number: '',
                notes: '',
                department: '',
                payroll_period_start: '',
                payroll_period_end: '',
                prepared_by: 'Mae N. Tembreville',
                prepared_by_title: 'Payroll Analyst',
                payslip_ids: []
            },
            payslips: [],
            payslipFilters: {
                search: '',
                department: '',
                period: ''
            },
            selectedCount: 0,
            totalAmount: 0
        };
    },
    computed: {
        allPayslipsSelected() {
            return this.payslips.length > 0 && this.formData.payslip_ids.length === this.payslips.length;
        }
    },
    mounted() {
        this.loadPayslips();
        if (this.isEdit && this.selectedUrl) {
            this.loadTransmittal();
        }
    },
    methods: {
        async loadPayslips() {
            try {
                const response = await this.$http.get(TRANSMITTAL_PAYSLIPS, {
                    params: this.payslipFilters
                });
                this.payslips = response.data.data;
            } catch (error) {
                this.$toastr.e(error.response?.data?.message || 'Error loading payslips');
            }
        },

        async loadTransmittal() {
            try {
                const response = await this.$http.get(this.selectedUrl);
                this.formData = { ...this.formData, ...response.data };
                this.updateTotals();
            } catch (error) {
                this.$toastr.e(error.response?.data?.message || 'Error loading transmittal');
            }
        },

        toggleAllPayslips() {
            if (this.allPayslipsSelected) {
                this.formData.payslip_ids = [];
            } else {
                this.formData.payslip_ids = this.payslips.map(p => p.id);
            }
            this.updateTotals();
        },

        updateTotals() {
            const selectedPayslips = this.payslips.filter(p =>
                this.formData.payslip_ids.includes(p.id)
            );
            this.selectedCount = selectedPayslips.length;
            this.totalAmount = selectedPayslips.reduce((sum, p) => sum + parseFloat(p.net_salary), 0);
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(amount);
        },

        async submitData() {
            this.loading = true;
            this.errors = {};

            try {
                const url = this.isEdit ? this.selectedUrl : TRANSMITTALS;
                const method = this.isEdit ? 'patch' : 'post';

                const response = await this.$http[method](url, this.formData);

                this.$toastr.s(response.data.message);
                this.$emit('reload-table');
                this.$emit('close-modal');
            } catch (error) {
                if (error.response?.status === 422) {
                    this.errors = error.response.data.errors;
                } else {
                    this.$toastr.e(error.response?.data?.message || 'An error occurred');
                }
            } finally {
                this.loading = false;
            }
        }
    }
};
</script>
