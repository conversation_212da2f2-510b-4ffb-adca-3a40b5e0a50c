<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('users')->cascadeOnDelete();//tenant_id
            $table->foreignId('plan_id')->constrained('plans')->cascadeOnDelete();//plan_id
            $table->timestamp('start_date');//start of the subscription
            $table->timestamp('end_date')->nullable();//end of the subscription
            $table->timestamp('renewal_date')->nullable();//renewal date
            $table->integer('status');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
