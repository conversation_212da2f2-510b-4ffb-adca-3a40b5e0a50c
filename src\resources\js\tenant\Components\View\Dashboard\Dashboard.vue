<template>    
    <div class="content-wrapper" :class="{'blocked': showBlockingModal}">
        
        <SubscriptionAlert :days-left="daysLeft"/>

        <app-page-top-section :title="$t('dashboard')">
            <template v-if="isTenantExist || tenant.is_single">
                <div class="d-flex">
                    <app-punch-in-out @open-modal="openPunchInModal" @open-break-time="openBreakTimeModal"/>
                    <button class="btn btn-outline-primary ml-3"
                            v-if="adminSummaryPermissions || employeeStatisticsPermissions || attendancePermissions"
                            @click="isAdmin = !isAdmin">
                        {{ dashboardButtonLabel }}
                    </button>
                </div>
            </template>
        </app-page-top-section>

        <app-employee-dashboard v-if="!isAdmin"/>

        <app-admin-dashboard v-if="isAdmin"/>

        <punch-in-out-modal
            v-if="isModalActive"
            v-model="isModalActive"
            :punch="punchStatus"
            @close="isModalActive = false"/>

        <break-time-start-modal
            v-if="breakTimeModalActive"
            v-model="breakTimeModalActive"
            :break-times="breakTimes"
            :on-break="onBreak"
            :details-id="attendanceDetailsId"
        />

    </div>
</template>

<script>

import AppPunchInOut from "../Attendance/Component/AppPunchInOut";
import PunchInOutModal from "../Attendance/Component/PunchInOutModal";
import BreakTimeStartModal from "../Attendance/Component/BreakTimeStartModal";
import { ACTIVE_SUBSCRIPTION } from "../../../Config/ApiUrl";
import { axiosGet } from "../../../../common/Helper/AxiosHelper";
import SubscriptionAlert from "../../../../common/Components/Helper/SubscriptionAlert";

export default {
    name: "Dashboard",
    components: {BreakTimeStartModal, PunchInOutModal, AppPunchInOut, SubscriptionAlert},
data() {
  return {
    punchStatus: '',
    isModalActive: false,
    isAdmin: this.$isAdmin(),
    breakTimeModalActive: false,
    breakTimes: [],
    attendanceDetailsId: null,
    onBreak: null,
    enable_biometric_device: 0,

    // 🔽 Add these
    tenantMembers: null,
    subscription: null,
    renewalChecker: false,
    daysLeft: null,
    preloader: false,
    error: null,
    showBlockingModal: false,
    beforeUnloadListener: null,
    popStateListener: null
  }
},

    methods: {
        openPunchInModal(value) {
            this.punchStatus = value;
            this.isModalActive = true;
        },
        openBreakTimeModal(value, detailsId, onBreak) {
            this.breakTimes = value;
            this.onBreak = onBreak;
            this.attendanceDetailsId = detailsId;
            this.breakTimeModalActive = true;
        },
        fetchSubscription() {
            this.preloader = true;
            this.error = null;
            
            axiosGet(ACTIVE_SUBSCRIPTION)
                .then(({ data }) => {
                    if (typeof data.data.plan.features === 'string') {
                        data.data.plan.features = JSON.parse(data.data.plan.features);
                    }
                    this.tenantMembers = data.tenant_members;
                    this.subscription = data.data;
                    this.renewalChecker = data.renewal_checker;
                    this.daysLeft = data.days_left;
                    
                    // Show blocking modal if subscription is expired
                    if (data.is_expired) {
                        this.blockNavigation();
                    }
                })
                .catch(error => {
                    this.error = error.response?.data?.message || 'Failed to fetch subscription';
                })
                .finally(() => {
                    this.preloader = false;
                });
         },
        setupNavigationBlocking() {
            // Block browser back/forward navigation
            this.popStateListener = (event) => {
                if (this.showBlockingModal) {
                    history.pushState(null, document.title, location.href);
                }
            };
            window.addEventListener('popstate', this.popStateListener);

            // Block page refresh/close
            this.beforeUnloadListener = (event) => {
                if (this.showBlockingModal) {
                    event.preventDefault();
                    event.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
                    return event.returnValue;
                }
            };
            window.addEventListener('beforeunload', this.beforeUnloadListener);
        },
        cleanupNavigationBlocking() {
            if (this.popStateListener) {
                window.removeEventListener('popstate', this.popStateListener);
            }
            if (this.beforeUnloadListener) {
                window.removeEventListener('beforeunload', this.beforeUnloadListener);
            }
        },
        blockNavigation() {
            this.showBlockingModal = true;
            // Disable body scroll
            document.body.style.overflow = 'hidden';
            // Add class to body for additional styling if needed
            document.body.classList.add('modal-open-blocked');
        },
        unblockNavigation() {
            this.showBlockingModal = false;
            // Re-enable body scroll
            document.body.style.overflow = '';
            document.body.classList.remove('modal-open-blocked');
        },
        navigateToSubscription() {
            window.location.href = '/hris/app/settings?tab=subscription';
        }
    },
    computed: {
        isTenantExist() {
            return !!window.tenant && !window.tenant.is_single;
        },
        tenant() {
            return window.tenant || {};
        },
        dashboardButtonLabel() {
            return this.isAdmin ? this.$t('view_as_employee')
                : (this.$isAdmin() ? this.$t('view_as_admin') : this.$t('view_as_manager'));
        },
        adminSummaryPermissions() {
            return this.$can('view_employees') ||
                this.$can('view_departments') || this.$can('view_all_leaves')
        },
        employeeStatisticsPermissions() {
            return this.$can('view_employment_statuses') ||
                this.$can('view_designations') || this.$can('view_departments')
        },
        attendancePermissions() {
            return this.$can('view_all_attendance')
        }
    },
    created() {
        this.fetchSubscription();
    },
}
</script>

