<?php

namespace App\Jobs\Tenant;

use App\Models\Tenant\Export\ModuleExport;
use App\Notifications\Tenant\ExportCompleteNotification;
use App\Repositories\Core\Status\StatusRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class AllDataExportCompleted implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int|null $module_export_id;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($module_export_id = null)
    {
        $this->module_export_id = $module_export_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // update status completed
        if($this->module_export_id) {
            $module_export = ModuleExport::query()->find($this->module_export_id);
            if($module_export) {
                $module_export->update([
                    'status_id' => resolve(StatusRepository::class)->exportCompleted(),
                    'completed_on' => now(),
                ]);
                // notify user
                notify()
                    ->on('export_complete')
                    ->mergeAudiences($module_export->created_by)
                    ->send(ExportCompleteNotification::class);
            }
        }
        Log::info('Export completed: all data');
    }
}
