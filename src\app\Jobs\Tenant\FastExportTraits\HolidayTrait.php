<?php

namespace App\Jobs\Tenant\FastExportTraits;

use App\Helpers\Traits\DateTimeHelper;
use App\Models\Tenant\Holiday\Holiday;

trait HolidayTrait
{
    use DateTimeHelper;

    // Define Holiday generator function
    public function HolidayDataGenerator(): \Generator
    {
        foreach (
            Holiday::query()
                ->select(['name', 'start_date', 'end_date'])
                ->cursor() as $holiday) {
            $holiday->sheet_name = 'holiday';
            yield $holiday;
        }
    }


    // Define Holiday map function
    public function holidayMap($row): array
    {
        return [
            'Title' => $row->name,
            'Type' => $this->getLeaveType($row),
            'Start_date' => $this->getHolidayStartDate($row),
            'End_date' => $this->getHolidayEndDate($row),
        ];
    }

    // mapping helper functions
    private function getHolidayStartDate($holiday)
    {
        if (isset($holiday->start_date)) {
            return $this->carbon($holiday->start_date . ' 00:00:00')->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }

    private function getHolidayEndDate($holiday)
    {
        if (isset($holiday->end_date)) {
            return $this->carbon($holiday->end_date . ' 23:59:59')->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }

    private function getLeaveType($holiday)
    {
        if (isset($holiday->start_date) && isset($holiday->end_date)) {
            if ($this->carbon($holiday->start_date)->parse()->isSameDay($holiday->end_date)) {
                return 'single_day';
            }
            return 'multi_day';
        }
        return '';
    }
}