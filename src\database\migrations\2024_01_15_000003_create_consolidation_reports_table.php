<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('consolidation_reports', function (Blueprint $table) {
            $table->id();
            $table->enum('report_type', ['summary', 'detailed', 'departmental'])->default('summary');
            $table->date('period_start');
            $table->date('period_end');
            $table->json('department_ids')->nullable();
            $table->integer('total_employees')->default(0);
            $table->decimal('total_amount', 15, 2)->default(0);
            $table->integer('total_departments')->default(0);
            $table->json('report_data');
            $table->unsignedBigInteger('generated_by');
            $table->unsignedBigInteger('status_id');
            $table->string('file_path')->nullable();
            $table->string('file_url')->nullable();
            $table->text('notes')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('generated_by')->references('id')->on('users')->onDelete('restrict');
            $table->foreign('status_id')->references('id')->on('statuses')->onDelete('restrict');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');

            // Indexes
            $table->index('report_type');
            $table->index(['period_start', 'period_end']);
            $table->index('generated_by');
            $table->index('status_id');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('consolidation_reports');
    }
};
