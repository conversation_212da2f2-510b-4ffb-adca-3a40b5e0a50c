<?php

namespace App\Jobs\Tenant\FastExportTraits;

use App\Helpers\Traits\DateTimeHelper;
use App\Models\Core\Auth\User;
use Illuminate\Support\Facades\DB;

trait EmployeeTrait
{
    use DateTimeHelper;
    // Define Employee generator function
    public function EmployeeDataGenerator(): \Generator
    {
        foreach (User::query()
                     ->select('users.id', 'email', 'first_name', 'last_name', 'profiles.contact', 'profiles.address as address', 'profiles.joining_date as joining_date')
                     ->selectSub(function ($query) {
                         $query->select('name')
                             ->from('statuses')
                             ->whereColumn('statuses.id', 'users.status_id')
                             ->limit(1);
                     }, 'status')
                     ->selectSub(function ($query) {
                         $query->select('name')
                             ->from(DB::raw('(SELECT id, name FROM departments) as departments'))
                             ->leftJoin('department_user', 'departments.id', '=', 'department_user.department_id')
                             ->whereColumn('users.id', 'department_user.user_id')
                             ->orderBy('department_user.start_date', 'desc')
                             ->limit(1);
                     }, 'department')
                     ->selectSub(function ($query) {
                         $query->select('manager_id')
                             ->from(DB::raw('(SELECT id, manager_id FROM departments) as departments'))
                             ->leftJoin('department_user', 'departments.id', '=', 'department_user.department_id')
                             ->whereColumn('users.id', 'department_user.user_id')
                             ->orderBy('department_user.start_date', 'desc')
                             ->limit(1);
                     }, 'manager_id')
                     ->selectSub(function ($query) {
                         $query->select('parent_department.name')
                             ->from('departments as department')
                             ->leftJoin('departments as parent_department', 'department.department_id', '=', 'parent_department.id')
                             ->leftJoin('department_user', 'department.id', '=', 'department_user.department_id')
                             ->whereColumn('users.id', 'department_user.user_id')
                             ->orderBy('department_user.start_date', 'desc')
                             ->limit(1);
                     }, 'parent_department')
//                     ->selectSub(function ($query) {
//                         $query->select('name')
//                             ->from(DB::raw('(SELECT id, name FROM working_shifts) as working_shifts'))
//                             ->leftJoin('working_shift_user', 'working_shifts.id', '=', 'working_shift_user.working_shift_id')
//                             ->whereColumn('users.id', 'working_shift_user.user_id')
//                             ->whereNull('working_shift_user.end_date')
//                             ->limit(1);
//                     }, 'working_shift')
                     ->selectSub(function ($query) {
                         $query->select('name')
                             ->from(DB::raw('(SELECT id, name FROM employment_statuses) as employment_statuses'))
                             ->leftJoin('user_employment_status', 'employment_statuses.id', '=', 'user_employment_status.employment_status_id')
                             ->whereColumn('users.id', 'user_employment_status.user_id')
                             ->whereNull('user_employment_status.end_date')
                             ->limit(1);
                     }, 'employment_status')
                     ->selectSub(function ($query) {
                         $query->select('user_employment_status.start_date')
                             ->from(DB::raw('(SELECT id, name FROM employment_statuses) as employment_statuses'))
                             ->leftJoin('user_employment_status', 'employment_statuses.id', '=', 'user_employment_status.employment_status_id')
                             ->whereColumn('users.id', 'user_employment_status.user_id')
                             ->whereNull('user_employment_status.end_date')
                             ->limit(1);
                     }, 'employment_status_start_date')
                     ->selectSub(function ($query) {
                         $query->select('name')
                             ->from(DB::raw('(SELECT id, name FROM designations) as designations'))
                             ->leftJoin('designation_user', 'designations.id', '=', 'designation_user.designation_id')
                             ->whereColumn('users.id', 'designation_user.user_id')
                             ->whereNull('designation_user.end_date')
                             ->limit(1);
                     }, 'designation')
                     ->leftJoin('profiles', 'users.id', '=', 'profiles.user_id')
                     ->cursor() as $user) {
            $user->sheet_name = 'employee';
            yield $user;
        }
    }

    // Define Employee function
    public function employeeMap($row): array {
        return [
            'Email' => $row->email,
            'First_name' => $row->first_name,
            'Last_name' => $row->last_name,
            'Department_name' => $row->department,
            'Is_department_manager' => $this->getManagerInfo($row),
            'Parent_department' => $row->parent_department,
//            'WorkShift' => $row->working_shift ?? ($this->defaultWorkingShift?->name ?? ''),
            'Join_date' => $this->getJoiningDate($row),
            'Employment_status' => $row->employment_status,
            'Designation' => $row->designation,
            'User_status' => __t($row->status),
            'termination_date' => $row->employment_status == 'Terminated' ? $this->getTerminationDate($row) : '',
            'Personal_number' => $row->contact ?? '',
            'Address' => $row->address ?? '',
        ];
    }

    // mapping helper functions
    private function getManagerInfo($user): int
    {
        if (isset($user->id) && isset($user->manager_id)) {
            return $user->id == $user->manager_id ? 1 : 0;
        }
        return 0;
    }
    private function getTerminationDate($employee)
    {
        if (isset($employee->employment_status_start_date)) {
            return $this->carbon($employee->employment_status_start_date . ' 00:00:00')->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
    private function getJoiningDate($employee)
    {
        if (isset($employee->joining_date)) {
            return $this->carbon($employee->joining_date . ' 00:00:00')->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
}