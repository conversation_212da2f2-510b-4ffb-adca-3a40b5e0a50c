<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class PlanSubscriptionRequest extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function build()
    {
        $subject = "New Plan Subscription Request - " . ($this->data['plan']['plan_name'] ?? 'New Plan');
        
        $message = view('emails.plan-subscription-request', [
            'data' => $this->data
        ])->render();

        return $this->subject($subject)
                   ->view('notification.mail.template', [
                       'template' => [
                           'subject' => $subject,
                           'parsed' => $message,
                           'parsed_subject' => $subject
                       ]
                   ]);
    }
}