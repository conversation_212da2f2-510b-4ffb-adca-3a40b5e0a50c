<?php

namespace App\Services\Tenant\Payroll;

use App\Models\Tenant\Payroll\Payrun;
use App\Models\Tenant\Payroll\Payslip;
use App\Models\Tenant\Payroll\Transmittal;
use App\Models\Tenant\WorkingShift\Department;
use Illuminate\Support\Facades\DB;

class TransmittalService
{
    /**
     * Generate transmittals from payrun
     */
    public function generateFromPayrun(Payrun $payrun, array $options = [])
    {
        $departmentId = $options['department_id'] ?? null;
        $employeeIds = $options['employee_ids'] ?? null;

        DB::beginTransaction();

        try {
            $payslipsQuery = $payrun->payslips()->with(['user.department', 'user.designation']);

            // Filter by department if specified
            if ($departmentId) {
                $payslipsQuery->whereHas('user.department', function ($q) use ($departmentId) {
                    $q->where('id', $departmentId);
                });
            }

            // Filter by specific employees if specified
            if ($employeeIds) {
                $payslipsQuery->whereIn('user_id', $employeeIds);
            }

            $payslips = $payslipsQuery->get();

            if ($payslips->isEmpty()) {
                throw new \Exception('No payslips found for the specified criteria');
            }

            // Group payslips by department
            $groupedPayslips = $departmentId 
                ? [$departmentId => $payslips]
                : $payslips->groupBy('user.department.id');

            $transmittals = [];

            foreach ($groupedPayslips as $deptId => $departmentPayslips) {
                $department = $departmentPayslips->first()->user->department;
                $totalAmount = $departmentPayslips->sum('net_salary');

                $transmittal = Transmittal::create([
                    'department_id' => $deptId,
                    'payrun_id' => $payrun->id,
                    'period_start' => $departmentPayslips->min('start_date'),
                    'period_end' => $departmentPayslips->max('end_date'),
                    'total_amount' => $totalAmount,
                    'payslip_count' => $departmentPayslips->count(),
                    'status_id' => $this->getGeneratedStatusId(),
                    'generated_by' => auth()->id(),
                    'reference_number' => $this->generateReferenceNumber(),
                    'metadata' => [
                        'payrun_name' => $payrun->name,
                        'department_name' => $department->name ?? 'Mixed Departments',
                        'employee_count' => $departmentPayslips->unique('user_id')->count()
                    ]
                ]);

                // Attach payslips to the transmittal
                $transmittal->payslips()->attach($departmentPayslips->pluck('id'));

                $transmittals[] = $transmittal;
            }

            DB::commit();

            return [
                'success' => true,
                'transmittals' => $transmittals,
                'count' => count($transmittals),
                'total_amount' => collect($transmittals)->sum('total_amount')
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get payslips for transmittal export
     */
    public function getPayslipsForExport(array $transmittalIds)
    {
        $transmittals = Transmittal::with(['department', 'payrun', 'payslips.user.department', 'payslips.user.designation'])
            ->whereIn('id', $transmittalIds)
            ->get();

        $payslipsForExport = collect();
        
        foreach ($transmittals as $transmittal) {
            foreach ($transmittal->payslips as $payslip) {
                $payslipsForExport->push((object)[
                    'employee' => $payslip->user,
                    'period_start' => $transmittal->period_start,
                    'period_end' => $transmittal->period_end,
                    'total_amount' => $payslip->net_salary,
                    'reference_number' => $transmittal->reference_number,
                    'department' => $transmittal->department,
                    'payrun' => $transmittal->payrun
                ]);
            }
        }

        return $payslipsForExport;
    }

    /**
     * Generate unique reference number
     */
    private function generateReferenceNumber()
    {
        $date = date('Ymd');
        $count = Transmittal::whereDate('created_at', today())->count() + 1;
        return 'TXN-' . $date . '-' . str_pad($count, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get the status ID for generated transmittals
     */
    private function getGeneratedStatusId()
    {
        // You might want to create a specific status for transmittals
        // For now, using status ID 1 (assuming it's an active/generated status)
        return 1;
    }

    /**
     * Get available payruns for transmittal generation
     */
    public function getAvailablePayruns()
    {
        return Payrun::with(['payslips'])
            ->whereHas('payslips')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get departments with payslips in a payrun
     */
    public function getDepartmentsWithPayslips(Payrun $payrun)
    {
        return Department::whereHas('users.payslips', function ($q) use ($payrun) {
            $q->where('payrun_id', $payrun->id);
        })->get();
    }
}
