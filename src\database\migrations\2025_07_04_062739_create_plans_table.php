<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plans', function (Blueprint $table) {
            $table->id();
            $table->string('plan_name');  // per month, yearly, lifetime
            $table->string('plan_type');
            $table->string('plan_description');
            $table->json('features')->nullable(); // JSON column for features
            $table->integer('employee_limit'); // 50, 100, 200, 500
            $table->decimal('price', 10, 2);// 0, 199, 499, 999
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plans');
    }
};
