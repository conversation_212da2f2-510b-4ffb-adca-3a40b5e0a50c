<?php

namespace App\Exports;

use App\Models\Tenant\Export\ModuleExport;
use App\Notifications\Tenant\ExportFailedNotification;
use App\Repositories\Core\Status\StatusRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Events\BeforeExport;

class AllDataExport implements ShouldQueue, WithEvents, WithMultipleSheets
{
    use Exportable, RegistersEventListeners;
    protected array $requested_sheets;
    protected int|null $module_export_id;

    public function __construct($requested_sheets = [], $module_export_id = null)
    {
        $this->requested_sheets = $requested_sheets;
        $this->module_export_id = $module_export_id;
    }

    public static function beforeExport(BeforeExport $event)
    {
        // Example event listener, you can define additional events if needed
        // update status processing
        if($event->getConcernable()?->module_export_id) {
            $module_export = ModuleExport::query()->find($event->getConcernable()->module_export_id);
            if($module_export) {
                $module_export->update([
                    'status_id' => resolve(StatusRepository::class)->exportProcessing(),
                ]);
            }
        }
        Log::info('All data: beforeExport');
    }
    public function failed(\Throwable $exception): void
    {
        // handle failed export
        if($this->module_export_id) {
            $module_export = ModuleExport::query()->find($this->module_export_id);
            if($module_export) {
                // update status rejected
                $module_export->update([
                    'status_id' => resolve(StatusRepository::class)->exportRejected(),
                    'data' => [
                        'message' => 'Export failed.'
                    ],
                ]);
                // notify user failed
                notify()
                    ->on('export_failed')
                    ->mergeAudiences($module_export->created_by)
                    ->send(ExportFailedNotification::class);
            }
        }
        Log::info('all data: failed');
    }

    public static function afterSheet(AfterSheet $event)
    {
        // Example event listener, you can define additional events if needed
        Log::info('All data: After sheet');
    }

    public function sheets(): array
    {
        $sheets = [];

        if (in_array('employee', $this->requested_sheets)) {
            $sheets[] = new EmployeeModuleExport($this->module_export_id);
        }
        if (in_array('leave', $this->requested_sheets)) {
            $sheets[] = new LeaveModuleExport($this->module_export_id);
        }
        if (in_array('attendance', $this->requested_sheets)) {
            $sheets[] = new AttendanceModuleExport($this->module_export_id);
        }
        if (in_array('work_shift', $this->requested_sheets)) {
            $sheets[] = new WorkingShiftModuleExport($this->module_export_id);
        }
        return $sheets;
    }
}
