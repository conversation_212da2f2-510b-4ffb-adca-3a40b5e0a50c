<?php

namespace App\Models\Tenant\Payroll;

use App\Models\Core\Auth\User;
use App\Models\Core\Status;
use App\Models\Core\Traits\StatusRelationship;
use App\Models\Tenant\TenantModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class Signature extends TenantModel
{
    use HasFactory, StatusRelationship;

    protected $fillable = [
        'name',
        'position',
        'department',
        'type',
        'signature_path',
        'signature_url',
        'is_default',
        'is_active',
        'metadata',
        'status_id',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'is_default' => 'boolean',
        'is_active' => 'boolean',
        'metadata' => 'array'
    ];

    protected $appends = [
        'type_label',
        'status_badge',
        'full_signature_url'
    ];

    /**
     * Get the user who created the signature
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated the signature
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the status of the signature
     */
    public function status(): BelongsTo
    {
        return $this->belongsTo(Status::class);
    }

    /**
     * Get type label attribute
     */
    public function getTypeLabelAttribute(): string
    {
        return match($this->type) {
            'digital' => 'Digital Signature',
            'manual' => 'Manual Signature',
            'stamp' => 'Official Stamp',
            default => 'Unknown'
        };
    }

    /**
     * Get status badge attribute
     */
    public function getStatusBadgeAttribute(): array
    {
        $status = $this->status;
        return [
            'name' => $status->name ?? 'unknown',
            'class' => $status->class ?? 'secondary',
            'translated_name' => $status->translated_name ?? 'Unknown'
        ];
    }

    /**
     * Get full signature URL attribute
     */
    public function getFullSignatureUrlAttribute(): ?string
    {
        if (!$this->signature_path) {
            return null;
        }

        if (filter_var($this->signature_url, FILTER_VALIDATE_URL)) {
            return $this->signature_url;
        }

        return Storage::url($this->signature_path);
    }

    /**
     * Scope for active signatures
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for default signature
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope for filtering by type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for filtering by department
     */
    public function scopeForDepartment($query, $department)
    {
        return $query->where('department', $department);
    }

    /**
     * Check if signature has file
     */
    public function hasFile(): bool
    {
        return !empty($this->signature_path) && Storage::disk('public')->exists($this->signature_path);
    }

    /**
     * Delete signature file
     */
    public function deleteFile(): bool
    {
        if ($this->hasFile()) {
            return Storage::disk('public')->delete($this->signature_path);
        }
        return true;
    }

    /**
     * Set as default signature
     */
    public function setAsDefault(): void
    {
        // Unset all other defaults
        static::where('is_default', true)->update(['is_default' => false]);
        
        // Set this as default
        $this->update(['is_default' => true]);
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($signature) {
            $signature->deleteFile();
        });

        static::updating(function ($signature) {
            if ($signature->isDirty('is_default') && $signature->is_default) {
                // Unset all other defaults
                static::where('id', '!=', $signature->id)
                      ->where('is_default', true)
                      ->update(['is_default' => false]);
            }
        });
    }
}
