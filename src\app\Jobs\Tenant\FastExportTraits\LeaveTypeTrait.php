<?php

namespace App\Jobs\Tenant\FastExportTraits;

use App\Helpers\Traits\DateTimeHelper;
use App\Models\Tenant\Leave\LeaveType;

trait LeaveTypeTrait
{
    use DateTimeHelper;

    // Define LeaveType generator function
    public function LeaveTypeDataGenerator(): \Generator
    {
        foreach (
            LeaveType::query()
                ->select(['name', 'amount', 'is_enabled', 'is_earning_enabled', 'type'])
                ->cursor() as $leave) {
            $leave->sheet_name = 'leave_type';
            yield $leave;
        }
    }


    // Define LeaveType map function
    public function leaveTypeMap($row, $paidStatuses = '', $unpaidStatuses = ''): array
    {
        return [
            'Name' => $row->name,
            'Calculate_allowance_by' => 'no_of_days',
            'Number_of_days' => $row->amount,
            'Apply_leaves_to' => 'employment_status',
            'Employment_status' => $row->type == 'paid' ? $paidStatuses : $unpaidStatuses,
            'Is_earned' => $row->is_earning_enabled ? 'true' : 'false',
            'Is_enable' => $row->is_enabled ? 'true' : 'false',
        ];
    }

}