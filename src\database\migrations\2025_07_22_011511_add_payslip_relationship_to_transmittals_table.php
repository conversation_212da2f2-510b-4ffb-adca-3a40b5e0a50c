<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create a pivot table to link transmittals with payslips
        if (!Schema::hasTable('transmittal_payslips')) {
            Schema::create('transmittal_payslips', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('transmittal_id');
                $table->unsignedBigInteger('payslip_id');
                $table->timestamps();

                // Foreign key constraints
                $table->foreign('transmittal_id')->references('id')->on('transmittals')->onDelete('cascade');
                $table->foreign('payslip_id')->references('id')->on('payslips')->onDelete('cascade');

                // Unique constraint to prevent duplicate entries
                $table->unique(['transmittal_id', 'payslip_id']);

                // Indexes
                $table->index('transmittal_id');
                $table->index('payslip_id');
            });
        }

        // Modify transmittals table to add new columns for payslip-based structure
        Schema::table('transmittals', function (Blueprint $table) {
            // Add department_id to group transmittals by department
            if (!Schema::hasColumn('transmittals', 'department_id')) {
                $table->unsignedBigInteger('department_id')->nullable()->after('id');
                $table->foreign('department_id')->references('id')->on('departments')->onDelete('set null');
                $table->index('department_id');
            }

            // Add payrun_id to link transmittals to specific payruns
            if (!Schema::hasColumn('transmittals', 'payrun_id')) {
                $table->unsignedBigInteger('payrun_id')->nullable()->after('department_id');
                $table->foreign('payrun_id')->references('id')->on('payruns')->onDelete('set null');
                $table->index('payrun_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the pivot table
        Schema::dropIfExists('transmittal_payslips');

        // Restore transmittals table structure
        Schema::table('transmittals', function (Blueprint $table) {
            // Drop new columns
            $table->dropForeign(['department_id']);
            $table->dropForeign(['payrun_id']);
            $table->dropColumn(['department_id', 'payrun_id']);

            // Restore employee_id
            $table->unsignedBigInteger('employee_id')->after('id');
            $table->foreign('employee_id')->references('id')->on('users')->onDelete('cascade');

            // Restore original indexes
            $table->dropIndex(['department_id', 'period_start', 'period_end']);
            $table->dropIndex(['payrun_id']);
            $table->index(['employee_id', 'period_start', 'period_end']);
        });
    }
};
