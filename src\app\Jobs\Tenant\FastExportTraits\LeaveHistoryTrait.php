<?php

namespace App\Jobs\Tenant\FastExportTraits;

use App\Helpers\Traits\DateTimeHelper;
use App\Models\Tenant\Leave\UserLeave;

trait LeaveHistoryTrait
{
    use DateTimeHelper;
    // Define DepartmentHistory generator function
    public function LeaveHistoryDataGenerator(): \Generator
    {
        foreach (
            UserLeave::query()
                ->select(['start_date', 'end_date', 'amount'])
                ->selectSub(function ($query) {
                    $query->select('name')
                        ->from('leave_types')
                        ->whereColumn('leave_types.id', 'user_leaves.leave_type_id')
                        ->limit(1);
                }, 'leave_type')
                ->selectSub(function ($query) {
                    $query->select('email')
                        ->from('users')
                        ->whereColumn('users.id', 'user_leaves.user_id')
                        ->limit(1);
                }, 'user')
                ->cursor() as $row) {
            $row->sheet_name = 'leave_status_history';
            yield $row;
        }
    }

    // Define DepartmentHistory map function
    public function leaveHistoryMap($row): array {
        return [
            'User' => $row->user,
            'Leave_type' => $row->leave_type,
            'Balance' => $row->amount ? number_format($row->amount, 2, '.', "") : 0.00,
            'Start_date' => $this->getLeaveHistoryStartDate($row),
            'End_date' => $this->getLeaveHistoryEndDate($row),
        ];
    }

    // mapping helper functions
    private function getLeaveHistoryStartDate($row) {
        if(isset($row->start_date)) {
            return $this->carbon($row->start_date)->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
    private function getLeaveHistoryEndDate($row) {
        if(isset($row->end_date)) {
            return $this->carbon($row->end_date)->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
}