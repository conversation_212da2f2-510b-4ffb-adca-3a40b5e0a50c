<template>
    <div class="content-wrapper" v-if="!loading">
        <app-page-top-section :title="$t('org_structure')" >
            <app-default-button
                :title="$t('Export')"
                @click="exportToImage"
                :disabled="isExporting"
                btn-class="btn btn-secondary"
            />
            <app-default-button
                v-if="$can('create_department')"
                :title="$addLabel('department')"
                @click="openDepartmentModal()"
            />
        </app-page-top-section>
        <div class="card card-with-shadow border border-0 custom-scrollbar" ref="chartContainer">
            <app-organization-chart 
                :chart-data="chartData" 
                :druggable="true" 
                :height="dynamicHeight"
                @selected-item="selectedItem"
                ref="orgChart"
            >
            </app-organization-chart>
        </div>

        <app-department-modal
            v-if="isAddEditModalActive"
            v-model="isAddEditModalActive"
            :selected-url="selectedUrl"
        />
        <department-employee
            v-if="isDepartmentEmployeeModalActive"
            v-model="isDepartmentEmployeeModalActive"
            :department="selectedDepartment"
        />
    </div>
    <app-overlay-loader v-else />
</template>

<script>
import {axiosGet} from "../../../../../common/Helper/AxiosHelper";
import {organizeOrganizationStructure} from "./Helper";
import DepartmentEmployee from "./DepartmentEmployee"
import html2canvas from 'html2canvas';

export default {
    name: "OrganizationStructure",
    components: {DepartmentEmployee},
    data() {
        return {
            chartData: {},
            loading: true,
            isAddEditModalActive: false,
            selectedUrl: '',
            isDepartmentEmployeeModalActive: false,
            selectedDepartment: {},
            isExporting: false,
            dynamicHeight: 760
        }
    },
    methods: {
        getOrganizationStructure() {
            axiosGet(this.apiUrl.ORGANIZATION_STRUCTURE).then(response => {
                const organizedData = organizeOrganizationStructure(response.data);
                this.chartData = organizedData;
                // Calculate dynamic height after data is loaded
                this.$nextTick(() => {
                    this.calculateDynamicHeight();
                });
            }).catch(error => {
                console.error('Error fetching organization structure:', error);
            }).finally(() => {
                this.loading = false;
            });
        },
        calculateDynamicHeight() {
            // Wait for the chart to render
            this.$nextTick(() => {
                const orgChartContainer = document.querySelector('.orgchart-container');
                if (orgChartContainer) {
                    // Get the actual content height
                    const contentHeight = orgChartContainer.scrollHeight;
                    // Add some padding (100px) to ensure full visibility
                    this.dynamicHeight = Math.max(contentHeight + 100, 760);
                    
                    // Update the container height
                    this.$nextTick(() => {
                        $(orgChartContainer).css({
                            "height": this.dynamicHeight + "px"
                        });
                    });
                }
            });
        },
        selectedItem(current) {
            this.isDepartmentEmployeeModalActive = true;
            this.selectedDepartment = current;
        },
        openDepartmentModal() {
            this.selectedUrl = '';
            this.isAddEditModalActive = true;
        },
        async exportToImage() {
            this.isExporting = true;
            
            try {
                // First, ensure the container height is adequate
                await this.prepareForExport();
                
                const orgChartContainer = document.querySelector('.orgchart-container');
                if (!orgChartContainer) {
                    throw new Error('Organization chart container not found');
                }

                // Get the actual dimensions of the content
                const rect = orgChartContainer.getBoundingClientRect();
                const scrollHeight = orgChartContainer.scrollHeight;
                const scrollWidth = orgChartContainer.scrollWidth;

                const canvas = await html2canvas(orgChartContainer, {
                    scale: 2, // High resolution
                    useCORS: true,
                    logging: false,
                    backgroundColor: '#ffffff',
                    width: scrollWidth,
                    height: scrollHeight,
                    scrollX: 0,
                    scrollY: 0,
                    windowWidth: scrollWidth,
                    windowHeight: scrollHeight,
                    onclone: (clonedDoc) => {
                        // Ensure the cloned container has the right dimensions
                        const clonedContainer = clonedDoc.querySelector('.orgchart-container');
                        if (clonedContainer) {
                            clonedContainer.style.height = scrollHeight + 'px';
                            clonedContainer.style.width = scrollWidth + 'px';
                            clonedContainer.style.overflow = 'visible';
                            
                            // Ensure manager names are black in the exported image
                            const managerNames = clonedContainer.querySelectorAll('.manager-name');
                            managerNames.forEach(name => {
                                name.style.color = '#000000';
                                // Also ensure text color is forced for any nested elements
                                const spans = name.querySelectorAll('span');
                                spans.forEach(span => {
                                    span.style.color = '#000000';
                                });
                            });
                        }
                    }
                });

                // Create download link
                const link = document.createElement('a');
                link.download = `organization-chart-${new Date().toISOString().split('T')[0]}.png`;
                link.href = canvas.toDataURL('image/png', 1.0);
                link.click();
                
                // Show success message
                this.$toastr.s('Organization chart ready for export!', 'Success');
                
            } catch (error) {
                console.error('Export failed:', error);
                this.$toastr.e('Failed to export organization chart', 'Error');
            } finally {
                this.isExporting = false;
            }
        },
        async prepareForExport() {
            return new Promise((resolve, reject) => {
                try {
                    // Temporarily remove height constraints and show all content
                    const orgChartContainer = document.querySelector('.orgchart-container');
                    if (!orgChartContainer) {
                        throw new Error('Organization chart container not found');
                    }
                    
                    // Store original styles
                    const originalHeight = orgChartContainer.style.height;
                    const originalOverflow = orgChartContainer.style.overflow;
                    
                    // Set to auto height to show all content
                    orgChartContainer.style.height = 'auto';
                    orgChartContainer.style.overflow = 'visible';
                    
                    // Wait for layout to settle
                    setTimeout(() => {
                        try {
                            // Calculate the full content height
                            const fullHeight = orgChartContainer.scrollHeight;
                            
                            // Set the height to accommodate all content
                            orgChartContainer.style.height = `${fullHeight}px`;
                            
                            // Wait for the next tick to ensure the height is applied
                            this.$nextTick(() => {
                                // Restore original styles after a short delay
                                setTimeout(() => {
                                    try {
                                        orgChartContainer.style.height = originalHeight;
                                        orgChartContainer.style.overflow = originalOverflow;
                                        
                                        // Resolve the promise to continue with export
                                        resolve();
                                    } catch (error) {
                                        console.error('Error restoring styles:', error);
                                        reject(error);
                                    }
                                }, 500);
                            });
                        } catch (error) {
                            console.error('Error in prepareForExport timeout:', error);
                            orgChartContainer.style.height = originalHeight;
                            orgChartContainer.style.overflow = originalOverflow;
                            reject(error);
                        }
                    }, 100);
                } catch (error) {
                    console.error('Error in prepareForExport:', error);
                    reject(error);
                }
            });
        }
    },
    watch: {
        isAddEditModalActive: {
            handler: function (value) {
                if (!value) {
                    this.getOrganizationStructure();
                }
            },
            immediate: true
        }
    },
    mounted() {
        // Recalculate height on window resize
        window.addEventListener('resize', this.calculateDynamicHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.calculateDynamicHeight);
    }
}
</script>

<style scoped>
.custom-scrollbar {
    overflow: auto;
}
:deep(.orgchart-container) {
    min-height: 760px;
    overflow: auto;
}
:deep(.orgchart) {
    padding: 20px;
}
</style>