import {pick} from "lodash";
import optional from "../../../../../common/Helper/Support/Optional";

export const organizeOrganizationStructure = data => {
    let chart = formatDepartment(data);
    chart.children = chart.child_departments.map(department => {
        return organizeChildren(department);
    });
    return chart;
}

const organizeChildren = department => {
    department.children = department.child_departments.map(dept => {
        return organizeChildren(dept);
    });
    const formatted = formatDepartment(department);
    return formatted;
}

const formatDepartment = data => {
    // Keep the manager object in the picked data
    const object = pick(data, ['id', 'name', 'manager', 'child_departments', 'children']);
    object.title = getTitle(object);
    object.avatar = getAvatar(object);
    
    // Don't delete the manager object - the component needs it for profile pictures
    // delete object.manager; // This line was removed
    
    return object;
}

const getAvatar = department => {
    const profilePic = department.manager?.profile_picture;
    //funtion is used to get the managers profile picture url
    if (profilePic) {
        if (profilePic.full_url) {
            return profilePic.full_url;
        }
        
        // If only path is available, construct the URL
        if (profilePic.path) {
            const path = profilePic.path.startsWith('avatar/') 
                ? profilePic.path 
                : `avatar/${profilePic.path}`;
            return `${window.location.origin}/storage/${path}`;
        }
    }
    
    // If no profile picture is available, return null to trigger the initials fallback
    return null;
}

const getTitle = department => {
    return optional(department, 'manager', 'full_name');
}