<?php

namespace Database\Seeders;

use App\Models\Tenant\Plans;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PlansTableSeeder extends Seeder
{
    public function run(): void
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Plans::truncate();

        $plans = [
            [
                'plan_name' => 'Monthly Plan',
                'plan_type' => 'Subscription',
                'plan_description' => 'Ideal for testing out Core HRM without a long-term commitment.',
                'features' => json_encode([
                    'Full access to all HR modules',
                    'Access to training and system guides',
                    'Technical/Customer Support',
                    'Custom reports',
                ]),
                'employee_limit' => 70,
                'price' => 70.00, // Monthly per employee
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'plan_name' => 'Yearly Plan',
                'plan_type' => 'Subscription',
                'plan_description' => 'All-in-one HR suite built for long-term efficiency and productivity.',
                'features' => json_encode([
                    'Full access to all HR modules',
                    'Access to training and system guides',
                    'Technical/Customer Support',
                    'Custom reports',
                ]),
                'employee_limit' => 250,
                'price' => 840.00, // Yearly per employee
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'plan_name' => 'Premium Plan',
                'plan_type' => 'One-time Payment',
                'plan_description' => 'For organizations looking for a long-term HR solution with zero recurring fees.',
                'features' => json_encode([
                    'Lifetime access',
                    'One-time setup and onboarding support',
                    'Unlimited employee records',
                    'Tailored-fit to your business',
                ]),
                'employee_limit' => 1000,
                'price' => 0.00,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        Plans::insert($plans);

        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $this->command->info('Updated plans from pricing page have been seeded!');
    }
}
