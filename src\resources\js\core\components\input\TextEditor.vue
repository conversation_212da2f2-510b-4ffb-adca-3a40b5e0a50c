<template>
    <div class="editor"
         :class="{'disabled':data.disabled}">
        <textarea
            :id="inputFieldId"
            :name="name"
        />
    </div>
</template>

<script>
import {InputMixin} from './mixin/InputMixin.js';

export default {
    name: "TextEditor",
    mixins: [InputMixin],
    mounted() {
        this.waitForSummernote();
    },
    watch: {
        value: {
            handler(newVal) {
                if (this.fieldValue !== newVal) {
                    this.setInitValue(newVal);
                }
            },
            deep: true
        }
    },
    computed: {
        editorValue() {
            return this.value;
        }
    },
    methods: {
        waitForSummernote() {
            if (typeof $.fn.summernote === 'undefined') {
                setTimeout(() => this.waitForSummernote(), 100);
                return;
            }
            this.initSummernote();
        },
        initSummernote() {
            const element = $("#" + this.inputFieldId);

            element.on("summernote.change", () => {
                this.input();
            });

            // Custom File upload button
            const fileButton = function (context) {
                const ui = $.summernote.ui;
                // Create button
                return ui.button({
                    contents: '<i class="fa fa-paperclip" style="color:#007bff"></i>',
                    tooltip: 'Upload File',
                    click: function () {
                        // Create hidden file input
                        const fileInput = document.createElement('input');
                        fileInput.type = 'file';
                        fileInput.accept = '.pdf,.doc,.docx,.xls,.xlsx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                        fileInput.style.display = 'none';
                        document.body.appendChild(fileInput);
                        fileInput.click();
                        fileInput.onchange = function (event) {
                            const file = event.target.files[0];
                            if (file) {
                                // Simulate upload: create a local URL (replace with real upload logic)
                                const url = URL.createObjectURL(file);
                                // Insert link to file
                                element.summernote('editor.insertNode', (function() {
                                    const a = document.createElement('a');
                                    a.href = url;
                                    a.target = '_blank';
                                    a.textContent = file.name;
                                    a.style.color = '#007bff';
                                    a.style.textDecoration = 'underline';
                                    return a;
                                })());
                            } else {
                                alert('Please select a file.');
                            }
                            document.body.removeChild(fileInput);
                        };
                    }
                }).render();
            };

            let config = {
                placeholder: this.data.placeholder,
                dialogsInBody: true,
                height: this.data.height || 200,
                callbacks: {
                    onChange: (contents) => {
                        this.fieldValue = contents;
                        this.$emit('input', contents);
                    }
                },
                toolbar: [
                    ['style', ['bold', 'italic', 'underline']],
                    ['fontsize', ['fontsize', 'height', 'color']],
                    ['para', ['ul', 'ol']],
                    ['insert', ['picture', 'link', 'video']],
                    ['custom', ['fileUpload']]
                ],
                buttons: {
                    fileUpload: fileButton
                }
            };

            if (Object.keys(this.data.textEditorHints || {}).length) {
                config.hint = this.data.textEditorHints;
            }

            if (this.data.minimal === true) {
                config.toolbar = [
                    ['style', ['bold', 'italic', 'underline']],
                    ['fontsize', ['fontsize', 'height', 'color']],
                    ['para', ['ul', 'ol']],
                    ['insert', ['picture', 'link', 'video']],
                    ['custom', ['fileUpload']]
                ];
            }

            element.summernote(config);

            if (this.value) {
                this.setInitValue(this.value);
            }
        },
        setInitValue(initialValue) {
            if (initialValue !== undefined && initialValue !== null) {
                $("#" + this.inputFieldId).summernote('code', initialValue);
            }
        },
        input() {
            this.fieldValue = $('#' + this.inputFieldId).summernote('code');
            this.$emit('input', this.fieldValue);
        }
    }
}
</script>
