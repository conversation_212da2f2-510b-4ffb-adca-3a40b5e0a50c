<?php

namespace App\Jobs\Tenant\FastExportTraits;

use App\Helpers\Traits\DateTimeHelper;
use App\Models\Tenant\Employee\Department;

trait DepartmentTrait
{
    use DateTimeHelper;

    // Define Leave generator function
    public function DepartmentDataGenerator(): \Generator
    {
        foreach (
            Department::query()
                ->select(['name', 'department_id', 'manager_id'])
                ->selectSub(function ($query) {
                    $query->select('email')
                        ->from('users')
                        ->whereColumn('users.id', 'departments.manager_id')
                        ->limit(1);
                }, 'user')
                ->selectSub(function ($query) {
                    $query->select('name')
                        ->from('departments as p_departments')
                        ->whereColumn('p_departments.id', 'departments.department_id')
                        ->limit(1);
                }, 'parent')
                ->cursor() as $department) {
            $department->sheet_name = 'department';
            yield $department;
        }
    }


    // Define Leave map function
    public function departmentMap($row): array
    {
        return [
            'Department_name' => $row->name,
            'Department_manager' => $row->user,
            'Parent_department' => $row->parent,
        ];
    }

}