<?php

namespace App\Jobs\Tenant\FastExportTraits;

use App\Helpers\Traits\DateTimeHelper;
use App\Models\Tenant\Leave\Leave;

trait LeaveTrait
{
    use DateTimeHelper;
    // Define Leave generator function
    public function LeaveDataGenerator(): \Generator
    {
        foreach (
            Leave::query()
                ->select(['start_at', 'end_at'])
                ->selectSub(function ($query) {
                    $query->select('name')
                        ->from('statuses')
                        ->whereColumn('statuses.id', 'leaves.status_id')
                        ->limit(1);
                }, 'status')
                ->selectSub(function ($query) {
                    $query->select('comment')
                        ->from('comments')
                        ->whereColumn('comments.commentable_id', 'leaves.id')
                        ->where('comments.commentable_type', Leave::class)
                        ->where('comments.type', 'reason-note')
//                        ->orderBy('parent_id', 'DESC')
                        ->limit(1);
                }, 'description')
                ->selectSub(function ($query) {
                    $query->select('email')
                        ->from('users')
                        ->whereColumn('users.id', 'leaves.user_id')
                        ->limit(1);
                }, 'user')
                ->selectSub(function ($query) {
                    $query->select('name')
                        ->from('leave_types')
                        ->whereColumn('leave_types.id', 'leaves.leave_type_id')
                        ->limit(1);
                }, 'leave_type')
                ->cursor() as $leave) {
            $leave->sheet_name = 'leave';
            yield $leave;
        }
    }


    // Define Leave map function
    public function leaveMap($row): array {
        return [
            'Start_date' => $this->getLeaveStartAt($row),
            'End_date' => $this->getLeaveEndAt($row),
            'Status' => __t($row->status),
            'description' => $row->description,
            'User' => $row->user,
            'Leave_type' => $row->leave_type,
        ];
    }

    // mapping helper functions
    private function getLeaveStartAt($leave) {
        if(isset($leave->start_at)) {
            return $this->carbon($leave->start_at)->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
    private function getLeaveEndAt($leave) {
        if(isset($leave->end_at)) {
            return $this->carbon($leave->end_at)->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
}