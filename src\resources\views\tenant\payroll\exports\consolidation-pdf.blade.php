<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Consolidation Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        .report-info {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
        }
        .info-left, .info-right {
            width: 48%;
        }
        .summary-cards {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .summary-card {
            width: 23%;
            border: 1px solid #ddd;
            padding: 15px;
            text-align: center;
            background-color: #f9f9f9;
        }
        .summary-card h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #666;
        }
        .summary-card .value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .total-row {
            background-color: #f9f9f9;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
        .signature-section {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
        }
        .signature-box {
            width: 200px;
            text-align: center;
        }
        .signature-line {
            border-top: 1px solid #333;
            margin-top: 50px;
            padding-top: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">{{ config('app.name') }}</div>
        <div class="report-title">PAYROLL CONSOLIDATION REPORT</div>
        <div style="font-size: 12px; margin-top: 5px;">
            {{ $report->report_type_label }} - {{ $report->period_display }}
        </div>
    </div>

    <div class="report-info">
        <div class="info-left">
            <strong>Generated Date:</strong> {{ $generated_at->format('F d, Y H:i:s') }}<br>
            <strong>Generated By:</strong> {{ $generated_by }}<br>
            <strong>Report Type:</strong> {{ $report->report_type_label }}
        </div>
        <div class="info-right">
            <strong>Period:</strong> {{ $report->period_display }}<br>
            <strong>Total Amount:</strong> {{ $report->formatted_amount }}<br>
            <strong>Status:</strong> {{ $report->status->name ?? 'Unknown' }}
        </div>
    </div>

    <div class="summary-cards">
        <div class="summary-card">
            <h3>Total Employees</h3>
            <div class="value">{{ number_format($report->total_employees) }}</div>
        </div>
        <div class="summary-card">
            <h3>Total Amount</h3>
            <div class="value">{{ $report->formatted_amount }}</div>
        </div>
        <div class="summary-card">
            <h3>Departments</h3>
            <div class="value">{{ number_format($report->total_departments) }}</div>
        </div>
        <div class="summary-card">
            <h3>Avg per Employee</h3>
            <div class="value">
                ₱{{ number_format($report->total_employees > 0 ? $report->total_amount / $report->total_employees : 0, 2) }}
            </div>
        </div>
    </div>

    @if($report->report_type === 'departmental')
        <h3>Department Breakdown</h3>
        <table>
            <thead>
                <tr>
                    <th>Department</th>
                    <th class="text-center">Employees</th>
                    <th class="text-right">Total Gross</th>
                    <th class="text-right">Total Net</th>
                    <th class="text-right">Avg per Employee</th>
                </tr>
            </thead>
            <tbody>
                @php $totalGross = 0; $totalNet = 0; $totalEmployees = 0; @endphp
                @foreach($report->report_data as $dept)
                    <tr>
                        <td>{{ $dept['department'] ?? 'Unassigned' }}</td>
                        <td class="text-center">{{ $dept['employee_count'] ?? 0 }}</td>
                        <td class="text-right">₱{{ number_format($dept['total_gross'] ?? 0, 2) }}</td>
                        <td class="text-right">₱{{ number_format($dept['total_net'] ?? 0, 2) }}</td>
                        <td class="text-right">
                            ₱{{ number_format(($dept['employee_count'] ?? 0) > 0 ? ($dept['total_net'] ?? 0) / ($dept['employee_count'] ?? 1) : 0, 2) }}
                        </td>
                    </tr>
                    @php 
                        $totalGross += $dept['total_gross'] ?? 0;
                        $totalNet += $dept['total_net'] ?? 0;
                        $totalEmployees += $dept['employee_count'] ?? 0;
                    @endphp
                @endforeach
                <tr class="total-row">
                    <td><strong>TOTAL</strong></td>
                    <td class="text-center"><strong>{{ number_format($totalEmployees) }}</strong></td>
                    <td class="text-right"><strong>₱{{ number_format($totalGross, 2) }}</strong></td>
                    <td class="text-right"><strong>₱{{ number_format($totalNet, 2) }}</strong></td>
                    <td class="text-right"><strong>₱{{ number_format($totalEmployees > 0 ? $totalNet / $totalEmployees : 0, 2) }}</strong></td>
                </tr>
            </tbody>
        </table>
    @elseif($report->report_type === 'detailed')
        <h3>Detailed Employee Report</h3>
        <table>
            <thead>
                <tr>
                    <th>Employee</th>
                    <th>Department</th>
                    <th>Position</th>
                    <th>Period</th>
                    <th class="text-right">Gross</th>
                    <th class="text-right">Net</th>
                    <th class="text-right">Deductions</th>
                </tr>
            </thead>
            <tbody>
                @foreach($report->report_data as $employee)
                    <tr>
                        <td>{{ $employee['employee_name'] ?? 'N/A' }}</td>
                        <td>{{ $employee['department'] ?? 'Unassigned' }}</td>
                        <td>{{ $employee['position'] ?? 'N/A' }}</td>
                        <td>{{ $employee['period'] ?? 'N/A' }}</td>
                        <td class="text-right">₱{{ number_format($employee['gross_salary'] ?? 0, 2) }}</td>
                        <td class="text-right">₱{{ number_format($employee['net_salary'] ?? 0, 2) }}</td>
                        <td class="text-right">₱{{ number_format($employee['deductions'] ?? 0, 2) }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    @endif

    <div class="signature-section">
        <div class="signature-box">
            <div class="signature-line">
                Prepared By
            </div>
        </div>
        <div class="signature-box">
            <div class="signature-line">
                Reviewed By
            </div>
        </div>
        <div class="signature-box">
            <div class="signature-line">
                Approved By
            </div>
        </div>
    </div>

    <div class="footer">
        <p>This is a computer-generated consolidation report. Generated on {{ $generated_at->format('F d, Y \a\t H:i:s') }}</p>
    </div>
</body>
</html>
