<?php

namespace App\Exports;

use App\Helpers\Traits\DateTimeHelper;
use App\Models\Tenant\Attendance\AttendanceDetails;
use App\Models\Tenant\Export\ModuleExport;
use App\Notifications\Tenant\ExportFailedNotification;
use App\Repositories\Core\Status\StatusRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;

class AttendanceModuleExport implements FromQuery, WithMapping,
    ShouldQueue,
//    WithCustomChunkSize,
    WithEvents,
    WithHeadings, WithTitle, ShouldAutoSize
{
    use Exportable, RegistersEventListeners, DateTimeHelper;

    protected int|null $module_export_id;
    public function __construct($module_export_id = null)
    {
        $this->module_export_id = $module_export_id;
    }
//    public function chunkSize(): int {
//        return 5000;
//    }

    public static function afterSheet(AfterSheet $event)
    {
        // Example event listener, you can define additional events if needed
        // update completed_modules attendance
        if($event->getConcernable()?->module_export_id) {
            $module_export = ModuleExport::query()->find($event->getConcernable()->module_export_id);
            if($module_export) {
                $completed_modules = array_values(array_unique(array_merge($module_export->completed_modules, ['attendance'])));
                $module_export->update([
                    'completed_modules' => $completed_modules,
                ]);
            }
        }
         Log::info('attendance: After sheet');
    }
    public function failed(\Throwable $exception): void
    {
        // handle failed export
        if($this->module_export_id) {
            $module_export = ModuleExport::query()->find($this->module_export_id);
            if($module_export) {
                // update status rejected
                $module_export->update([
                    'status_id' => resolve(StatusRepository::class)->exportRejected(),
                    'data' => [
                        'message' => 'Attendance export failed.'
                    ],
                ]);
                // notify user failed
                notify()
                    ->on('export_failed')
                    ->mergeAudiences($module_export->created_by)
                    ->send(ExportFailedNotification::class);
            }
        }
        // notify user failed
        Log::info('Attendance: failed');
    }

    public function query()
    {
        return AttendanceDetails::query()
            ->select([
                'id',
                'in_time',
                'out_time',
                'attendance_id',
                'status_id',
                'review_by',
                'added_by',
            ])
            ->with([
                'user:users.id,email',
                'status:id,name',
                'comments' => fn(MorphMany $morphMany) => $morphMany->orderBy('parent_id', 'DESC')
                    ->select('id', 'commentable_type', 'commentable_id', 'user_id', 'type', 'comment', 'parent_id')
            ]);

    }
    public function map($attendanceDetail): array
    {
        return [
            $this->getIntime($attendanceDetail),
            $this->getOuttime($attendanceDetail),
            $this->getStatus($attendanceDetail),
            $this->getComment($attendanceDetail),
            $this->getUserEmail($attendanceDetail),
        ];
    }

    public function headings(): array
    {
        return [
            'Start_date',
            'End_date',
            'Status',
            'description',
            'User',
        ];
    }

    public function title(): string
    {
        return __t('attendances');
    }

    // mapping functions
    private function getIntime($attendanceDetail) {
        if(isset($attendanceDetail->in_time)) {
            return $this->carbon($attendanceDetail->in_time)->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
    private function getOuttime($attendanceDetail) {
        if(isset($attendanceDetail->out_time)) {
            return $this->carbon($attendanceDetail->out_time)->parse()->format('Y-m-d\TH:i:s.u\Z');
        }
        return '';
    }
    private function getStatus($attendanceDetail): string {
        if(isset($attendanceDetail->status) && isset($attendanceDetail->status->translated_name)) {
            return $attendanceDetail->status->translated_name;
        }
        return '';
    }
    private function getComment(AttendanceDetails $attendanceDetail)
    {
        if(!isset($attendanceDetail->comments)) return '';
        if (!$attendanceDetail->comments->count()) return '';

        if ($attendanceDetail->review_by || $attendanceDetail->added_by) {
            $note = $this->getNote($attendanceDetail->comments, 'manual');
            return $note ? "Reason Note:'$note'" : '';
        }
        $in_note = $this->getNote($attendanceDetail->comments, 'in-note');
        $out_note = $this->getNote($attendanceDetail->comments, 'out-note');

        if ($in_note && $out_note) {
            return "PUNCH-IN:'$in_note' || PUNCH-OUT:'$out_note'";
        }

        if ($in_note) {
            return "PUNCH-IN:'$in_note'";
        }
        if ($out_note) {
            return "PUNCH-OUT:'$out_note'";
        }

        $note = $this->getNote($attendanceDetail->comments, 'request');
        return $note ? "Reason Note:'$note'" : '';
    }

    private function getNote(Collection $comments, $type)
    {
        if (!$comments->count()) return null;

        return optional($comments->where('type', $type)->sortByDesc('parent_id')->first())->comment;
    }
    private function getUserEmail($attendanceDetail): string {
        if(isset($attendanceDetail->user) && isset($attendanceDetail->user->email)) {
            return $attendanceDetail->user->email;
        }
        return '';
    }
}
