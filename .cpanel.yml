---
deployment:
  tasks:
    - export DEPLOY_PATH=/home/<USER>/public_html/demo.corehrm.ph
    - echo "=== Deploying HEAD commit ==="

    # Copy only selected directories
    - /usr/bin/rsync -av src/app/ $DEPLOY_PATH/app/
    - /usr/bin/rsync -av src/config/ $DEPLOY_PATH/config/
    - /usr/bin/rsync -av src/css/ $DEPLOY_PATH/css/
    - /usr/bin/rsync -av src/database/ $DEPLOY_PATH/database/
    - /usr/bin/rsync -av src/resources/ $DEPLOY_PATH/resources/
    - /usr/bin/rsync -av src/routes/ $DEPLOY_PATH/routes/
    - /usr/bin/rsync -av js/ $DEPLOY_PATH/js/
    - /usr/bin/rsync -av css/ $DEPLOY_PATH/css/
    - /usr/bin/rsync -av fonts/ $DEPLOY_PATH/fonts/

    - echo "=== Fixing folder permissions ==="
    - find $DEPLOY_PATH -type d -exec chmod 755 {} \;
    - find $DEPLOY_PATH -type f -exec chmod 644 {} \;

    - echo "=== Deployment finished. Cache cleared. Permissions fixed. ==="
