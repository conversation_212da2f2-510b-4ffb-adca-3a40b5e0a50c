<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Transmittal Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .company-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .company-address {
            font-size: 10px;
            font-style: italic;
            margin-bottom: 20px;
        }
        .department {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .payroll-period {
            font-weight: bold;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .total-row {
            font-weight: bold;
        }
        .signature-section {
            margin-top: 40px;
        }
        .signature-line {
            margin-top: 60px;
            border-bottom: 1px solid #000;
            width: 200px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">{{ strtoupper($companyName ?? 'STA. CATALINA TOLONG MILLING CORPORATION') }}</div>
        <div class="company-address">{{ $companyAddress ?? 'Sitio. Scopong Brgy. Caranoche, Sta. Catalina, Negros Oriental' }}</div>
        <div class="department">{{ strtoupper($transmittal->department ?? 'ALL DEPARTMENTS') }}</div>
    </div>

    <div class="payroll-period">
        PAYROLL COVERED AS OF {{ strtoupper($transmittal->payroll_period ?? 'MAY 26 - JUNE 10, 2024') }}
    </div>

    <table>
        <thead>
            <tr>
                <th>EMPLOYEE NUMBER</th>
                <th>NAME</th>
                <th>DESIGNATION</th>
                <th>TOTAL SALARY</th>
            </tr>
        </thead>
        <tbody>
            @foreach($payslips as $index => $payslip)
            <tr>
                <td class="text-center">
                    {{ $payslip->user->employee_id ?? sprintf('%03d-2024', $payslip->user->id) }}
                </td>
                <td>{{ $payslip->user->full_name }}</td>
                <td>{{ $payslip->user->designation->name ?? 'N/A' }}</td>
                <td class="text-right">{{ number_format($payslip->net_salary, 2) }}</td>
            </tr>
            @endforeach
            <tr class="total-row">
                <td colspan="3" class="text-center">TOTAL</td>
                <td class="text-right">{{ number_format($transmittal->total_amount, 2) }}</td>
            </tr>
        </tbody>
    </table>

    <div class="signature-section">
        <p><strong>Prepared By:</strong></p>
        <div class="signature-line"></div>
        <p><strong>Via {{ $transmittal->prepared_by ?? 'Mae N. Tembreville' }}</strong></p>
        <p>{{ $transmittal->prepared_by_title ?? 'Payroll Analyst' }}</p>
    </div>
</body>
</html>
