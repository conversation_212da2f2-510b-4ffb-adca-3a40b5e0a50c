<?php

namespace App\Models\Tenant;

use App\Models\Core\Auth\User;
use App\Models\Tenant\Plans;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Subscription Model
 *
 * Represents a tenant's subscription in the HRIS system.
 * This model is used to manage subscription details for tenants.
 *
 */
class Subscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'plan_id',
        'start_date',
        'end_date',
        'renewal_date',
        'status',
    ];

    public function tenant() // phpcs:ignore PEAR.Commenting.FunctionComment.Missing
    {
        return $this->belongsTo(User::class);
    }

    public function plan() // phpcs:ignore PEAR.Commenting.FunctionComment.Missing
    {
        return $this->belongsTo(Plans::class);
    }
}
