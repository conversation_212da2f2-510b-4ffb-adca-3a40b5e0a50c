<?php

namespace App\Exports;

use App\Models\Tenant\Export\ModuleExport;
use App\Models\Tenant\WorkingShift\WorkingShift;
use App\Notifications\Tenant\ExportFailedNotification;
use App\Repositories\Core\Status\StatusRepository;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;

class WorkingShiftModuleExport implements
    FromQuery,
    WithMapping,
    ShouldQueue,
//    WithCustomChunkSize,
    WithEvents,
    WithHeadings,
    WithTitle,
    ShouldAutoSize
{
    use Exportable, RegistersEventListeners;

    protected int|null $module_export_id;
    public function __construct($module_export_id = null)
    {
        $this->module_export_id = $module_export_id;
    }

//    public function chunkSize(): int {
//        return 1000;
//    }

    public static function afterSheet(AfterSheet $event)
    {
        // Example event listener, you can define additional events if needed
        // update completed_modules working shift
        if($event->getConcernable()?->module_export_id) {
            $module_export = ModuleExport::query()->find($event->getConcernable()->module_export_id);
            if($module_export) {
                $completed_modules = array_values(array_unique(array_merge($module_export->completed_modules, ['work_shift'])));
                $module_export->update([
                    'completed_modules' => $completed_modules,
                ]);
            }
        }
         Log::info('Working shift: After sheet');
    }
    public function failed(\Throwable $exception): void
    {
        // handle failed export
        // update status rejected
        if($this->module_export_id) {
            $module_export = ModuleExport::query()->find($this->module_export_id);
            if($module_export) {
                $module_export->update([
                    'status_id' => resolve(StatusRepository::class)->exportRejected(),
                    'data' => [
                        'message' => 'Working shift export failed.'
                    ],
                ]);
                // notify user failed
                notify()
                    ->on('export_failed')
                    ->mergeAudiences($module_export->created_by)
                    ->send(ExportFailedNotification::class);
            }
        }
        // notify user failed
        Log::info('working shift: failed');
    }
    public function query()
    {
        return WorkingShift::query()
            ->with([
                'details',
            ]);
    }

    public function map($shift): array
    {
        return [
            $this->getShiftTime($shift, 'mon'),
            $this->getShiftTime($shift, 'tue'),
            $this->getShiftTime($shift, 'wed'),
            $this->getShiftTime($shift, 'thu'),
            $this->getShiftTime($shift, 'fri'),
            $this->getShiftTime($shift, 'sat'),
            $this->getShiftTime($shift, 'sun'),
            $shift->name,
        ];
    }

    public function headings(): array
    {
        return [
            __t('monday'),
            __t('tuesday'),
            __t('wednesday'),
            __t('thursday'),
            __t('friday'),
            __t('saturday'),
            __t('sunday'),
            __t('name'),
        ];
    }

    public function title(): string
    {
        return __t('working_shifts');
    }

    // mapping functions
    private function getShiftTime($shift, $day): string
    {
        if(!isset($shift->details)) return '';

        $details = $shift->details;
        $start = $details->where('weekday', $day)->first()->start_at;
        $end = $details->where('weekday', $day)->first()->end_at;

        $start = $start ? Carbon::parse($start)->setTimezone(request('timeZone', timezone()))->format('h:i a') : 'x';
        $end = $end ? Carbon::parse($end)->setTimezone(request('timeZone', timezone()))->format('h:i a') : 'x';

        return $start == 'x' ? $start : "$start - $end";
    }
}
